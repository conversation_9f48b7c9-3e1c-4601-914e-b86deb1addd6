"""
Script para generar un archivo Excel de ejemplo para la carga masiva de patrones.
"""

import pandas as pd
import os
from datetime import datetime

def crear_excel_ejemplo():
    """Crea un archivo Excel de ejemplo para la carga masiva de patrones"""
    # Datos de ejemplo
    datos = [
        {
            'Registro Patronal': 'E5352621109',
            'RFC': 'EME180502I89',
            'Razon Social': 'EMPRESA MUESTRA',
            'Actividad Económica': 'SERVICIOS GENERALES DE ADMIN PUBLICA',
            'Domicilio': 'DOMICILIO 180, COL LA QUE SEA',
            'Municipio': 'ACATLAN DE PEREZ FIGUEROA OAX',
            'Código Postal': '70000',
            'Entidad Federativa': 'OAXACA',
            'Teléfono': '0000000000',
            'Rembolso de Subsidios': False,
            'Zona Salario': 'A',
            'Subdelegacion': 'SANTA MARIA HUATULCO',
            'Fecha Prima de RT': datetime(2022, 11, 1),
            'Representante Legal': 'EL REPRESENTANTE',
            'Clase': 'I',
            'Fracción': '043',
            'STyPS': 'No',
            'Factor Prima de RT': 0.58980
        },
        {
            'Registro Patronal': 'S5110723100',
            'RFC': 'CIN181109UK1',
            'Razon Social': 'CIVIL E INSTALACIONES NSS SAS DE CV',
            'Actividad Económica': 'SERVICIOS PROFESIONALES JURIDI',
            'Domicilio': 'ALGUN DOMICILIO',
            'Municipio': 'GUADALAJARA',
            'Código Postal': '45540',
            'Entidad Federativa': 'JALISCO',
            'Teléfono': '3332445566',
            'Rembolso de Subsidios': True,
            'Zona Salario': 'A',
            'Subdelegacion': 'LIBERTAD REFORMA',
            'Fecha Prima de RT': datetime(2025, 1, 15),
            'Representante Legal': 'FULANO DE TAL POR CUAL',
            'Clase': 'I',
            'Fracción': '841',
            'STyPS': 'No',
            'Factor Prima de RT': 0.68540
        }
    ]
    
    # Crear DataFrame
    df = pd.DataFrame(datos)
    
    # Guardar a Excel
    nombre_archivo = 'carga_masiva_patrones_ejemplo.xlsx'
    df.to_excel(nombre_archivo, sheet_name='patron', index=False)
    
    print(f"Archivo de ejemplo creado con éxito: {os.path.abspath(nombre_archivo)}")
    return nombre_archivo

if __name__ == "__main__":
    crear_excel_ejemplo() 