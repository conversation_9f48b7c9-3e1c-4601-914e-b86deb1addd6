import pyodbc

def main():
    try:
        # Establecer conexión
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa a la base de datos")
        
        # Consulta para obtener columnas
        cursor = conn.cursor()
        cursor.execute("SELECT TOP 1 * FROM Asegura")
        
        # Imprimir nombres de columnas
        print("\nNombres de columnas en la tabla Asegura:")
        total_columnas = 0
        for i, column in enumerate(cursor.description):
            total_columnas += 1
            print(f"{i+1}. {column[0]}")
        
        print(f"\nTotal de columnas: {total_columnas}")
        
        # Consulta para verificar los campos específicos
        cursor.execute("SELECT TOP 1 TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc FROM Asegura")
        print("\nDetalle de los campos de crédito:")
        for i, column in enumerate(cursor.description):
            print(f"{i+1}. {column[0]} (tipo: {column[1]}, tamaño: {column[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 