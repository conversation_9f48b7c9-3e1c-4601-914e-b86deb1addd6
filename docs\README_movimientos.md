# Carga Masiva de Movimientos SUA

## Descripción General

Esta herramienta permite cargar masivamente movimientos al Sistema Único de Autodeterminación (SUA) desde un archivo Excel. Está diseñada para procesar diferentes tipos de movimientos (bajas, modificaciones salariales, reingresos, movimientos de crédito Infonavit, etc.) y actualizar correctamente tanto la tabla de movimientos (Movtos) como la de asegurados (Asegura).

## Tipos de Movimientos Soportados

| Código | Descripción | Tablas Afectadas | Campos Especiales |
|--------|-------------|------------------|-------------------|
| 02 | Baja | Asegura + Movtos | TIP_INC (contador secuencial de bajas) |
| 07 | Modificación de Salario | Movtos | SAL_MOVT (nuevo salario) |
| 08 | Reingreso | Asegura + Movtos | - |
| 09 | Aportación Voluntaria | Movtos | - |
| 11 | Ausentismo | Movtos | - |
| 12 | Incapacidad | Movtos | - |
| 15 | Inicio de Crédito de Vivienda (ICV) | Asegura + Movtos | Tip_Des, Val_Des, Num_Cre |
| 16 | Fecha de Suspensión de Descuento (FS) | Movtos | - |
| 17 | Reinicio de Descuento (RD) | Movtos | - |
| 18 | Modificación de Tipo de Descuento (MTD) | Movtos | Tip_Des |
| 19 | Modificación de Valor de Descuento (MVD) | Movtos | Val_Des |
| 20 | Modificación de Número de Crédito (MND) | Movtos | Num_Cre |

**Nota**: Los movimientos tipo 01 (Altas) deben procesarse con la herramienta específica de carga de asegurados.

## Estructura de la Base de Datos

### Tabla Movtos

La tabla Movtos almacena todos los movimientos asociados a los asegurados. Cada tipo de movimiento utiliza diferentes campos:

- **REG_PATR**: Registro patronal
- **NUM_AFIL**: Número de afiliación (NSS)
- **TIP_MOVS**: Tipo de movimiento (01, 02, 07, etc.)
- **FEC_INIC**: Fecha del movimiento
- **CVE_MOVS**: Clave del movimiento (varía según el tipo)
- **SAL_MOVT**: Salario (para movimientos tipo 01, 07, 08)
- **EDO_MOV**: Estado del movimiento
- **TIP_INC**: Para bajas, lleva control del número secuencial de bajas
- **Num_Cre**: Número de crédito Infonavit
- **Val_Des**: Valor del descuento
- **Tip_Des**: Tipo de descuento (Porcentaje, Cuota Fija, Factor de Descuento)

### Tabla Asegura

La tabla Asegura contiene la información de los trabajadores asegurados:

- **REG_PATR**: Registro patronal
- **NUM_AFIL**: Número de afiliación (NSS)
- **FEC_ALT**: Fecha de alta
- **FEC_BAJ**: Fecha de baja (se actualiza al procesar una baja)
- **SAL_IMSS**: Salario registrado ante el IMSS
- **TIP_DSC**: Tipo de descuento Infonavit
- **VAL_DSC**: Valor del descuento Infonavit
- **FEC_DSC**: Fecha de inicio del descuento Infonavit
- **Fec_FinDsc**: Fecha de fin del descuento Infonavit
- **Num_Cre**: Número de crédito Infonavit

## Funcionamiento de la Herramienta

### Carga del Archivo Excel

El archivo Excel debe contener las siguientes columnas:
- **REG_PATR**: Registro patronal
- **NUM_AFIL**: Número de afiliación
- **NOMBRE**: Nombre del trabajador (referencial, no se procesa)
- **FECHA_MOVIMIENTO**: Fecha del movimiento
- **TIPO_MOVIMIENTO**: Código del tipo de movimiento
- **SALARIO**: Salario (para movimientos tipo 07 y 08)
- **TIPO_DESCUENTO**: Tipo de descuento Infonavit (para movimientos tipo 15, 18, 19)
- **VALOR_DESCUENTO**: Valor del descuento (para movimientos tipo 15, 18, 19)

### Procesamiento Específico por Tipo de Movimiento

#### Bajas (02)
1. Actualiza FEC_BAJ en Asegura
2. Si el trabajador tiene crédito Infonavit, actualiza Fec_FinDsc
3. Genera un movimiento tipo 16 para finalizar el crédito Infonavit si existe
4. Asigna un número secuencial en TIP_INC (contador de bajas)

#### Reingreso (08)
1. Establece FEC_BAJ a NULL en Asegura (elimina la fecha de baja)
2. Registra el movimiento en Movtos con el salario especificado

#### Inicio de Crédito Infonavit (15)
1. Actualiza TIP_DSC, VAL_DSC y FEC_DSC en Asegura
2. Genera un número de crédito si no se proporciona
3. Registra el movimiento en Movtos

#### Modificación Salarial (07)
1. Registra el nuevo salario en SAL_MOVT
2. Actualiza únicamente la tabla Movtos

#### Suspensión de Crédito Infonavit (16)
1. Verifica que no exista una suspensión previa (FEC_DSC debe ser NULL)
2. Obtiene datos del último movimiento de crédito (tipo 15 o 17)
3. Actualiza FEC_DSC en la tabla Asegura
4. Registra el movimiento en Movtos con CVE_MOVS 'G'
5. Convierte Tip_Des a formato numérico adecuado:
   - "porcentaje" o "1" → 1
   - "cuota fija" o "2" → 2
   - "factor de descuento" o "3" → 3

#### Reinicio de Crédito Infonavit (17)
1. Verifica que exista una suspensión previa (FEC_DSC no debe ser NULL)
2. Obtiene datos del último movimiento de suspensión (tipo 16) o inicio (tipo 15)
3. Elimina FEC_DSC en la tabla Asegura (establece a NULL)
4. Registra el movimiento en Movtos con CVE_MOVS 'H'
5. Convierte Tip_Des a formato numérico adecuado:
   - "porcentaje" o "1" → 1
   - "cuota fija" o "2" → 2
   - "factor de descuento" o "3" → 3

### Funcionalidades Adicionales

#### Análisis de Movimientos
La herramienta incluye una función para analizar los movimientos existentes en la base de datos, mostrando ejemplos reales para comprender mejor la estructura de cada tipo de movimiento.

#### Generación de Plantilla
Se puede generar una plantilla Excel con ejemplos de cada tipo de movimiento y hojas de instrucciones detalladas.

#### Validación Inteligente
La herramienta valida los campos obligatorios según el tipo de movimiento y verifica el formato y contenido de los datos.

## Consideraciones Importantes

1. **Validación de Existencia**: Antes de procesar cualquier movimiento, se verifica que el trabajador exista en la tabla Asegura.

2. **Transacciones**: Las operaciones se realizan dentro de transacciones para garantizar la integridad de los datos.

3. **Conteo de Bajas**: El sistema mantiene un control automático del número de bajas de cada trabajador.

4. **Reingresos**: Solo se pueden procesar reingresos si el trabajador tiene una fecha de baja registrada.

5. **Créditos Infonavit**: Para movimientos relacionados con créditos Infonavit, se verifica que el trabajador no tenga fecha de baja.

## Implementación Técnica

La herramienta está implementada en Python utilizando:
- **Tkinter** para la interfaz gráfica
- **Pandas** para el procesamiento de datos
- **PyODBC** para la conexión con la base de datos Access

Los métodos principales para el procesamiento de movimientos están encapsulados en la clase `ConectorSUA`:
- `procesar_baja(datos_mov)`: Procesa bajas (02)
- `procesar_reingreso(datos_mov)`: Procesa reingresos (08)
- `procesar_credito_infonavit(datos_mov)`: Procesa inicios de crédito Infonavit (15)
- `procesar_suspension_credito(datos_mov)`: Procesa suspensiones de crédito (16)
- `procesar_reinicio_credito(datos_mov)`: Procesa reinicios de crédito (17)
- `insertar_movimiento(datos_mov)`: Método genérico para insertar otros tipos de movimientos

## Uso de la Herramienta

1. **Generar Plantilla**: Utilice el botón "Generar Plantilla" para obtener un archivo Excel con la estructura correcta.

2. **Preparar Archivo**: Llene el archivo Excel con los movimientos a cargar, respetando los campos obligatorios según el tipo.

3. **Validar**: Utilice el botón "Validar Excel" para verificar que el archivo esté correcto antes de procesar.

4. **Cargar**: Utilice el botón "Cargar Movimientos" para procesar los movimientos.

5. **Analizar**: Si necesita entender mejor la estructura de los movimientos, utilice "Analizar Movimientos".

## Mensajes de Error Comunes

- **"No se encontró el asegurado"**: El número de afiliación o registro patronal no existe en Asegura.
- **"El asegurado tiene fecha de baja registrada"**: No se puede registrar un crédito para un trabajador dado de baja.
- **"El asegurado no tiene fecha de baja registrada"**: No se puede procesar un reingreso para un trabajador que no está dado de baja.
- **"Para movimientos tipo X se requiere Y"**: Faltan campos obligatorios para el tipo de movimiento.

## Mejoras Realizadas

### Gestión de Créditos Infonavit

Se ha mejorado la gestión de créditos Infonavit para los siguientes tipos de movimientos:

#### Suspensión de Crédito (16)
- Se corrigió la transformación del campo `Tip_Des` para asegurar la correcta conversión entre formatos de texto y numérico.
- Se implementó una validación completa para verificar la existencia de una fecha de suspensión previa.
- Se mejoró la consulta para obtener el último movimiento de crédito del tipo 15 (inicio) o 17 (reinicio).
- Se realiza una verificación temporal para asegurar que la fecha de suspensión no sea anterior a la fecha del último movimiento de crédito.

#### Reinicio de Crédito (17)
- Se corrigió la transformación del campo `Tip_Des` para garantizar la conversión del formato de texto a numérico en el mismo patrón que el método `insertar_movimiento`.
- Se implementó una validación estricta para verificar que exista una fecha de suspensión activa antes de procesar el reinicio.
- Se mejoró la lógica para obtener los datos del último movimiento de suspensión (tipo 16) o inicio de crédito (tipo 15).
- Se agregó validación temporal para garantizar que la fecha de reinicio no sea anterior a la fecha de suspensión.

Estas mejoras aseguran un manejo coherente de los formatos de datos entre todas las tablas relacionadas con créditos Infonavit, evitando inconsistencias en el procesamiento de los movimientos tipo 16 y 17.

## Desarrollo Futuro

La herramienta actualmente soporta completamente los tipos de movimiento 02 (Baja), 07 (Modificación de Salario), 08 (Reingreso), 15 (Inicio de Crédito Infonavit), 16 (Suspensión de crédito) y 17 (Reinicio de crédito). Las siguientes mejoras están planificadas para futuras versiones:

1. **Implementación prioritaria de otros movimientos relacionados con Crédito Infonavit**:
   - Tipo 18: Modificación de Tipo de Descuento (MTD)
   - Tipo 19: Modificación de Valor de Descuento (MVD)
   - Tipo 20: Modificación de Número de Crédito (MND)

2. **Implementación de otros tipos de movimientos**:
   - Tipo 09: Aportación Voluntaria
   - Tipo 12: Incapacidad

3. **Mejoras técnicas**:
   - Optimizar el rendimiento para grandes volúmenes de movimientos
   - Mejorar la validación de fechas y periodos para asegurar coherencia
   - Implementar exportación de resultados detallados y reportes
   - Agregar funcionalidad para procesar archivos con múltiples patrones de forma más eficiente
   - Integración con sistemas externos y automatización de carga periódica

4. **Mejoras de interfaz**:
   - Vista previa de registros antes de procesar
   - Historial de cargas realizadas
   - Panel de estadísticas y métricas de carga 

## Mejoras Recientes

### Manejo de Fechas
- Se implementó validación estricta del formato DD/MM/AAAA para todas las fechas
- Se mejoraron los mensajes de error para fechas inválidas
- Se eliminaron conversiones redundantes de fechas
- Se agregó validación de fechas futuras y fechas anteriores a la fecha de alta

### Procesamiento de Ausencias
- Se agregó validación para asegurar que el total de días de ausencia no exceda 7 días por mes
- Se mejoró el manejo de la variable CON_SEC para tratarla como texto
- Se implementaron mensajes de error más descriptivos para casos de ausencias inválidas 