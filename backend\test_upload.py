import requests
import pandas as pd

# Primero, veamos qué contiene el archivo
print("Contenido del archivo Excel:")
try:
    df = pd.read_excel('Plantilla_Patrones_20250425.xlsx', sheet_name='patron')
    print("\nColumnas encontradas:")
    print(df.columns.tolist())
    print("\nPrimeras filas:")
    print(df.head())
except Exception as e:
    print(f"Error al leer el Excel: {e}")

# Ahora intentamos la carga
print("\nIntentando carga al servidor...")
url = "http://localhost:8000/api/v1/patrones/carga-masiva"
files = {'file': open('Plantilla_Patrones_20250425.xlsx', 'rb')}

response = requests.post(url, files=files)
print(f"\nStatus Code: {response.status_code}")
print(f"Response Headers: {dict(response.headers)}")
print(f"Response Text: {response.text}")
try:
    print(f"Response JSON: {response.json()}")
except Exception as e:
    print(f"Error parsing JSON: {e}") 