# Carga Masiva de Patrones para SUA

Este módulo permite realizar la carga masiva de patrones y sus primas RT en la base de datos SUA. Está diseñado para facilitar la importación de múltiples registros desde un archivo Excel.

# Carga Masiva de Asegurados con Infonavit

Esta funcionalidad permite importar asegurados con soporte para créditos Infonavit directamente a la base de datos SUA desde un archivo Excel. El sistema maneja correctamente las distintas tablas involucradas y el formato específico de cada campo.

## Características Principales
- Carga masiva de asegurados a partir de Excel
- Soporte para créditos Infonavit con sus tipos de descuento
- Validación previa de datos para evitar inconsistencias
- Manejo diferenciado para tipos de descuento en cada tabla
- Reportes detallados del proceso y errores
- Interfaz gráfica intuitiva

## Estructura del Excel de Carga de Asegurados

El archivo Excel debe tener una hoja llamada `Asegurados` con las siguientes columnas:

| Columna | Descripción | Ejemplo |
|---------|-------------|---------|
| Registro Patronal | Registro patronal existente | R1265455102 |
| Número de Seguridad Social | NSS del trabajador (11 dígitos) | 03178452540 |
| RFC | RFC del asegurado | AEPL920101XY1 |
| CURP | CURP del asegurado | AEPL920101HJCSTR05 |
| Primer apellido | Apellido paterno | LOPEZ |
| Segundo apellido | Apellido materno | PEREZ |
| Nombre(s) | Nombre(s) completo | JUAN CARLOS |
| Tipo de trabajador | Categoría | Permanente |
| Tipo de salario | Tipo de salario | Fijo |
| Tipo de jornada | Jornada laboral | Jornada Completa |
| Fecha de alta | Fecha de ingreso | 2025-01-01 |
| Salario diario integrado | Salario en pesos | 441.22 |
| Numero Credito Infonavit | Número de crédito | 1422304943 |
| Tipo Descuento | Tipo de descuento | Cuota Fija |
| Valor Descuento | Valor del descuento | 3197.23 |

## Procesamiento de Tipos de Descuento

El sistema maneja automáticamente la conversión entre los diferentes formatos requeridos por las tablas:

| Valor en Excel | Tabla Asegura (TIP_DSC) | Tabla Movtos (Tip_Des) |
|----------------|-------------------------|------------------------|
| "Porcentaje" | 1 | "Porcentaje" |
| "Cuota Fija" | 2 | "Cuota Fija" |
| "Factor de Descuento" | 3 | "Factor de Descuento" |

### Ejemplo de Conversión
```python
# Para Asegura: Convertir texto a número
if tipo_descuento_texto == "Cuota Fija":
    tip_dsc = 2
elif tipo_descuento_texto == "Porcentaje":
    tip_dsc = 1
elif tipo_descuento_texto == "Factor de Descuento":
    tip_dsc = 3

# Para Movtos: Mantener el texto original
tip_des = tipo_descuento_texto  # Ej: "Cuota Fija"
```

## Tablas Afectadas por la Carga

La importación inserta registros en las siguientes tablas:
- **Asegura**: Datos principales del asegurado
- **Afiliacion**: Datos complementarios
- **Trabs**: Tabla de relación trabajador-patrón
- **Movtos**: Movimientos (alta tipo 01 y crédito Infonavit tipo 15)

## Solución a Problemas Comunes

### 1. Error en Tipo de Descuento
El tipo de descuento tiene formato diferente entre tablas. El problema se resuelve convirtiendo:
- Para la tabla Asegura: Se convierte a valor numérico (1, 2, 3)
- Para la tabla Movtos: Se mantiene como texto ("Porcentaje", "Cuota Fija", etc.)

### 2. Manejo de Errores
El sistema utiliza un enfoque de múltiples intentos para garantizar la inserción:
```python
try:
    # Intento principal con parámetros estándar
    cursor.execute(sql, parametros)
except Exception:
    # Intentos alternativos con diferentes formatos
    try:
        cursor.execute(sql_alternativo)
    except Exception:
        # Último intento con fallback
        cursor.execute(sql_fallback)
```

### 3. Validación Previa
Se realizan validaciones exhaustivas antes de intentar insertar:
- Verificación de existencia del registro patronal
- Validación de formato de NSS (11 dígitos)
- Verificación de valores requeridos
- Conversión de tipos de datos

## Uso del Script de Corrección

Se incluye un script `corregir_asegurado.py` que permite solucionar problemas específicos:
1. Identifica registros con problemas de inserción
2. Obtiene los datos correctos de las tablas relacionadas
3. Realiza la inserción manual con el formato adecuado
4. Verifica que la corrección haya sido exitosa

## Funcionalidades

- Validación previa de registros patronales para evitar duplicados
- Carga de datos básicos del patrón
- Asignación automática de primas RT
- Interfaz gráfica para facilitar la operación
- Generación de reportes de errores

## Requisitos

- Base de datos SUA (Access MDB)
- Python 3.8 o superior
- Librerías requeridas:
  - pandas
  - pyodbc (para conexión a bases de datos Access)
  - tkinter (para la interfaz gráfica)

## Estructura del Excel de carga

El archivo Excel debe tener una hoja llamada `patron` con las siguientes columnas:

| Columna | Descripción | Ejemplo |
|---------|-------------|---------|
| Registro Patronal | Clave del registro patronal (11 caracteres) | A1234567890 |
| RFC | RFC del patrón | ABC123456XYZ |
| Razon Social | Nombre de la empresa | EMPRESA S.A. DE C.V. |
| Actividad Económica | Descripción de la actividad | SERVICIOS PROFESIONALES |
| Domicilio | Dirección completa | CALLE 123 #456 |
| Municipio | Municipio o alcaldía | GUADALAJARA |
| Código Postal | CP (5 dígitos) | 45040 |
| Entidad Federativa | Estado | JALISCO |
| Teléfono | Número telefónico | 3312345678 |
| Rembolso de Subsidios | Valor booleano | TRUE/FALSE |
| Zona Salario | Zona salarial | A |
| Subdelegacion | Subdelegación IMSS | ZAPOPAN |
| Fecha Prima de RT | Fecha de inicio de la Prima RT | 2025-01-01 |
| Representante Legal | Nombre del representante | JUAN PÉREZ LÓPEZ |
| Clase | Clase de riesgo (I-V) | I |
| Fracción | Número de fracción | 43 |
| STyPS | ¿Registrado en STyPS? | Sí/No |
| Factor Prima de RT | Valor de la prima (decimal) | 0.5 |

## Mapeo de Campos SUA a Layout Excel

La aplicación realiza automáticamente el mapeo de campos entre el Excel y la base de datos SUA. A continuación se detalla esta relación:

| Campo SUA | Campo Excel | Procesamiento | Descripción |
|-----------|-------------|---------------|-------------|
| REG_PAT | Registro Patronal | Directo | Clave de registro patronal |
| RFC_PAT | RFC | Directo | RFC del patrón |
| NOM_PAT | Razon Social | Directo | Nombre de la empresa |
| ACT_PAT | Actividad Económica | Directo | Actividad económica del patrón |
| DOM_PAT | Domicilio | Directo | Domicilio completo |
| MUN_PAT | Municipio | Directo | Municipio |
| CPP_PAT | Código Postal | Directo | Código postal |
| ENT_PAT | Entidad Federativa | Búsqueda | Se obtiene el IdClave de la tabla Estados SUA relacionando con el campo Descripcion |
| TEL_PAT | Teléfono | Directo | Teléfono de contacto |
| REM_PAT | Rembolso de Subsidios | Conversión | 1 = Verdadero, 0 = Falso |
| ZON_PAT | Zona Salario | Directo | Zona salarial |
| DEL_PAT | Subdelegacion | Búsqueda | Se obtiene el IdClaveSub de la tabla Subdelega SUA relacionando con el campo Descripcion |
| CAR_ENT | Entidad Federativa | Directo | Texto de la entidad federativa |
| NUM_DEL | - | Fijo = 0 | Valor numérico fijo |
| CAR_DEL | Entidad Federativa | Directo | Texto de la entidad federativa |
| NUM_SUB | Subdelegacion | Búsqueda | Se obtiene la posición numérica de la subdelegación en la tabla Subdelega |
| CAR_SUB | Subdelegacion | Directo | Texto de la subdelegación |
| TIP_CON | - | Fijo = 0 | Valor numérico fijo |
| CON_VEN | - | NULL | Se deja vacío |
| INI_AFIL | Fecha Prima de RT | Formato | Se convierte al formato "AAAAMM" |
| Pat_Rep | Representante Legal | Directo | Nombre del representante legal |
| Clase | Clase | Directo | Clase de riesgo |
| Fraccion | Fracción | Directo | Fracción del riesgo |
| STyPS | STyPS | Directo | Valor Sí/No |

### Ejemplo de Búsqueda de IDs de Entidades y Subdelegaciones

La aplicación automáticamente busca los IDs correspondientes para las entidades federativas y subdelegaciones:

```python
# Buscar ID de entidad federativa
entidad_data = self.buscar_id_entidad(entidad_nombre)
if entidad_data:
    entidad_id = entidad_data['id']
else:
    self.log(f"Error: No se encontró la entidad federativa '{entidad_nombre}'", error=True)
    return False

# Buscar ID de subdelegación
subdelegacion_data = self.buscar_id_subdelegacion(subdelegacion_nombre)
if subdelegacion_data:
    subdelegacion_id = subdelegacion_data['id']
    subdelegacion_posicion = subdelegacion_data['posicion']
else:
    self.log(f"Error: No se encontró la subdelegación '{subdelegacion_nombre}'", error=True)
    return False
```

### Formato de Fecha para INI_AFIL

La fecha de afiliación inicial se formatea específicamente como "AAAAMM":

```python
# Formatear fecha para INI_AFIL (AAAAMM)
fecha_prima = datos.get('Fecha Prima de RT')
if pd.notna(fecha_prima):
    if isinstance(fecha_prima, str):
        fecha_prima = pd.to_datetime(fecha_prima)
    # Formato AAAAMM
    fecha_afil = fecha_prima.strftime("%Y%m")
```

## Solución a Problemas Comunes

### 1. Manejo de Registros Patronales Duplicados

El sistema verifica la existencia de registros patronales antes de intentar insertarlos. Si un registro ya existe, se omite su inserción y se registra en el reporte de errores.

```python
# Verificar si ya existe el registro patronal
cursor = self.conector.conn.cursor()
consulta_verificacion = f"SELECT COUNT(*) FROM Patron WHERE REG_PAT = '{reg_pat}'"
cursor.execute(consulta_verificacion)
count = cursor.fetchone()[0]

if count > 0:
    self.log(f"El registro patronal {reg_pat} ya existe en la base de datos", warning=True)
    return False
```

### 2. Inserción de Primas RT

La inserción de primas RT puede ser problemática debido a incompatibilidades de tipos de datos. Se implementaron múltiples estrategias de inserción, con alternativas de respaldo en caso de fallo:

```python
# Estrategia primaria de inserción
consulta = f"INSERT INTO Prima_RT (Reg_Pat, Ano, Mes, Prima_Rt, ValMes) " + \
           f"VALUES ('{reg_pat}', {año}, '{mes_txt}', {prima_rt}, {mes_num})"

# Si falla, se intenta una estrategia alternativa
consulta_final = f"""
INSERT INTO Prima_RT (Reg_Pat, Ano, Prima_Rt, ValMes) 
VALUES ('{reg_pat}', {año}, {prima_rt}, {mes_num})
"""
# Actualización posterior del campo Mes
update_mes = f"""
UPDATE Prima_RT SET Mes = '{mes_txt}' 
WHERE Reg_Pat = '{reg_pat}' AND Ano = {año} AND ValMes = {mes_num}
"""
```

### 3. Validación de Tipos de Datos

Los diferentes tipos de datos entre Python y Access pueden causar problemas. Se implementaron conversiones explícitas para asegurar la compatibilidad:

- Enteros: `int(valor)`
- Flotantes: `float(valor)`
- Cadenas: `str(valor).strip()`
- Booleanos: `1 if valor else 0`

### 4. Manejo de Errores

El sistema registra todos los errores durante el proceso de carga y genera un archivo detallado para facilitar la corrección de problemas:

```
ERRORES EN CARGA MASIVA DE PATRONES
Fecha: 2025-04-21 16:33:20
Archivo: C:/Users/<USER>/path/to/excel.xlsx

Fila 2: El registro patronal E5352621109 ya existe
Fila 3: El registro patronal S5110723100 ya existe
```

## Uso del Script de Prueba

Se incluye un script `probar_carga_patrones.py` que permite verificar el funcionamiento de la carga masiva sin necesidad de usar la interfaz gráfica. Este script:

1. Genera registros patronales aleatorios para evitar duplicados
2. Crea un archivo Excel de prueba con esos registros
3. Realiza la inserción en la base de datos
4. Registra los resultados del proceso

## Consideraciones para Access

La interacción con bases de datos Access requiere consideraciones especiales:

- Evitar el uso de parámetros en las consultas SQL (`?`) que pueden causar errores
- Usar comillas simples para valores de texto
- Manejar correctamente los tipos de datos en las columnas
- Escapar comillas dentro de los valores de texto

## Mejoras Futuras

- Soporte para carga desde archivos CSV
- Validación más exhaustiva de los datos de entrada
- Opciones avanzadas de configuración
- Integración con el sistema principal de manejo de SUA

### Manejo de Relaciones entre Tablas

El sistema maneja automáticamente las relaciones entre tablas, buscando los IDs correspondientes en las tablas relacionadas:

1. Para la entidad federativa (ENT_PAT), se busca el IdClave en la tabla Estados:
```python
# Buscar ID de entidad federativa
entidad_data = self.buscar_id_entidad(entidad_nombre)
entidad_id = entidad_data['id']
```

2. Para la subdelegación (DEL_PAT), se busca el IdClaveSub en la tabla Subdelega:
```python
# Buscar ID de subdelegación
subdelegacion_data = self.buscar_id_subdelegacion(subdelegacion_nombre)
subdelegacion_id = subdelegacion_data['id']
subdelegacion_posicion = subdelegacion_data['posicion']
```

### Manejo de Valores por Defecto

Si no se encuentran los valores en las tablas relacionadas, el sistema utiliza valores por defecto para asegurar que el proceso pueda continuar:

```python
# Valor por defecto para entidades federativas
return {
    'id': 20,  # ID por defecto (Jalisco generalmente)
    'nombre': nombre_entidad,
    'posicion': 1
}

# Valor por defecto para subdelegaciones
return {
    'id': 2153,  # ID por defecto
    'nombre': nombre_subdelegacion,
    'posicion': 3
}
```

### Formato Especial para INI_AFIL

El campo INI_AFIL (fecha de afiliación inicial) utiliza un formato específico "AAAAMM" que se genera a partir de la fecha de prima RT:

```python
# Formatear fecha para INI_AFIL (AAAAMM)
fecha_afil = ""
fecha_prima = datos.get('Fecha Prima de RT')
if pd.notna(fecha_prima):
    if isinstance(fecha_prima, str):
        fecha_prima = pd.to_datetime(fecha_prima)
    # Formato AAAAMM
    fecha_afil = fecha_prima.strftime("%Y%m")
```

### Script de Prueba con Valores Aleatorios

El script `probar_carga_patrones.py` incluye funcionalidad para generar registros patronales aleatorios, evitando así problemas con duplicados:

```python
# Función para generar un registro patronal aleatorio
def generar_registro_patronal():
    letra = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
    numeros = ''.join(random.choices(string.digits, k=10))
    return f"{letra}{numeros}"

# Usar timestamp para asegurar que sean únicos
timestamp = int(time.time())
reg1 = generar_registro_patronal() + str(timestamp)[-3:]
reg2 = generar_registro_patronal() + str(timestamp)[-3:]
reg3 = generar_registro_patronal() + str(timestamp)[-3:]

# Asegurarse de que tengan sólo 11 caracteres (formato requerido)
reg1 = reg1[:11]
reg2 = reg2[:11]
reg3 = reg3[:11]
```

## Validaciones de Ausencias

### Límites de Ausencias
- Se implementó un límite de 7 días de ausencia por mes por trabajador
- El sistema valida tanto los días existentes en la base de datos como los nuevos días a registrar
- Se muestra un mensaje de error cuando se intenta exceder el límite permitido

### Validaciones en Dos Niveles
1. **Validación en Excel**:
   - Se verifica que el total de días de ausencia en el archivo no exceda 7 días por mes
   - Se agrupan los registros por trabajador y mes para validar el total

2. **Validación en Base de Datos**:
   - Se verifica el total de días de ausencia existentes en la base de datos
   - Se suma el total de días existentes con los nuevos días a registrar
   - Se rechaza la operación si el total excede 7 días

### Mensajes de Error
- "El total de días de ausencia excede el límite de 7 días por mes"
- "Ya existen X días de ausencia registrados para este mes"
- "No se pueden registrar Y días adicionales ya que excedería el límite" 