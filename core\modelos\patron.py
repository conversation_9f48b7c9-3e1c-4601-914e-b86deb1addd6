"""
Modelo para la tabla Patron del SUA.
Define las estructuras de datos y las operaciones relacionadas con la tabla Patron.
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
import pandas as pd
import datetime

@dataclass
class Patron:
    """
    Clase que representa un registro de la tabla Patron.
    Los nombres de atributos y tipos son aproximados y deben ajustarse 
    según la estructura real de la base de datos.
    """
    # Campos aproximados, ajustar según exploración
    registro_patronal: str
    nombre: str
    direccion: Optional[str] = None
    actividad: Optional[str] = None
    rfc: Optional[str] = None
    delegacion: Optional[str] = None
    subdelegacion: Optional[str] = None
    clase_riesgo: Optional[str] = None
    fecha_alta: Optional[datetime.date] = None
    # Añadir los demás campos según la exploración
    
    # Campos adicionales para control interno
    _modificado: bool = field(default=False, repr=False)
    _metadatos: Dict[str, Any] = field(default_factory=dict, repr=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Patron':
        """
        Crea una instancia de Patron a partir de un diccionario.
        
        Args:
            data: Diccionario con los datos del patrón.
            
        Returns:
            Patron: Instancia de Patron con los datos proporcionados.
        """
        # Filtrar solo las claves que son atributos de la clase
        campos_validos = {k: v for k, v in data.items() 
                         if k in cls.__annotations__ and not k.startswith('_')}
        
        # Convertir fechas si es necesario
        if 'fecha_alta' in campos_validos and campos_validos['fecha_alta'] and not isinstance(campos_validos['fecha_alta'], datetime.date):
            try:
                campos_validos['fecha_alta'] = pd.to_datetime(campos_validos['fecha_alta']).date()
            except:
                campos_validos['fecha_alta'] = None
        
        return cls(**campos_validos)
    
    @classmethod
    def from_dataframe(cls, df: pd.DataFrame) -> List['Patron']:
        """
        Crea una lista de instancias de Patron a partir de un DataFrame.
        
        Args:
            df: DataFrame con datos de patrones.
            
        Returns:
            List[Patron]: Lista de instancias de Patron.
        """
        return [cls.from_dict(row) for _, row in df.iterrows()]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convierte la instancia a un diccionario.
        
        Returns:
            Dict[str, Any]: Diccionario con los atributos del patrón.
        """
        # Excluir campos internos que comienzan con _
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    def marcar_como_modificado(self) -> None:
        """Marca el registro como modificado para seguimiento de cambios"""
        self._modificado = True
    
    def esta_modificado(self) -> bool:
        """Verifica si el registro ha sido modificado"""
        return self._modificado
    
    def agregar_metadato(self, clave: str, valor: Any) -> None:
        """Agrega un metadato al registro"""
        self._metadatos[clave] = valor
    
    def obtener_metadato(self, clave: str, default: Any = None) -> Any:
        """Obtiene un metadato del registro"""
        return self._metadatos.get(clave, default)


class PatronRepository:
    """
    Clase para gestionar operaciones de acceso a datos de la tabla Patron.
    Implementa el patrón Repository para separar la lógica de acceso a datos.
    """
    
    def __init__(self, conector):
        """
        Inicializa el repositorio.
        
        Args:
            conector: Instancia de ConectorSUA para acceder a la base de datos.
        """
        self.conector = conector
        self.tabla = "Patron"
    
    def obtener_todos(self, limite: int = 1000) -> List[Patron]:
        """
        Obtiene todos los registros de patrones.
        
        Args:
            limite: Número máximo de registros a obtener.
            
        Returns:
            List[Patron]: Lista de patrones.
        """
        df = self.conector.obtener_datos_tabla(self.tabla, limite=limite)
        return Patron.from_dataframe(df) if not df.empty else []
    
    def obtener_por_registro_patronal(self, registro_patronal: str) -> Optional[Patron]:
        """
        Obtiene un patrón por su registro patronal.
        
        Args:
            registro_patronal: Registro patronal a buscar.
            
        Returns:
            Patron: Patrón encontrado, o None si no existe.
        """
        # Escapar posibles comillas en el registro patronal
        reg_pat_escaped = registro_patronal.replace("'", "''")
        
        # Consulta directa sin parámetros
        query = f"SELECT TOP 1 * FROM [Patron] WHERE registro_patronal = '{reg_pat_escaped}'"
        
        resultado = self.conector.ejecutar_consulta(query)
        
        if not resultado:
            return None
        
        # Crear objeto Patron a partir de los datos obtenidos
        patron = Patron(**resultado[0])
        return patron
    
    def buscar(self, **criterios) -> List[Patron]:
        """
        Busca patrones según criterios específicos.
        
        Args:
            **criterios: Pares clave-valor para búsqueda (campo=valor).
            
        Returns:
            List[Patron]: Lista de patrones que cumplen los criterios.
        """
        # Construir condición WHERE
        condiciones = []
        for campo, valor in criterios.items():
            if isinstance(valor, str):
                condiciones.append(f"{campo} LIKE '%{valor}%'")
            else:
                condiciones.append(f"{campo} = {valor}")
        
        where = " AND ".join(condiciones) if condiciones else None
        
        df = self.conector.obtener_datos_tabla(self.tabla, where=where)
        return Patron.from_dataframe(df) if not df.empty else []
    
    def contar(self) -> int:
        """
        Cuenta el número total de patrones.
        
        Returns:
            int: Número de patrones.
        """
        # Usar una consulta COUNT directa para optimizar
        df = self.conector.ejecutar_consulta(f"SELECT COUNT(*) AS total FROM [{self.tabla}]")
        return int(df.iloc[0]['total']) if not df.empty else 0 