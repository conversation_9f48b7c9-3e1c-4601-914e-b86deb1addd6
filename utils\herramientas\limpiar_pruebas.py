"""
Script para limpiar los registros de prueba generados durante las pruebas de suspensión y reinicio de crédito.
"""

from conectores.conector_sua import ConectorSUA

def main():
    # Conectar a la base de datos
    conector = ConectorSUA(password="S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    # Datos de prueba
    reg_patr = "E5352621109"
    num_afil = "04088625456"
    
    try:
        # 1. Eliminar movimientos de suspensión y reinicio
        cursor = conector.conn.cursor()
        
        query = f"""
        DELETE FROM Movtos 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' 
        AND TIP_MOVS IN ('16', '17')
        """
        cursor.execute(query)
        
        # 2. Resetear el campo FEC_DSC en la tabla Asegura
        query = f"""
        UPDATE Asegura 
        SET FEC_DSC = NULL 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        """
        cursor.execute(query)
        
        # Confirmar transacción
        conector.conn.commit()
        print(f"✅ Registros de prueba limpiados para el asegurado {num_afil}")
        
        # Verificar estado final
        query = f"""
        SELECT TIP_DSC, VAL_DSC, FEC_DSC 
        FROM Asegura 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        """
        cursor.execute(query)
        row = cursor.fetchone()
        
        if row:
            print("\nEstado final del asegurado:")
            print(f"TIP_DSC: {row[0]}")
            print(f"VAL_DSC: {row[1]}")
            print(f"FEC_DSC: {row[2] or 'NULL'}")
        
    except Exception as e:
        print(f"Error al limpiar registros: {str(e)}")
        try:
            conector.conn.rollback()
        except:
            pass
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 