"""
Script para probar la carga de movimientos de suspensión y reinicio de créditos.
"""

import pandas as pd
import sys
import logging
from conectores.conector_sua import ConectorSUA

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def procesar_archivo(archivo_excel="test_bajas_reingresos.xlsx"):
    print(f"Procesando archivo: {archivo_excel}")
    
    try:
        # Leer el archivo Excel
        df = pd.read_excel(
            archivo_excel,
            dtype={
                'REG_PATR': str,
                'NUM_AFIL': str,
                'TIPO_MOVIMIENTO': str,
                'NUMERO_CREDITO': str
            }
        )
        
        print(f"Registros encontrados: {len(df)}")
        
        # Conectar a la base de datos
        conector = ConectorSUA(password="S5@N52V49")
        conector.conectar()
        
        # Procesar cada registro
        exitos = 0
        errores = 0
        
        for idx, row in df.iterrows():
            print(f"\nProcesando registro {idx+1}/{len(df)}:")
            print(f"  - REG_PATR: {row['REG_PATR']}")
            print(f"  - NUM_AFIL: {row['NUM_AFIL']}")
            print(f"  - TIPO_MOVIMIENTO: {row['TIPO_MOVIMIENTO']}")
            print(f"  - FECHA_MOVIMIENTO: {row['FECHA_MOVIMIENTO']}")
            
            try:
                # Convertir la fecha a formato ISO usando pandas
                fecha = pd.to_datetime(row['FECHA_MOVIMIENTO']).strftime('%Y-%m-%d')
                
                # Preparar los datos del movimiento
                datos_mov = {
                    'REG_PATR': row['REG_PATR'],
                    'NUM_AFIL': row['NUM_AFIL'],
                    'TIP_MOVS': row['TIPO_MOVIMIENTO'],
                    'FEC_INIC': fecha
                }
                
                # Agregar número de crédito si está presente
                if 'NUMERO_CREDITO' in row and pd.notna(row['NUMERO_CREDITO']):
                    datos_mov['Num_Cre'] = str(row['NUMERO_CREDITO'])
                
                # Procesar el movimiento
                resultado = conector.procesar_movimiento(datos_mov)
                
                if resultado:
                    print(f"✅ Movimiento procesado correctamente")
                    exitos += 1
                else:
                    print(f"❌ Error al procesar movimiento")
                    errores += 1
            
            except Exception as e:
                print(f"❌ Error en registro {idx+1}: {str(e)}")
                errores += 1
        
        # Verificar estado final en la base de datos
        print("\nVerificando datos en Asegura después de procesar todos los movimientos...")
        cursor = conector.conn.cursor()
        query = f"SELECT FEC_DSC FROM Asegura WHERE REG_PATR = 'E5352621109' AND NUM_AFIL = '04088625456'"
        cursor.execute(query)
        row = cursor.fetchone()
        
        if row:
            fec_dsc = row[0]
            if fec_dsc is not None:
                print(f"✅ FEC_DSC en Asegura: {fec_dsc}")
            else:
                print(f"❌ ERROR: FEC_DSC es NULL, lo cual no es esperado después de un reinicio")
        else:
            print("❌ No se encontró el registro en Asegura")
        
        # Verificar movimientos en la tabla Movtos
        query = """
        SELECT TOP 5 TIP_MOVS, FEC_INIC, Tip_Des, Num_Cre 
        FROM Movtos 
        WHERE REG_PATR = 'E5352621109' AND NUM_AFIL = '04088625456' 
        AND TIP_MOVS IN ('16', '17')
        ORDER BY FEC_INIC DESC
        """
        cursor.execute(query)
        rows = cursor.fetchall()
        
        print("\nMovimientos más recientes en la tabla Movtos:")
        for row in rows:
            print(f"  - Tipo: {row[0]}, Fecha: {row[1]}, Tip_Des: {row[2]}, Num_Cre: {row[3]}")
        
        # Desconectar
        conector.desconectar()
        
        print(f"\nResumen: {exitos} éxitos, {errores} errores de {len(df)} registros")
        
    except Exception as e:
        print(f"Error general: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    archivo = "test_bajas_reingresos.xlsx"
    if len(sys.argv) > 1:
        archivo = sys.argv[1]
    
    procesar_archivo(archivo)
