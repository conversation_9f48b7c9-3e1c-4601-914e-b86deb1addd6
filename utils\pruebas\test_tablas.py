from conectores.conector_sua import ConectorSUA
import pandas as pd

def examinar_tablas():
    """Examina las tablas Asegura y Movtos directamente"""
    try:
        print("Conectando a la base de datos...")
        conector = ConectorSUA(password='S5@N52V49')
        if not conector.conectar():
            print("Error al conectar a la base de datos")
            return
        
        # 1. Examinar estructura de la tabla Asegura
        print("\n=== ESTRUCTURA DE LA TABLA ASEGURA ===")
        try:
            estructura_asegura = conector.obtener_estructura_tabla('Asegura')
            for campo in estructura_asegura:
                print(f"- {campo}")
        except Exception as e:
            print(f"Error al obtener estructura: {e}")

        # 2. Obtener una muestra de datos de Asegura
        print("\n=== MUESTRA DE DATOS DE ASEGURA ===")
        try:
            # Usar pandas directamente con la conexión
            query = "SELECT TOP 3 * FROM Asegura"
            cursor = conector.conn.cursor()
            cursor.execute(query)
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            
            # Obtener datos
            rows = cursor.fetchall()
            datos = []
            for row in rows:
                datos.append({columnas[i]: value for i, value in enumerate(row)})
            
            # Mostrar datos
            print(pd.DataFrame(datos))
        except Exception as e:
            print(f"Error al obtener datos de Asegura: {e}")
        
        # 3. Examinar estructura de la tabla Movtos
        print("\n=== ESTRUCTURA DE LA TABLA MOVTOS ===")
        try:
            estructura_movtos = conector.obtener_estructura_tabla('Movtos')
            for campo in estructura_movtos:
                print(f"- {campo}")
        except Exception as e:
            print(f"Error al obtener estructura: {e}")
        
        # 4. Obtener una muestra de datos de Movtos
        print("\n=== MUESTRA DE DATOS DE MOVTOS ===")
        try:
            # Usar pandas directamente con la conexión
            query = "SELECT TOP 3 * FROM Movtos"
            cursor = conector.conn.cursor()
            cursor.execute(query)
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            
            # Obtener datos
            rows = cursor.fetchall()
            datos = []
            for row in rows:
                datos.append({columnas[i]: value for i, value in enumerate(row)})
            
            # Mostrar datos
            print(pd.DataFrame(datos))
        except Exception as e:
            print(f"Error al obtener datos de Movtos: {e}")
        
        # 5. Revisar un empleado específico
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        print(f"\n=== BUSCANDO EMPLEADO {reg_patr} - {num_afil} ===")
        try:
            # Consultar en Asegura
            query = f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            cursor = conector.conn.cursor()
            cursor.execute(query)
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            
            # Obtener datos
            rows = cursor.fetchall()
            if rows:
                datos = []
                for row in rows:
                    datos.append({columnas[i]: value for i, value in enumerate(row)})
                
                print("Datos del empleado en Asegura:")
                print(pd.DataFrame(datos))
            else:
                print(f"No se encontró el empleado {num_afil} en Asegura")
                
            # Consultar en Movtos
            query = f"SELECT * FROM Movtos WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            cursor = conector.conn.cursor()
            cursor.execute(query)
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            
            # Obtener datos
            rows = cursor.fetchall()
            if rows:
                datos = []
                for row in rows:
                    datos.append({columnas[i]: value for i, value in enumerate(row)})
                
                print("\nMovimientos del empleado en Movtos:")
                print(pd.DataFrame(datos))
            else:
                print(f"No se encontraron movimientos para el empleado {num_afil}")
                
        except Exception as e:
            print(f"Error al buscar empleado: {e}")
        
        # Desconectar
        conector.desconectar()
    
    except Exception as e:
        print(f"Error general: {e}")

if __name__ == "__main__":
    examinar_tablas() 