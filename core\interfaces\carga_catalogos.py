import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from datetime import datetime, date
import os
import pyodbc
from plantillas_catalogos import crear_plantilla_uma, crear_plantilla_umi, crear_plantilla_inpc, crear_plantilla_salario

class CargaCatalogos:
    def __init__(self, root):
        self.root = root
        self.root.title("Carga de Catálogos")
        self.root.geometry("800x600")
        
        # Configuración de la conexión a Access
        ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
        password = "S5@N52V49"
        ruta_normalizada = os.path.normpath(ruta_bd)
        
        self.conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={ruta_normalizada};'
            f'PWD={password};'
        )
        
        # Frame principal
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Título
        ttk.Label(self.main_frame, text="Carga de Catálogos", font=('Arial', 16)).grid(row=0, column=0, columnspan=2, pady=10)
        
        # Selección de catálogo
        ttk.Label(self.main_frame, text="Seleccionar Catálogo:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.catalogo_var = tk.StringVar()
        self.catalogo_combo = ttk.Combobox(self.main_frame, textvariable=self.catalogo_var)
        self.catalogo_combo['values'] = ('UMA', 'UMI', 'INPC', 'SALARIO')
        self.catalogo_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        self.catalogo_combo.bind('<<ComboboxSelected>>', self.on_catalogo_selected)
        
        # Botones
        self.btn_frame = ttk.Frame(self.main_frame)
        self.btn_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(self.btn_frame, text="Generar Plantilla", command=self.generar_plantilla).pack(side=tk.LEFT, padx=5)
        ttk.Button(self.btn_frame, text="Cargar Archivo", command=self.cargar_archivo).pack(side=tk.LEFT, padx=5)
        
        # Área de texto para logs
        ttk.Label(self.main_frame, text="Log de Operaciones:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.log_text = tk.Text(self.main_frame, height=20, width=80)
        self.log_text.grid(row=4, column=0, columnspan=2, pady=5)
        
        # Scrollbar para el área de texto
        scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(row=4, column=2, sticky=(tk.N, tk.S))
        self.log_text['yscrollcommand'] = scrollbar.set
        
    def log(self, mensaje):
        """Agrega un mensaje al log"""
        self.log_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {mensaje}\n")
        self.log_text.see(tk.END)
        
    def on_catalogo_selected(self, event):
        """Maneja el evento de selección de catálogo"""
        catalogo = self.catalogo_var.get()
        self.log(f"Catálogo seleccionado: {catalogo}")
        
    def generar_plantilla(self):
        """Genera la plantilla según el catálogo seleccionado"""
        catalogo = self.catalogo_var.get()
        if not catalogo:
            messagebox.showerror("Error", "Por favor seleccione un catálogo")
            return
            
        try:
            if catalogo == 'UMA':
                archivo = crear_plantilla_uma()
                self.log(f"Plantilla UMA generada: {archivo}")
                messagebox.showinfo("Éxito", f"Plantilla generada: {archivo}")
            elif catalogo == 'UMI':
                archivo = crear_plantilla_umi()
                self.log(f"Plantilla UMI generada: {archivo}")
                messagebox.showinfo("Éxito", f"Plantilla generada: {archivo}")
            elif catalogo == 'INPC':
                archivo = crear_plantilla_inpc()
                self.log(f"Plantilla INPC generada: {archivo}")
                messagebox.showinfo("Éxito", f"Plantilla generada: {archivo}")
            elif catalogo == 'SALARIO':
                archivo = crear_plantilla_salario()
                self.log(f"Plantilla SALARIO generada: {archivo}")
                messagebox.showinfo("Éxito", f"Plantilla generada: {archivo}")
            else:
                self.log(f"Generación de plantilla para {catalogo} no implementada aún")
                messagebox.showinfo("Información", f"La generación de plantilla para {catalogo} estará disponible próximamente")
        except Exception as e:
            self.log(f"Error al generar plantilla: {str(e)}")
            messagebox.showerror("Error", f"Error al generar plantilla: {str(e)}")
            
    def cargar_archivo(self):
        """Carga y valida el archivo seleccionado"""
        catalogo = self.catalogo_var.get()
        if not catalogo:
            messagebox.showerror("Error", "Por favor seleccione un catálogo")
            return
            
        try:
            archivo = filedialog.askopenfilename(
                title="Seleccionar archivo",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if not archivo:
                return
                
            self.log(f"Cargando archivo: {archivo}")
            
            # Leer el archivo Excel
            df = pd.read_excel(archivo)
            
            # Validar según el catálogo
            if catalogo == 'UMA':
                df = self.validar_uma(archivo)
                if df is not None:
                    self.guardar_en_access(df, 'UMA')
            elif catalogo == 'UMI':
                df = self.validar_umi(archivo)
                if df is not None:
                    self.guardar_en_access(df, 'ValINF')
            elif catalogo == 'INPC':
                df = self.validar_inpc(archivo)
                if df is not None:
                    self.guardar_en_access(df, 'INPC')
            elif catalogo == 'SALARIO':
                df = self.validar_salario(df)
                if df is not None:
                    self.guardar_en_access(df, 'SALARIO')
            else:
                self.log(f"Validación para {catalogo} no implementada aún")
                messagebox.showinfo("Información", f"La validación para {catalogo} estará disponible próximamente")
                
        except Exception as e:
            self.log(f"Error al cargar archivo: {str(e)}")
            messagebox.showerror("Error", f"Error al cargar archivo: {str(e)}")
            
    def validar_umi(self, archivo):
        """Valida el archivo de UMI"""
        try:
            df = pd.read_excel(archivo)
            
            # Validar columnas
            columnas_requeridas = ['FEC_INIC', 'VALOR']
            for col in columnas_requeridas:
                if col not in df.columns:
                    raise ValueError(f"Falta la columna: {col}")
                    
            # Validar fechas
            df['FEC_INIC'] = pd.to_datetime(df['FEC_INIC'], errors='coerce')
            
            if df['FEC_INIC'].isnull().any():
                raise ValueError("Hay fechas inválidas en el archivo")
                
            # Validar que todas las fechas de inicio sean 01/01
            for fecha in df['FEC_INIC']:
                if fecha.day != 1 or fecha.month != 1:
                    raise ValueError(f"La fecha {fecha.strftime('%d/%m/%Y')} debe ser 01/01")
                    
            # Ordenar por fecha
            df = df.sort_values('FEC_INIC')
            
            # Calcular fechas fin y validar solapamientos
            for i in range(len(df)):
                fecha_inicio = df.iloc[i]['FEC_INIC']
                es_ultimo = (i == len(df) - 1)
                
                # Calcular fecha fin
                if es_ultimo:
                    fecha_fin = pd.Timestamp('2999-12-31')
                else:
                    fecha_fin = pd.Timestamp(fecha_inicio.year, 12, 31)
                
                # Validar que no haya solapamiento con el siguiente registro
                if not es_ultimo:
                    siguiente_inicio = df.iloc[i+1]['FEC_INIC']
                    if fecha_fin >= siguiente_inicio:
                        raise ValueError(f"Solapamiento detectado entre {fecha_inicio.strftime('%d/%m/%Y')} y {siguiente_inicio.strftime('%d/%m/%Y')}")
                
                # Agregar fecha fin al DataFrame
                df.loc[df.index[i], 'FEC_FIN'] = fecha_fin
                    
            # Validar valores
            df['VALOR'] = pd.to_numeric(df['VALOR'], errors='coerce')
            if df['VALOR'].isnull().any():
                raise ValueError("Los valores deben ser numéricos")
                
            self.log("Archivo UMI validado correctamente")
            messagebox.showinfo("Éxito", "Archivo validado correctamente")
            
            return df
            
        except Exception as e:
            self.log(f"Error en validación UMI: {str(e)}")
            messagebox.showerror("Error", f"Error en validación: {str(e)}")
            return None
            
    def validar_uma(self, archivo):
        """Valida el archivo de UMA"""
        try:
            df = pd.read_excel(archivo)
            
            # Validar columnas
            columnas_requeridas = ['Fecha Inicio', 'Valor UMA', 'Observaciones']
            for col in columnas_requeridas:
                if col not in df.columns:
                    raise ValueError(f"Falta la columna: {col}")
                    
            # Validar fechas
            df['Fecha Inicio'] = pd.to_datetime(df['Fecha Inicio'])
            
            # Validar que todas las fechas sean 01/02
            for fecha in df['Fecha Inicio']:
                if fecha.day != 1 or fecha.month != 2:
                    raise ValueError(f"La fecha {fecha.strftime('%d/%m/%Y')} debe ser 01/02")
                    
            # Ordenar por fecha
            df = df.sort_values('Fecha Inicio')
            
            # Calcular fechas fin y validar solapamientos
            for i in range(len(df)):
                fecha_inicio = df.iloc[i]['Fecha Inicio']
                es_ultimo = (i == len(df) - 1)
                
                # Calcular fecha fin
                if es_ultimo:
                    fecha_fin = pd.Timestamp('2999-12-31')
                else:
                    fecha_fin = pd.Timestamp(fecha_inicio.year + 1, 1, 31)
                
                # Validar que no haya solapamiento con el siguiente registro
                if not es_ultimo:
                    siguiente_inicio = df.iloc[i+1]['Fecha Inicio']
                    if fecha_fin >= siguiente_inicio:
                        raise ValueError(f"Solapamiento detectado entre {fecha_inicio.strftime('%d/%m/%Y')} y {siguiente_inicio.strftime('%d/%m/%Y')}")
                    
            # Validar valores UMA
            if not all(isinstance(x, (int, float)) for x in df['Valor UMA']):
                raise ValueError("Los valores de UMA deben ser numéricos")
                
            self.log("Archivo UMA validado correctamente")
            messagebox.showinfo("Éxito", "Archivo validado correctamente")
            
            # Agregar columna de fecha fin
            df['Fecha Fin'] = [pd.Timestamp('2999-12-31') if i == len(df)-1 else pd.Timestamp(df.iloc[i]['Fecha Inicio'].year + 1, 1, 31) for i in range(len(df))]
            
            return df
            
        except Exception as e:
            self.log(f"Error en validación UMA: {str(e)}")
            messagebox.showerror("Error", f"Error en validación: {str(e)}")
            return None
            
    def validar_inpc(self, archivo):
        """Valida el archivo de INPC"""
        try:
            df = pd.read_excel(archivo)
            
            # Validar columnas
            columnas_requeridas = ['Fecha', 'Valor INPC', 'Valor Rec']
            for col in columnas_requeridas:
                if col not in df.columns:
                    raise ValueError(f"Falta la columna: {col}")
                    
            # Validar fechas
            df['Fecha'] = pd.to_datetime(df['Fecha'], errors='coerce')
            
            if df['Fecha'].isnull().any():
                raise ValueError("Hay fechas inválidas en el archivo")
                
            # Validar que todas las fechas sean el primer día del mes
            for fecha in df['Fecha']:
                if fecha.day != 1:
                    raise ValueError(f"La fecha {fecha.strftime('%d/%m/%Y')} debe ser el primer día del mes")
                    
            # Ordenar por fecha
            df = df.sort_values('Fecha')
            
            # Convertir fecha a formato AAAAMM
            df['MES_ANO'] = df['Fecha'].dt.strftime('%Y%m')
            
            # Validar valores
            for col in ['Valor INPC', 'Valor Rec']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                if df[col].isnull().any():
                    raise ValueError(f"Los valores de {col} deben ser numéricos")
                
            self.log("Archivo INPC validado correctamente")
            messagebox.showinfo("Éxito", "Archivo validado correctamente")
            
            return df
            
        except Exception as e:
            self.log(f"Error en validación INPC: {str(e)}")
            messagebox.showerror("Error", f"Error en validación: {str(e)}")
            return None
            
    def validar_salario(self, df):
        """Valida el archivo de salarios mínimos"""
        try:
            self.log("Iniciando validación de salarios...")
            
            # Mostrar las columnas disponibles
            self.log(f"Columnas disponibles: {df.columns.tolist()}")
            
            # Validar columnas requeridas
            columnas_requeridas = ['Fecha Inicio', 'Salario']
            for col in columnas_requeridas:
                if col not in df.columns:
                    raise ValueError(f"Falta la columna requerida: {col}")
            
            # Mostrar los primeros registros para depuración
            self.log("Primeros registros del archivo:")
            self.log(df.head().to_string())
            
            # Convertir fechas
            self.log("Convirtiendo fechas...")
            df['Fecha Inicio'] = pd.to_datetime(df['Fecha Inicio'], format='%d/%m/%Y')
            
            # Validar que las fechas sean el primer día del mes
            self.log("Validando días del mes...")
            if not all(df['Fecha Inicio'].dt.day == 1):
                raise ValueError("Todas las fechas deben ser el primer día del mes")
            
            # Ordenar por fecha
            self.log("Ordenando por fecha...")
            df = df.sort_values('Fecha Inicio')
            
            # Validar que no haya solapamiento
            self.log("Validando solapamientos...")
            for i in range(len(df) - 1):
                if df['Fecha Inicio'].iloc[i + 1] <= df['Fecha Inicio'].iloc[i]:
                    raise ValueError(f"Hay solapamiento entre las fechas {df['Fecha Inicio'].iloc[i]} y {df['Fecha Inicio'].iloc[i + 1]}")
            
            # Convertir salario a numérico
            self.log("Convirtiendo salarios...")
            df['Salario'] = pd.to_numeric(df['Salario'])
            
            # Calcular fechas fin (día anterior a la siguiente fecha)
            self.log("Calculando fechas fin...")
            df['Fecha Fin'] = df['Fecha Inicio'].shift(-1) - pd.Timedelta(days=1)
            
            # Para el último registro, usar 31/12 del año correspondiente
            ultima_fecha = df['Fecha Inicio'].iloc[-1]
            df['Fecha Fin'].iloc[-1] = pd.Timestamp(ultima_fecha.year, 12, 31)
            
            # Calcular aumentos
            self.log("Calculando aumentos...")
            df['Aumento'] = df['Salario'] * 1.04932
            
            # Redondear valores
            self.log("Redondeando valores...")
            df['Salario'] = df['Salario'].round(2)
            df['Aumento'] = df['Aumento'].round(2)
            
            # Mostrar el resultado final
            self.log("Resultado final:")
            self.log(df.to_string())
            
            self.log("Archivo de salarios validado correctamente")
            return df
        
        except Exception as e:
            self.log(f"Error en validación de salarios: {str(e)}")
            self.log(f"Tipo de error: {type(e)}")
            self.log(f"Detalles del error: {str(e)}")
            messagebox.showerror("Error", f"Error en validación: {str(e)}")
            return None
            
    def guardar_en_access(self, df, tipo_catalogo):
        """Guarda los datos en la base de datos Access"""
        try:
            conn = pyodbc.connect(self.conn_str)
            cursor = conn.cursor()
            
            if tipo_catalogo == "UMA":
                # Para cada registro en el DataFrame
                for _, row in df.iterrows():
                    fecha_inicio = row['Fecha Inicio']
                    fecha_fin = row['Fecha Fin']
                    valor = row['Valor UMA']
                    
                    # Verificar si ya existe un registro con esta fecha
                    cursor.execute(
                        "SELECT COUNT(*) FROM UMA WHERE Fec_Ini = ?",
                        fecha_inicio
                    )
                    existe = cursor.fetchone()[0] > 0
                    
                    if existe:
                        # Actualizar registro existente
                        cursor.execute(
                            "UPDATE UMA SET Valor = ?, Fec_Ter = ? WHERE Fec_Ini = ?",
                            valor, fecha_fin, fecha_inicio
                        )
                        self.log(f"Actualizado registro para fecha {fecha_inicio.strftime('%d/%m/%Y')}")
                    else:
                        # Insertar nuevo registro
                        cursor.execute(
                            "INSERT INTO UMA (Fec_Ini, Fec_Ter, Valor) VALUES (?, ?, ?)",
                            fecha_inicio, fecha_fin, valor
                        )
                        self.log(f"Insertado nuevo registro para fecha {fecha_inicio.strftime('%d/%m/%Y')}")
            
            elif tipo_catalogo == "ValINF":
                # Para cada registro en el DataFrame
                for _, row in df.iterrows():
                    fecha_inicio = row['FEC_INIC']
                    fecha_fin = row['FEC_FIN']
                    valor = row['VALOR']
                    
                    # Verificar si ya existe un registro con estas fechas
                    cursor.execute(
                        "SELECT COUNT(*) FROM ValINF WHERE Fec_Ini = ? AND Fec_Ter = ?",
                        fecha_inicio, fecha_fin
                    )
                    existe = cursor.fetchone()[0] > 0
                    
                    if existe:
                        # Actualizar registro existente
                        cursor.execute(
                            "UPDATE ValINF SET Valor = ? WHERE Fec_Ini = ? AND Fec_Ter = ?",
                            valor, fecha_inicio, fecha_fin
                        )
                        self.log(f"Actualizado registro para período {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}")
                    else:
                        # Insertar nuevo registro
                        cursor.execute(
                            "INSERT INTO ValINF (Fec_Ini, Fec_Ter, Valor) VALUES (?, ?, ?)",
                            fecha_inicio, fecha_fin, valor
                        )
                        self.log(f"Insertado nuevo registro para período {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}")
            
            elif tipo_catalogo == "INPC":
                # Para cada registro en el DataFrame
                for _, row in df.iterrows():
                    mes_ano = row['MES_ANO']
                    valor_inpc = row['Valor INPC']
                    valor_rec = row['Valor Rec']
                    
                    # Verificar si ya existe un registro con este mes_año
                    cursor.execute(
                        "SELECT COUNT(*) FROM INPC WHERE MES_ANO = ?",
                        (mes_ano,)
                    )
                    existe = cursor.fetchone()[0] > 0
                    
                    if existe:
                        # Actualizar registro existente
                        cursor.execute(
                            "UPDATE INPC SET VAL_INP = ?, VAL_REC = ? WHERE MES_ANO = ?",
                            (valor_inpc, valor_rec, mes_ano)
                        )
                        self.log(f"Actualizado registro para mes {mes_ano}")
                    else:
                        # Insertar nuevo registro
                        cursor.execute(
                            "INSERT INTO INPC (MES_ANO, VAL_INP, VAL_REC) VALUES (?, ?, ?)",
                            (mes_ano, valor_inpc, valor_rec)
                        )
                        self.log(f"Insertado nuevo registro para mes {mes_ano}")
            
            elif tipo_catalogo == "SALARIO":
                # Para cada registro en el DataFrame
                for _, row in df.iterrows():
                    # Convertir fechas a formato datetime
                    fecha_inicio = row['Fecha Inicio'].to_pydatetime()
                    fecha_fin = row['Fecha Fin'].to_pydatetime()
                    
                    # Convertir valores numéricos a float
                    salario = float(row['Salario'])
                    aumento = float(row['Aumento'])
                    
                    # Verificar si existe el registro
                    cursor.execute("SELECT COUNT(*) FROM SALARIO WHERE FEC_INI = ?", fecha_inicio)
                    if cursor.fetchone()[0] > 0:
                        # Actualizar registro existente
                        cursor.execute("""
                            UPDATE SALARIO 
                            SET ZONAA = ?, AUMENTOA = ?, ZONAB = ?, AUMENTOB = ?, ZONAC = ?, AUMENTOC = ?, FEC_TER = ?
                            WHERE FEC_INI = ?
                        """, (salario, aumento, salario, aumento, salario, aumento, fecha_fin, fecha_inicio))
                        self.log(f"Actualizado registro de salario para fecha {fecha_inicio.strftime('%d/%m/%Y')}")
                    else:
                        # Insertar nuevo registro
                        cursor.execute("""
                            INSERT INTO SALARIO (FEC_INI, FEC_TER, ZONAA, AUMENTOA, ZONAB, AUMENTOB, ZONAC, AUMENTOC)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (fecha_inicio, fecha_fin, salario, aumento, salario, aumento, salario, aumento))
                        self.log(f"Insertado nuevo registro de salario para fecha {fecha_inicio.strftime('%d/%m/%Y')}")
            
            conn.commit()
            self.log(f"Datos guardados correctamente en la tabla {tipo_catalogo}")
            messagebox.showinfo("Éxito", f"Datos guardados correctamente en {tipo_catalogo}")
            
        except Exception as e:
            self.log(f"Error al guardar en Access: {str(e)}")
            messagebox.showerror("Error", f"Error al guardar en Access: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

if __name__ == "__main__":
    root = tk.Tk()
    app = CargaCatalogos(root)
    root.mainloop() 