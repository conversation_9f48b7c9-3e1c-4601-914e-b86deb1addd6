"""
Script simple para explorar las tablas y sus campos en la base de datos SUA.
"""

import os
import sys
import pyodbc

def conectar_bd(ruta_bd, password=None):
    """Conecta a la base de datos SUA y devuelve la conexión"""
    try:
        # Normalizar la ruta
        ruta_bd = os.path.normpath(ruta_bd)
        
        # Construir cadena de conexión
        if password:
            connection_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={ruta_bd};PWD={password};"
        else:
            connection_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={ruta_bd};"
        
        # Establecer conexión
        conn = pyodbc.connect(connection_str)
        print(f"Conexión exitosa a la base de datos: {ruta_bd}")
        return conn
    
    except Exception as e:
        print(f"Error al conectar a la base de datos: {e}")
        return None

def listar_tablas(conn):
    """Lista todas las tablas en la base de datos"""
    if not conn:
        return []
    
    cursor = conn.cursor()
    tablas = []
    
    # En MS Access, la consulta para obtener tablas es diferente
    for row in cursor.tables(tableType='TABLE'):
        tablas.append(row.table_name)
    
    return tablas

def ver_estructura_tabla(conn, nombre_tabla):
    """Muestra la estructura (columnas) de una tabla específica"""
    if not conn:
        return
    
    cursor = conn.cursor()
    
    try:
        # Ejecutar una consulta que no devuelve datos para obtener las columnas
        cursor.execute(f"SELECT TOP 0 * FROM [{nombre_tabla}]")
        columnas = [column[0] for column in cursor.description]
        
        print(f"\nEstructura de la tabla {nombre_tabla}:")
        for i, col in enumerate(columnas, 1):
            print(f"{i}. {col}")
    
    except Exception as e:
        print(f"Error al obtener estructura de la tabla {nombre_tabla}: {e}")

def main():
    # Configuración
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    password = "S5@N52V49"
    
    # Conectar a la base de datos
    conn = conectar_bd(ruta_bd, password)
    if not conn:
        print("No se pudo conectar a la base de datos. Abortando.")
        sys.exit(1)
    
    try:
        # Listar todas las tablas
        tablas = listar_tablas(conn)
        print(f"\nTablas en la base de datos ({len(tablas)}):")
        for i, tabla in enumerate(tablas, 1):
            print(f"{i}. {tabla}")
        
        # Mostrar estructura de algunas tablas específicas
        tablas_objetivo = ['Patron', 'Prima_RT', 'Estados', 'Subdelega']
        
        for tabla in tablas_objetivo:
            if tabla in tablas:
                ver_estructura_tabla(conn, tabla)
            else:
                print(f"\nLa tabla {tabla} no existe en la base de datos.")
    
    finally:
        # Cerrar conexión
        conn.close()
        print("\nExploración finalizada.")

if __name__ == "__main__":
    main() 