"""
Modelo para la tabla Prima_RT del SUA.
Define las estructuras de datos y las operaciones relacionadas con la tabla Prima_RT.
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
import pandas as pd
import datetime

@dataclass
class PrimaRT:
    """
    Clase que representa un registro de la tabla Prima_RT.
    Los nombres de atributos y tipos son aproximados y deben ajustarse 
    según la estructura real de la base de datos.
    """
    # Campos aproximados, ajustar según exploración
    registro_patronal: str
    prima: float
    fecha_inicio: Optional[datetime.date] = None
    fecha_fin: Optional[datetime.date] = None
    # Añadir los demás campos según la exploración
    
    # Campos adicionales para control interno
    _modificado: bool = field(default=False, repr=False)
    _metadatos: Dict[str, Any] = field(default_factory=dict, repr=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PrimaRT':
        """
        Crea una instancia de PrimaRT a partir de un diccionario.
        
        Args:
            data: Diccionario con los datos de la prima RT.
            
        Returns:
            PrimaRT: Instancia de PrimaRT con los datos proporcionados.
        """
        # Filtrar solo las claves que son atributos de la clase
        campos_validos = {k: v for k, v in data.items() 
                         if k in cls.__annotations__ and not k.startswith('_')}
        
        # Convertir fechas si es necesario
        for campo_fecha in ['fecha_inicio', 'fecha_fin']:
            if campo_fecha in campos_validos and campos_validos[campo_fecha] and not isinstance(campos_validos[campo_fecha], datetime.date):
                try:
                    campos_validos[campo_fecha] = pd.to_datetime(campos_validos[campo_fecha]).date()
                except:
                    campos_validos[campo_fecha] = None
        
        # Convertir prima a float si es necesario
        if 'prima' in campos_validos and not isinstance(campos_validos['prima'], float):
            try:
                campos_validos['prima'] = float(campos_validos['prima'])
            except:
                campos_validos['prima'] = 0.0
        
        return cls(**campos_validos)
    
    @classmethod
    def from_dataframe(cls, df: pd.DataFrame) -> List['PrimaRT']:
        """
        Crea una lista de instancias de PrimaRT a partir de un DataFrame.
        
        Args:
            df: DataFrame con datos de primas RT.
            
        Returns:
            List[PrimaRT]: Lista de instancias de PrimaRT.
        """
        return [cls.from_dict(row) for _, row in df.iterrows()]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convierte la instancia a un diccionario.
        
        Returns:
            Dict[str, Any]: Diccionario con los atributos de la prima RT.
        """
        # Excluir campos internos que comienzan con _
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    def marcar_como_modificado(self) -> None:
        """Marca el registro como modificado para seguimiento de cambios"""
        self._modificado = True
    
    def esta_modificado(self) -> bool:
        """Verifica si el registro ha sido modificado"""
        return self._modificado
    
    def agregar_metadato(self, clave: str, valor: Any) -> None:
        """Agrega un metadato al registro"""
        self._metadatos[clave] = valor
    
    def obtener_metadato(self, clave: str, default: Any = None) -> Any:
        """Obtiene un metadato del registro"""
        return self._metadatos.get(clave, default)


class PrimaRTRepository:
    """
    Clase para gestionar operaciones de acceso a datos de la tabla Prima_RT.
    Implementa el patrón Repository para separar la lógica de acceso a datos.
    """
    
    def __init__(self, conector):
        """
        Inicializa el repositorio.
        
        Args:
            conector: Instancia de ConectorSUA para acceder a la base de datos.
        """
        self.conector = conector
        self.tabla = "Prima_RT"
    
    def obtener_todos(self, limite: int = 1000) -> List[PrimaRT]:
        """
        Obtiene todos los registros de primas RT.
        
        Args:
            limite: Número máximo de registros a obtener.
            
        Returns:
            List[PrimaRT]: Lista de primas RT.
        """
        df = self.conector.obtener_datos_tabla(self.tabla, limite=limite)
        return PrimaRT.from_dataframe(df) if not df.empty else []
    
    def obtener_por_registro_patronal(self, registro_patronal: str) -> List[PrimaRT]:
        """
        Obtiene todos los registros de PrimaRT para un registro patronal específico.
        
        Args:
            registro_patronal: Registro patronal a buscar.
            
        Returns:
            List[PrimaRT]: Lista de registros de PrimaRT.
        """
        # Escapar posibles comillas en el registro patronal
        reg_pat_escaped = registro_patronal.replace("'", "''")
        
        # Consulta directa sin parámetros
        query = f"SELECT * FROM [Prima_RT] WHERE Reg_Pat = '{reg_pat_escaped}'"
        
        resultados = self.conector.ejecutar_consulta(query)
        
        if not resultados:
            return []
        
        # Crear objetos PrimaRT a partir de los datos obtenidos
        primas = [PrimaRT(**resultado) for resultado in resultados]
        return primas
    
    def obtener_prima_vigente(self, registro_patronal: str, fecha: Optional[datetime.date] = None) -> Optional[PrimaRT]:
        """
        Obtiene la prima RT vigente para un patrón en una fecha específica.
        
        Args:
            registro_patronal: Registro patronal a buscar.
            fecha: Fecha para la cual buscar la prima vigente. Si es None, se usa la fecha actual.
            
        Returns:
            Optional[PrimaRT]: Prima RT vigente o None si no se encuentra.
        """
        if fecha is None:
            fecha = datetime.date.today()
        
        fecha_str = fecha.strftime("%Y-%m-%d")
        
        # Buscar prima vigente (donde la fecha está entre fecha_inicio y fecha_fin)
        df = self.conector.ejecutar_consulta(
            f"SELECT TOP 1 * FROM [{self.tabla}] "
            f"WHERE registro_patronal = '{registro_patronal}' "
            f"AND fecha_inicio <= #{fecha_str}# "
            f"AND (fecha_fin IS NULL OR fecha_fin >= #{fecha_str}#) "
            f"ORDER BY fecha_inicio DESC"
        )
        
        if df.empty:
            return None
        
        return PrimaRT.from_dict(df.iloc[0].to_dict())
    
    def buscar(self, **criterios) -> List[PrimaRT]:
        """
        Busca primas RT según criterios específicos.
        
        Args:
            **criterios: Pares clave-valor para búsqueda (campo=valor).
            
        Returns:
            List[PrimaRT]: Lista de primas RT que cumplen los criterios.
        """
        # Construir condición WHERE
        condiciones = []
        for campo, valor in criterios.items():
            if isinstance(valor, str):
                condiciones.append(f"{campo} LIKE '%{valor}%'")
            elif isinstance(valor, (datetime.date, datetime.datetime)):
                fecha_str = valor.strftime("%Y-%m-%d")
                condiciones.append(f"{campo} = #{fecha_str}#")
            else:
                condiciones.append(f"{campo} = {valor}")
        
        where = " AND ".join(condiciones) if condiciones else None
        
        df = self.conector.obtener_datos_tabla(self.tabla, where=where)
        return PrimaRT.from_dataframe(df) if not df.empty else []
    
    def obtener_historial(self, registro_patronal: str) -> pd.DataFrame:
        """
        Obtiene el historial completo de primas RT para un registro patronal.
        
        Args:
            registro_patronal: Registro patronal a consultar.
            
        Returns:
            DataFrame: DataFrame con el historial ordenado cronológicamente.
        """
        # Ejecutar consulta directa con análisis
        df = self.conector.ejecutar_consulta(
            f"SELECT * FROM [{self.tabla}] "
            f"WHERE registro_patronal = '{registro_patronal}' "
            f"ORDER BY fecha_inicio"
        )
        
        # Si hay datos, añadir columna de duración
        if not df.empty and 'fecha_inicio' in df.columns and 'fecha_fin' in df.columns:
            # Convertir columnas a datetime si no lo están
            for col in ['fecha_inicio', 'fecha_fin']:
                if col in df.columns and not pd.api.types.is_datetime64_any_dtype(df[col]):
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            
            # Calcular duración en días (solo donde fecha_fin no es nula)
            mask = df['fecha_fin'].notna()
            df.loc[mask, 'duracion_dias'] = (df.loc[mask, 'fecha_fin'] - df.loc[mask, 'fecha_inicio']).dt.days
        
        return df 