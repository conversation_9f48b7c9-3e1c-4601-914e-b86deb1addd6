from conectores.conector_sua import ConectorSUA
from datetime import datetime

def probar_baja_sua():
    try:
        # Conectar a la base de datos
        print("Conectando a la base de datos SUA...")
        conector = ConectorSUA(password='S5@N52V49')
        if not conector.conectar():
            print("Error al conectar a la base de datos")
            return False
        
        # Datos del empleado a dar de baja
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        # Obtener fecha actual para la baja
        fecha_baja = datetime.now().strftime("%Y-%m-%d")
        
        print(f"Procesando baja para: {reg_patr} - {num_afil} con fecha {fecha_baja}")
        
        # Preparar datos del movimiento
        datos_mov = {
            'REG_PATR': reg_patr,
            'NUM_AFIL': num_afil,
            'TIP_MOVS': '02',  # Código de baja
            'FEC_INIC': fecha_baja
        }
        
        # Procesar la baja usando el método específico de ConectorSUA
        resultado = conector.procesar_baja(datos_mov)
        
        # Verificar el resultado
        if resultado:
            print("✅ Baja procesada exitosamente")
        else:
            print("❌ Error al procesar la baja")
        
        # Desconectar
        conector.desconectar()
        return resultado
    
    except Exception as e:
        print(f"Error en la prueba: {str(e)}")
        return False

if __name__ == "__main__":
    probar_baja_sua() 