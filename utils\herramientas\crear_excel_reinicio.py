"""
Script para generar un archivo Excel de prueba con solo un movimiento de reinicio de crédito.
"""

import pandas as pd
from datetime import datetime, timedelta

def main():
    # Crear el DataFrame para el Excel
    df = pd.DataFrame(columns=[
        'REG_PATR',
        'NUM_AFIL',
        'NOMBRE',
        'FECHA_MOVIMIENTO',
        'TIPO_MOVIMIENTO',
        'NUMERO_CREDITO'
    ])
    
    # Fecha futura para el reinicio (posterior a la fecha que ya existe: 2025-09-05)
    fecha_reinicio = '2025-10-15'
    
    # Agregar un registro de reinicio de crédito (17)
    reinicio = {
        'REG_PATR': 'E5352621109',
        'NUM_AFIL': '04088625456',
        'NOMBRE': 'EMPLEADO PRUEBA',
        'FECHA_MOVIMIENTO': fecha_reinicio,
        'TIPO_MOVIMIENTO': '17',
        'NUMERO_CREDITO': '1565544777'
    }
    
    # Agregar el registro al DataFrame
    df = pd.concat([df, pd.DataFrame([reinicio])], ignore_index=True)
    
    # Guardar el DataFrame como Excel
    archivo_salida = 'test_reinicio_credito.xlsx'
    df.to_excel(archivo_salida, index=False)
    
    print(f"Archivo Excel creado: {archivo_salida}")
    print(f"Reinicio (17): Fecha={fecha_reinicio}")
    
    # Verificar el contenido
    df_check = pd.read_excel(archivo_salida)
    print("\nContenido del archivo creado:")
    print(df_check)

if __name__ == "__main__":
    main() 