from conectores.conector_sua import ConectorSUA

# Conectar a la base de datos
conector = ConectorSUA(ruta_bd='C:\\Cobranza\\SUA\\SUA.MDB', password='S5@N52V49')
if conector.conectar():
    print('Conexión establecida')
    
    # Consultar movimientos tipo 19
    print('Consultando movimientos tipo 19:')
    resultados_19 = conector.ejecutar_consulta(
        'SELECT TOP 5 REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, Tip_Des, Val_Des FROM Movtos WHERE TIP_MOVS=?', 
        ('19',)
    )
    if resultados_19:
        for registro in resultados_19:
            print(registro)
    else:
        print('No hay movimientos tipo 19')
    
    # Consultar movimientos tipo 18 para comparar
    print('\nConsultando movimientos tipo 18:')
    resultados_18 = conector.ejecutar_consulta(
        'SELECT TOP 5 REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, Tip_Des, Val_Des FROM Movtos WHERE TIP_MOVS=?', 
        ('18',)
    )
    if resultados_18:
        for registro in resultados_18:
            print(registro)
    else:
        print('No hay movimientos tipo 18')
    
    conector.desconectar()
else:
    print('Error al conectar a la base de datos') 