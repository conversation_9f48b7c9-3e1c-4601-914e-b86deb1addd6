# Modelo de Datos SUA-Tool

## Estructura de la Base de Datos SUA Original

La base de datos SUA.MDB es una base de datos Microsoft Access que contiene principalmente las siguientes tablas:

### Tablas Principales
- **Empresas**: Información de registro patronal y datos fiscales
- **Trabajadores**: Datos personales y laborales de los empleados
- **Movimientos**: Altas, bajas y modificaciones salariales
- **Incidencias**: Ausentismos, incapacidades y otras incidencias
- **Pagos**: Registro de aportaciones, amortizaciones y pagos
- **Catálogos**: Tablas de referencia para UMAs, salarios mínimos, etc.

## Modelo de Datos SUA-Tool

### 1. Esquema de Clonación
Nuestra base de datos PostgreSQL replicará la estructura esencial de SUA.MDB pero con mejoras:

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│   Empresa   │──1:N──│ Trabajador  │──1:N──│ Movimiento  │
└─────────────┘       └─────────────┘       └─────────────┘
      │                     │                     │
      │                     │                     │
      │                  1:N│                     │
      │                     ▼                     │
      │              ┌─────────────┐              │
      │              │ Incidencia  │              │
      │              └─────────────┘              │
      │                                           │
      │                                           │
    1:N│                                        1:N│
      ▼                                           ▼
┌─────────────┐                            ┌─────────────┐
│    Pago     │                            │   Periodo   │
└─────────────┘                            └─────────────┘
```

### 2. Extensiones al Modelo Original

Además de las tablas originales, añadiremos:

- **Usuarios**: Gestión de accesos a la plataforma
  - id, email, password_hash, nombre, rol, estado, fecha_creacion
  
- **Suscripciones**: Control de planes contratados
  - id, usuario_id, plan, fecha_inicio, fecha_fin, estado, metodo_pago
  
- **Análisis**: Almacenamiento de reportes y consultas personalizadas
  - id, usuario_id, nombre, parametros, consulta_sql, fecha_creacion
  
- **Logs**: Auditoría de cambios y operaciones
  - id, usuario_id, operacion, tabla_afectada, registro_id, detalles, timestamp

### 3. Mejoras en el Esquema

- Implementación de claves primarias y foráneas con restricciones de integridad
- Índices optimizados para consultas frecuentes
- Particionado de tablas grandes por periodos
- Normalización de estructuras redundantes del SUA original
- Campos para control de versiones y sincronización

## Estrategia de Migración y Sincronización

1. **Extracción Inicial**:
   - Mapeo completo de la estructura SUA.MDB
   - Carga inicial con preservación de relaciones
   - Validación de integridad referencial

2. **Sincronización Bidireccional**:
   - Detección de cambios mediante checksums
   - Resolución de conflictos priorizando cambios locales
   - Log de sincronización con posibilidad de rollback

3. **Manejo de Esquemas Múltiples**:
   - Soporte para diferentes versiones del SUA
   - Adaptadores para diferencias entre versiones
   - Migración automática ante actualizaciones del SUA

## Consideraciones de Rendimiento

- Tablas particionadas por año/mes para datos históricos
- Vistas materializadas para reportes frecuentes
- Indices compuestos para patrones de consulta comunes
- Compresión de datos para tablas grandes e históricas 