from conectores.conector_sua import ConectorSUA
import pandas as pd
from datetime import datetime

def probar_baja():
    """Prueba la funcionalidad de baja del asegurado"""
    try:
        print("Conectando a la base de datos...")
        conector = ConectorSUA(password='S5@N52V49')
        if not conector.conectar():
            print("Error al conectar a la base de datos")
            return False
            
        # Datos del empleado a dar de baja
        reg_patr = "E5352621109"
        num_afil = "04088625456"  
        
        # 1. Verificar estado actual del empleado en Asegura
        print(f"\nVerificando estado actual del empleado: {reg_patr} - {num_afil}")
        cursor = conector.conn.cursor()
        query = f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
        cursor.execute(query)
        
        # Obtener columnas
        columnas = [column[0] for column in cursor.description]
        
        # Obtener datos
        row = cursor.fetchone()
        if not row:
            print(f"No se encontró el empleado {num_afil} en el registro patronal {reg_patr}")
            return False
        
        # Convertir a diccionario
        asegurado = {columnas[i]: value for i, value in enumerate(row)}
        print("Datos actuales en Asegura:")
        for k, v in asegurado.items():
            if k in ['REG_PATR', 'NUM_AFIL', 'NOM_ASEG', 'FEC_ALT', 'FEC_BAJ', 'TIP_DSC', 'VAL_DSC', 'FEC_DSC', 'Fec_FinDsc', 'Num_Cre']:
                print(f"  {k}: {v}")
        
        # 2. Contar bajas previas
        query = f"""
        SELECT COUNT(*) as total_bajas
        FROM Movtos
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' AND TIP_MOVS = '02'
        """
        cursor.execute(query)
        total_bajas = cursor.fetchone()[0]
        print(f"\nTotal de bajas previas: {total_bajas}")
        
        # 3. Verificar si tiene crédito Infonavit
        tiene_credito = asegurado.get('TIP_DSC') is not None and asegurado.get('TIP_DSC').strip() != ''
        print(f"¿Tiene crédito Infonavit?: {'Sí' if tiene_credito else 'No'}")
        if tiene_credito:
            print(f"  Número de crédito: {asegurado.get('Num_Cre')}")
            print(f"  Tipo de descuento: {asegurado.get('TIP_DSC')}")
            print(f"  Valor de descuento: {asegurado.get('VAL_DSC')}")
        
        # 4. Procesar la baja
        fecha_baja = datetime.now().strftime("%Y-%m-%d")
        print(f"\nProcesando baja con fecha: {fecha_baja}")
        
        datos_baja = {
            'REG_PATR': reg_patr,
            'NUM_AFIL': num_afil,
            'FEC_INIC': fecha_baja
        }
        
        # Ejecutar la función de baja
        resultado = conector.procesar_baja(datos_baja)
        print(f"Resultado de la baja: {'Éxito' if resultado else 'Error'}")
        
        if resultado:
            # 5. Verificar cambios en Asegura
            cursor.execute(query)
            row = cursor.fetchone()
            asegurado_post = {columnas[i]: value for i, value in enumerate(row)}
            
            print("\nDatos en Asegura después de la baja:")
            for k, v in asegurado_post.items():
                if k in ['REG_PATR', 'NUM_AFIL', 'NOM_ASEG', 'FEC_ALT', 'FEC_BAJ', 'TIP_DSC', 'VAL_DSC', 'FEC_DSC', 'Fec_FinDsc', 'Num_Cre']:
                    print(f"  {k}: {v}")
            
            # Verificar si se actualizó la fecha de baja
            if asegurado_post.get('FEC_BAJ') == fecha_baja:
                print("✅ Fecha de baja actualizada correctamente")
            else:
                print(f"❌ Fecha de baja no actualizada correctamente. Valor actual: {asegurado_post.get('FEC_BAJ')}")
            
            # Verificar si se actualizó la fecha de fin de descuento (si aplica)
            if tiene_credito:
                if asegurado_post.get('Fec_FinDsc') == fecha_baja:
                    print("✅ Fecha fin descuento actualizada correctamente")
                else:
                    print(f"❌ Fecha fin descuento no actualizada correctamente. Valor actual: {asegurado_post.get('Fec_FinDsc')}")
            
            # 6. Verificar movimientos generados
            print("\nVerificando movimientos generados:")
            
            # Contar bajas nuevas
            cursor.execute(query)
            total_bajas_nuevas = cursor.fetchone()[0]
            print(f"Total de bajas después del proceso: {total_bajas_nuevas}")
            
            if total_bajas_nuevas > total_bajas:
                print("✅ Se incrementó correctamente el contador de bajas")
            else:
                print("❌ No se incrementó el contador de bajas")
            
            # Buscar los nuevos movimientos
            query = f"""
            SELECT * 
            FROM Movtos 
            WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' AND FEC_INIC = '{fecha_baja}'
            ORDER BY TIP_MOVS
            """
            cursor.execute(query)
            
            # Obtener columnas
            columnas = [column[0] for column in cursor.description]
            
            # Obtener datos
            movimientos_nuevos = []
            for row in cursor.fetchall():
                mov = {columnas[i]: value for i, value in enumerate(row)}
                movimientos_nuevos.append(mov)
            
            if movimientos_nuevos:
                print(f"\nSe encontraron {len(movimientos_nuevos)} movimientos nuevos:")
                for mov in movimientos_nuevos:
                    tipo_mov = mov.get('TIP_MOVS', '')
                    desc_tipo = "Baja" if tipo_mov == '02' else "Fin crédito" if tipo_mov == '16' else f"Tipo {tipo_mov}"
                    print(f"  - {desc_tipo} con fecha {mov.get('FEC_INIC')}")
                    
                    # Verificar TIP_INC para la baja
                    if tipo_mov == '02':
                        tip_inc = mov.get('TIP_INC')
                        if str(tip_inc) == str(total_bajas + 1):
                            print(f"    ✅ TIP_INC correcto: {tip_inc}")
                        else:
                            print(f"    ❌ TIP_INC incorrecto. Valor: {tip_inc}, esperado: {total_bajas + 1}")
            else:
                print("❌ No se encontraron movimientos nuevos")
        
        # Desconectar
        conector.desconectar()
        return resultado
            
    except Exception as e:
        print(f"Error durante la prueba: {e}")
        return False

if __name__ == "__main__":
    probar_baja() 