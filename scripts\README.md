# Scripts para Carga Masiva

Este directorio contiene scripts para facilitar la carga masiva de datos en la base de datos SUA.

## Archivos principales

### carga_masiva_patrones.py
Este script permite la carga masiva de patrones y sus primas de RT desde un archivo Excel. El script maneja automáticamente las relaciones entre tablas y proporciona valores por defecto en caso de que no se encuentren coincidencias.

#### Funcionalidades principales:
- Carga de patrones desde Excel
- Validación de datos
- Inserción en tablas Patron y Prima_RT
- Manejo automático de relaciones con tablas Estados y Subdelega
- Generación de logs y reportes de errores

#### Uso básico:
```python
from scripts.carga_masiva_patrones import CargaMasivaPatrones

# Inicializar el cargador
cargador = CargaMasivaPatrones(ruta_bd='C:\\Cobranza\\SUA\\SUA.MDB', password='S5@N52V49')

# Cargar patrones desde un archivo Excel
cargador.cargar_patrones_desde_excel('ruta_archivo.xlsx')
```

### probar_carga_patrones.py
Script para probar la funcionalidad de carga masiva con datos de prueba generados automáticamente.

#### Funcionalidades:
- Genera un archivo Excel de prueba con datos aleatorios
- Ejecuta la carga masiva sobre el archivo generado
- Muestra estadísticas del proceso

#### Ejecución:
```
python probar_carga_patrones.py
```

## Campos Importantes

### Tabla Patron
- **REG_PAT**: Registro patronal (11 caracteres, único)
- **ENT_PAT**: ID de la entidad federativa (relacionado con tabla Estados)
- **DEL_PAT**: ID de la subdelegación (relacionado con tabla Subdelega)
- **NUM_SUB**: Posición numérica de la subdelegación
- **INI_AFIL**: Fecha de afiliación inicial en formato "AAAAMM"

### Tabla Prima_RT
- **REG_PAT**: Registro patronal (relacionado con tabla Patron)
- **PER_PRI**: Período de prima en formato "AAAAMM"
- **TASA_RT**: Tasa de riesgo de trabajo (decimal)

## Manejo de Errores

El sistema registra los errores en archivos de log dentro del directorio `resultados/`. Los errores incluyen:
- Problemas de conexión a la base de datos
- Registros duplicados
- Valores faltantes o inválidos
- Errores al buscar IDs relacionados

## Requisitos

- Python 3.7+
- pandas
- pyodbc (o pypyodbc)
- openpyxl

## Notas Adicionales

- El script utiliza valores por defecto (ENT_PAT = 20, DEL_PAT = 2153) cuando no se encuentran coincidencias en las tablas relacionadas.
- Se recomienda realizar pruebas con un conjunto pequeño de datos antes de proceder a cargas masivas reales. 