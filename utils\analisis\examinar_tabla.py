import pyodbc
import os

def examinar_tabla(nombre_tabla):
    # Configuración de la conexión a Access
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    password = "S5@N52V49"
    ruta_normalizada = os.path.normpath(ruta_bd)
    
    conn_str = (
        r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
        f'DBQ={ruta_normalizada};'
        f'PWD={password};'
    )
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Obtener información de la tabla
        cursor.execute(f"SELECT * FROM {nombre_tabla} WHERE 1=0")  # Solo estructura
        columnas = [column[0] for column in cursor.description]
        
        print(f"\nEstructura de la tabla {nombre_tabla}:")
        print("-" * 50)
        for columna in columnas:
            print(f"Columna: {columna}")
            
        # Obtener algunos registros de ejemplo
        cursor.execute(f"SELECT TOP 5 * FROM {nombre_tabla}")
        registros = cursor.fetchall()
        
        print("\nRegistros de ejemplo:")
        print("-" * 50)
        for registro in registros:
            print(registro)
            
    except Exception as e:
        print(f"Error al examinar la tabla: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        examinar_tabla(sys.argv[1])
    else:
        print("Por favor especifique el nombre de la tabla como argumento") 