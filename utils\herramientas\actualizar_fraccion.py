import pyodbc
import sys

def actualizar_fraccion(reg_pat, nueva_fraccion):
    try:
        # Conexión a la base de datos
        conn = pyodbc.connect(
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49'
        )
        
        cursor = conn.cursor()
        
        # Verificar que el patrón existe
        cursor.execute('SELECT REG_PAT, Fraccion FROM Patron WHERE REG_PAT = ?', (reg_pat,))
        patron = cursor.fetchone()
        
        if not patron:
            print(f"Error: No se encontró el patrón con registro {reg_pat}")
            conn.close()
            return False
        
        # Mostrar valor actual
        print(f"Patrón encontrado: {reg_pat}")
        print(f"Valor actual de Fraccion: {patron.Fraccion if patron.Fraccion else 'NULL'}")
        
        # Ejecutar la actualización
        cursor.execute(
            'UPDATE Patron SET Fraccion = ? WHERE REG_PAT = ?', 
            (nueva_fraccion, reg_pat)
        )
        conn.commit()
        
        # Verificar la actualización
        cursor.execute('SELECT REG_PAT, Fraccion FROM Patron WHERE REG_PAT = ?', (reg_pat,))
        patron_actualizado = cursor.fetchone()
        print(f"Actualización exitosa. Nuevo valor de Fraccion: {patron_actualizado.Fraccion}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error durante la actualización: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Uso: python actualizar_fraccion.py <REG_PAT> <NUEVA_FRACCION>")
        print("Ejemplo: python actualizar_fraccion.py E5352621109 043")
        sys.exit(1)
    
    reg_pat = sys.argv[1]
    nueva_fraccion = sys.argv[2]
    
    actualizar_fraccion(reg_pat, nueva_fraccion) 