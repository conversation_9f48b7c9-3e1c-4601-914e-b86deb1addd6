#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para probar la actualización de fracciones de patrones en la base de datos SUA
"""

import os
import sys
import pandas as pd
import random
from pathlib import Path

# Asegurar que los módulos sean accesibles
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

# Importar el conector personalizado
from conectores.conector_sua import ConectorSUA

def main():
    """Función principal"""
    # Ruta por defecto de la base de datos SUA
    ruta_bd_default = "C:\\Cobranza\\SUA\\SUA.MDB"
    
    # Solicitar la ruta de la base de datos
    print("\n=== PRUEBA DE ACTUALIZACIÓN DE FRACCIONES ===")
    print("Este script actualizará la fracción de patrones existentes en la base de datos SUA.")
    ruta_bd = input(f"Ruta de la base de datos SUA [{ruta_bd_default}]: ") or ruta_bd_default
    
    # Verificar si la ruta existe
    if not os.path.exists(ruta_bd):
        print(f"Error: La ruta {ruta_bd} no existe.")
        return
    
    # Conectar a la base de datos
    print("\nConectando a la base de datos...")
    conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
    
    if not conector.conectar():
        print("Error: No se pudo conectar a la base de datos.")
        return
    
    # Obtener los registros patronales existentes
    print("Obteniendo registros patronales...")
    query = "SELECT REG_PAT, Fraccion FROM Patron"
    patrones = conector.ejecutar_consulta(query)
    
    if not patrones:
        print("No se encontraron patrones en la base de datos.")
        conector.desconectar()
        return
    
    # Mostrar información de los patrones
    print(f"\nSe encontraron {len(patrones)} patrones en la base de datos.")
    
    # Crear directorio de resultados si no existe
    directorio_resultados = Path("resultados")
    directorio_resultados.mkdir(exist_ok=True)
    
    # Crear un DataFrame con los registros patronales
    df_patrones = pd.DataFrame(patrones)
    
    # Crear un archivo Excel con los registros patronales y fracciones aleatorias
    excel_path = directorio_resultados / "actualizacion_fracciones_test.xlsx"
    
    # Seleccionar algunos patrones al azar para actualizar (máximo 10)
    if len(patrones) > 10:
        patrones_seleccionados = random.sample(patrones, 10)
    else:
        patrones_seleccionados = patrones
    
    # Crear DataFrame para actualización
    data = []
    fracciones = ["01", "02", "03", "04", "05", "10", "11", "15", "20", "25", "30", "35", "40", "45"]
    
    for patron in patrones_seleccionados:
        # Seleccionar una fracción aleatoria diferente a la actual
        fraccion_actual = patron["Fraccion"] if "Fraccion" in patron else "00"
        fracciones_disponibles = [f for f in fracciones if f != fraccion_actual]
        nueva_fraccion = random.choice(fracciones_disponibles) if fracciones_disponibles else "00"
        
        data.append({
            "Registro Patronal": patron["REG_PAT"],
            "Fracción Actual": fraccion_actual,
            "Fracción": nueva_fraccion
        })
    
    df_actualizacion = pd.DataFrame(data)
    
    # Guardar DataFrame en Excel
    df_actualizacion.to_excel(excel_path, index=False)
    print(f"\nSe ha creado un archivo Excel con {len(df_actualizacion)} patrones para actualizar:")
    print(f"Ruta: {excel_path}")
    
    # Preguntar al usuario si desea realizar la actualización
    while True:
        respuesta = input("\n¿Desea realizar la actualización de fracciones? (s/n): ").lower()
        if respuesta in ["s", "n"]:
            break
    
    if respuesta == "s":
        print("\nActualizando fracciones...")
        
        # Realizar la actualización
        actualizaciones_exitosas = 0
        errores = 0
        
        for idx, row in df_actualizacion.iterrows():
            reg_pat = row["Registro Patronal"]
            fraccion = row["Fracción"]
            fraccion_actual = row["Fracción Actual"]
            
            try:
                # Preparar consulta de actualización
                consulta = f"""
                UPDATE Patron 
                SET Fraccion = '{fraccion}'
                WHERE REG_PAT = '{reg_pat}'
                """
                
                print(f"Actualizando {reg_pat}: {fraccion_actual} -> {fraccion}...")
                
                # Ejecutar la actualización
                cursor = conector.conn.cursor()
                cursor.execute(consulta)
                conector.conn.commit()
                
                # Verificar si se actualizó correctamente
                consulta_verificacion = f"SELECT Fraccion FROM Patron WHERE REG_PAT = '{reg_pat}'"
                resultado = conector.ejecutar_consulta(consulta_verificacion)
                
                if resultado and resultado[0]["Fraccion"] == fraccion:
                    print(f"✓ Se actualizó correctamente el patrón {reg_pat}.")
                    actualizaciones_exitosas += 1
                else:
                    print(f"✗ La actualización no se reflejó correctamente para el patrón {reg_pat}.")
                    errores += 1
                
            except Exception as e:
                print(f"✗ Error al actualizar el patrón {reg_pat}: {e}")
                errores += 1
        
        # Mostrar resumen
        print("\n=== RESUMEN ===")
        print(f"Actualizaciones exitosas: {actualizaciones_exitosas}")
        print(f"Errores: {errores}")
    
    else:
        print("Operación cancelada por el usuario.")
    
    # Cerrar conexión
    conector.desconectar()
    print("\nConexión a la base de datos cerrada.")
    print("Fin del proceso.")

if __name__ == "__main__":
    main() 