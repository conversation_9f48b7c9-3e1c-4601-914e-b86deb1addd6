from conectores.conector_sua import ConectorSUA

def probar_conexion():
    try:
        print("Intentando conectar con password='S5@N52V49'...")
        conn = ConectorSUA(password='S5@N52V49')
        resultado = conn.conectar()
        print(f"Resultado de la conexión: {resultado}")
        
        if resultado:
            print("¡Conexión exitosa!")
            
            # Listar las tablas disponibles
            tablas = conn.listar_tablas()
            print(f"\nTablas disponibles: {tablas[:10]}")
            
            # Examinar estructura de tabla Asegura
            print("\n--- Estructura de la tabla Asegura ---")
            estructura_asegura = conn.obtener_estructura_tabla("Asegura")
            for col in estructura_asegura:
                print(f"- {col}")
                
            # Obtener algunos registros de ejemplo de Asegura
            print("\n--- Ejemplos de registros en Asegura ---")
            datos_asegura = conn.obtener_datos_tabla("Asegura", limite=3)
            print(datos_asegura)
            
            # Examinar estructura de tabla Movtos
            print("\n--- Estructura de la tabla Movtos ---")
            estructura_movtos = conn.obtener_estructura_tabla("Movtos")
            for col in estructura_movtos:
                print(f"- {col}")
            
            # Contar registros en tabla Movtos
            consulta_conteo = "SELECT COUNT(*) AS Total FROM Movtos"
            conteo = conn.ejecutar_consulta(consulta_conteo)
            print(f"\nTotal de registros en Movtos: {conteo[0]['Total'] if conteo else 'Error'}")
            
            # Desconectar
            conn.desconectar()
            print("\nConexión cerrada correctamente.")
        else:
            print("La conexión falló.")
        
        return resultado
    except Exception as e:
        print(f"Error durante la conexión: {e}")
        return False

if __name__ == "__main__":
    probar_conexion() 