import pandas as pd
import os

# Crear un registro de prueba para el movimiento tipo 19
nuevo_registro = {
    'REG_PATR': 'E5352621109',
    'NUM_AFIL': '04088625456',
    'NOMBRE': 'EMPLEADO PRUEBA',
    'FECHA_MOVIMIENTO': '2025-04-20',
    'TIPO_MOVIMIENTO': '19',
    'VALOR_DESCUENTO': 2400
}

# Crear el DataFrame y guardar como Excel
df = pd.DataFrame([nuevo_registro])
ruta_archivo = 'pruebas/test_mov19.xlsx'

# Asegurar que el directorio existe
os.makedirs('pruebas', exist_ok=True)

# Guardar el archivo
df.to_excel(ruta_archivo, index=False)
print(f'Archivo creado en {ruta_archivo}') 