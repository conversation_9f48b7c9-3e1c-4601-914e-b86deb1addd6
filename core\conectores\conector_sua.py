"""
Módulo para conectar y gestionar la base de datos SUA.MDB.
Proporciona una clase reutilizable para acceder a las tablas de SUA.
"""

import os
import pandas as pd
import pyodbc
import traceback
from typing import List, Dict, Optional, Any, Tuple, Union
from datetime import datetime
import logging

class ConectorSUA:
    """Clase para la conexión y gestión de la base de datos SUA.MDB"""
    
    def __init__(self, ruta_bd=None, password=None):
        """
        Inicializa el conector a la base de datos SUA.
        
        Args:
            ruta_bd: Ruta a la base de datos SUA.
            password: Contraseña de la base de datos (si tiene).
        """
        # Normalizar la ruta para evitar problemas con secuencias de escape
        self.ruta_bd = os.path.normpath(ruta_bd) if ruta_bd else r'C:\Cobranza\SUA\SUA.MDB'
        self.password = password
        self.conn = None
        self._tablas_cache = None
    
    def conectar(self) -> bool:
        """
        Establece la conexión con la base de datos SUA.MDB.
        
        Returns:
            bool: True si la conexión fue exitosa, False en caso contrario.
        """
        try:
            # Verificar si el archivo existe
            if not os.path.exists(self.ruta_bd):
                print(f"Error: No se encontró la base de datos en {self.ruta_bd}")
                return False
            
            # Normalizar la ruta para evitar problemas con secuencias de escape
            ruta_normalizada = os.path.normpath(self.ruta_bd)
            
            # Establecer la conexión
            if self.password:
                # Si hay contraseña, la incluimos en la cadena de conexión
                connection_str = (
                    r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                    f'DBQ={ruta_normalizada};'
                    f'PWD={self.password};'
                )
            else:
                # Intento de conexión sin contraseña
                connection_str = (
                    r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                    f'DBQ={ruta_normalizada};'
                    'ReadOnly=0;Exclusive=0;'  # Modo no exclusivo, sólo lectura
                )
            
            self.conn = pyodbc.connect(connection_str)
            print(f"Conexión exitosa a la base de datos: {self.ruta_bd}")
            return True
        
        except pyodbc.Error as e:
            # Si el error es por contraseña, dar un mensaje más específico
            if "No es una contraseña válida" in str(e):
                print("Error: La base de datos está protegida por contraseña. Por favor proporcione la contraseña correcta.")
                print(f"Detalles del error: {e}")
            elif "Could not find" in str(e) or "No se pudo encontrar" in str(e):
                print(f"Error: No se pudo encontrar el controlador ODBC para Access. Verifique que esté instalado.")
                print(f"Detalles del error: {e}")
            else:
                print(f"Error al conectar a la base de datos: {e}")
            return False
    
    def desconectar(self) -> None:
        """Cierra la conexión con la base de datos"""
        if self.conn:
            self.conn.close()
            self.conn = None
    
    def listar_tablas(self) -> List[str]:
        """
        Lista todas las tablas en la base de datos.
        
        Returns:
            List[str]: Lista de nombres de tablas.
        """
        if not self.conn and not self.conectar():
            return []
        
        # Si ya tenemos las tablas en caché, las devolvemos
        if self._tablas_cache:
            return self._tablas_cache
        
        cursor = self.conn.cursor()
        tablas = []
        
        # En MS Access, la consulta para obtener tablas es diferente
        for row in cursor.tables(tableType='TABLE'):
            tablas.append(row.table_name)
        
        # Guardamos en caché para futuras consultas
        self._tablas_cache = tablas
        return tablas
    
    def obtener_estructura_tabla(self, nombre_tabla: str) -> List[str]:
        """
        Obtiene la estructura (columnas) de una tabla específica.
        
        Args:
            nombre_tabla: Nombre de la tabla.
            
        Returns:
            List[str]: Lista de nombres de columnas.
        """
        if not self.conn and not self.conectar():
            return []
        
        cursor = self.conn.cursor()
        columnas = []
        
        try:
            # Ejecutar una consulta que no devuelve datos para obtener las columnas
            cursor.execute(f"SELECT TOP 0 * FROM [{nombre_tabla}]")
            columnas = [column[0] for column in cursor.description]
        except pyodbc.Error as e:
            print(f"Error al obtener estructura de la tabla {nombre_tabla}: {e}")
        
        return columnas
    
    def obtener_datos_tabla(self, nombre_tabla: str, limite: int = 100, 
                           where: str = None, order_by: str = None) -> pd.DataFrame:
        """
        Obtiene los registros de una tabla.
        
        Args:
            nombre_tabla: Nombre de la tabla.
            limite: Número máximo de registros a obtener.
            where: Condición WHERE para filtrar (sin la palabra 'WHERE').
            order_by: Campo para ordenar los resultados (sin 'ORDER BY').
            
        Returns:
            DataFrame: DataFrame con los registros obtenidos.
        """
        if not self.conn and not self.conectar():
            return pd.DataFrame()
        
        try:
            # Construir la consulta SQL
            query = f"SELECT TOP {limite} * FROM [{nombre_tabla}]"
            
            if where:
                query += f" WHERE {where}"
            
            if order_by:
                query += f" ORDER BY {order_by}"
            
            # Usar pandas para leer directamente la tabla
            df = pd.read_sql(query, self.conn)
            return df
        except Exception as e:
            print(f"Error al obtener datos de la tabla {nombre_tabla}: {e}")
            return pd.DataFrame()
    
    def ejecutar_consulta(self, query, params=None):
        """Ejecuta una consulta SQL y devuelve los resultados como una lista de diccionarios"""
        if not self.conn:
            print("No hay conexión a la base de datos")
            return []
        
        try:
            cursor = self.conn.cursor()
            
            # Para MS Access, necesitamos usar adecuadamente los parámetros
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description] if cursor.description else []
            
            # Convertir a lista de diccionarios
            resultados = []
            for row in cursor.fetchall():
                # Convertir valores None a 0 para campos numéricos
                row_dict = {}
                for i, value in enumerate(row):
                    if value is None:
                        # Si es una columna numérica, usar 0
                        if cursor.description[i][1] in [3, 4, 5]:  # Códigos para tipos numéricos
                            row_dict[columnas[i]] = 0
                        else:
                            row_dict[columnas[i]] = None
                    else:
                        row_dict[columnas[i]] = value
                resultados.append(row_dict)
            
            cursor.close()
            return resultados
        
        except Exception as e:
            print(f"Error al ejecutar consulta: {e}")
            print(f"Query: {query}")
            if params:
                print(f"Params: {params}")
            return []
    
    def obtener_metadata_tabla(self, nombre_tabla: str) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene metadatos detallados sobre las columnas de una tabla.
        
        Args:
            nombre_tabla: Nombre de la tabla.
            
        Returns:
            Dict: Diccionario con información detallada sobre cada columna.
        """
        if not self.conn and not self.conectar():
            return {}
        
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT TOP 0 * FROM [{nombre_tabla}]")
            
            metadata = {}
            for column in cursor.description:
                # column contiene: name, type_code, display_size, internal_size, precision, scale, null_ok
                metadata[column[0]] = {
                    'tipo': column[1],
                    'tamaño_mostrado': column[2],
                    'tamaño_interno': column[3],
                    'precision': column[4],
                    'escala': column[5],
                    'permite_nulos': column[6]
                }
            
            return metadata
        except Exception as e:
            print(f"Error al obtener metadatos de la tabla {nombre_tabla}: {e}")
            return {}
    
    def existe_tabla(self, nombre_tabla: str) -> bool:
        """
        Verifica si existe una tabla con el nombre dado.
        
        Args:
            nombre_tabla: Nombre de la tabla a verificar.
            
        Returns:
            bool: True si la tabla existe, False en caso contrario.
        """
        tablas = self.listar_tablas()
        return nombre_tabla in tablas
    
    def __enter__(self):
        """Para usar con 'with' statement"""
        self.conectar()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Para usar con 'with' statement"""
        self.desconectar()
    
    def insertar_movimiento(self, datos_mov):
        """
        Inserta un movimiento en la tabla Movtos.
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
        
        Returns:
            bool: True si la inserción fue exitosa, False en caso contrario.
        """
        try:
            # Validar campos requeridos
            campos_requeridos = ['REG_PATR', 'NUM_AFIL', 'TIP_MOVS', 'FEC_INIC']
            for campo in campos_requeridos:
                if campo not in datos_mov:
                    raise ValueError(f"Falta el campo requerido: {campo}")
            
            # Asegurar que NUM_AFIL tenga 11 dígitos
            datos_mov['NUM_AFIL'] = str(datos_mov['NUM_AFIL']).zfill(11)
            
            # Construir la sentencia SQL sin parámetros
            # Para evitar problemas con los tipos de datos
            
            # Transformar Tip_Des a formato numérico si es texto
            if 'Tip_Des' in datos_mov:
                tip_des = datos_mov['Tip_Des']
                if isinstance(tip_des, str):
                    if tip_des.lower() == 'cuota fija' or tip_des == '2':
                        datos_mov['Tip_Des'] = 2
                    elif tip_des.lower() == 'porcentaje' or tip_des == '1':
                        datos_mov['Tip_Des'] = 1
                    elif tip_des.lower() == 'factor de descuento' or tip_des == '3':
                        datos_mov['Tip_Des'] = 3
                    elif tip_des == '':
                        datos_mov['Tip_Des'] = None
            
            # Construir la parte de los campos
            campos = []
            valores = []
            
            # Agregar campos obligatorios
            campos.append("REG_PATR")
            valores.append(f"'{datos_mov['REG_PATR']}'")
            
            campos.append("NUM_AFIL")
            valores.append(f"'{datos_mov['NUM_AFIL']}'")
            
            campos.append("TIP_MOVS")
            valores.append(f"'{datos_mov['TIP_MOVS']}'")
            
            # Convertir fecha al formato de Access (MM/DD/YYYY)
            fecha_dt = pd.to_datetime(datos_mov['FEC_INIC'])
            fecha_str = fecha_dt.strftime('%m/%d/%Y')
            campos.append("FEC_INIC")
            valores.append(f"#{fecha_str}#")
            
            # Agregar campos opcionales si están presentes
            for campo, valor in datos_mov.items():
                if campo not in ['REG_PATR', 'NUM_AFIL', 'TIP_MOVS', 'FEC_INIC']:
                    campos.append(campo)
                    
                    # Manejar diferentes tipos de campos
                    if campo in ['CVE_MOVS', 'ART_33', 'Num_Cre']:
                        if valor is None or valor == '':
                            valores.append("NULL")
                        else:
                            valores.append(f"'{valor}'")
                    # Manejo especial de Tip_Des
                    elif campo == 'Tip_Des':
                        if valor is None or valor == '':
                            valores.append("NULL")
                        else:
                            # Tip_Des debe ser texto cuando es movimiento 16, 17 o 18
                            if datos_mov['TIP_MOVS'] in ['16', '17', '18', '19', '20']:
                                # Convertir a formato descriptivo como en movimiento 15
                                if valor == 1 or valor == '1' or (isinstance(valor, str) and 'porcentaje' in valor.lower()):
                                    valores.append("'Porcentaje'")
                                elif valor == 2 or valor == '2' or (isinstance(valor, str) and 'cuota' in valor.lower() and 'fija' in valor.lower()):
                                    valores.append("'Cuota Fija'")
                                elif valor == 3 or valor == '3' or (isinstance(valor, str) and any(term in valor.lower() for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo'])):
                                    valores.append("'Factor de Descuento'")
                                else:
                                    # Si es un valor no reconocido, intentamos mantenerlo como texto
                                    valores.append(f"'{valor}'")
                            else:
                                # Para otros movimientos, mantener como valor numérico
                                valores.append(f"{valor}")
                    # Manejo del valor de disminución y otros campos numéricos
                    elif campo in ['SAL_MOVT', 'SAL_MOVT2', 'SAL_MOVT3', 'SAL_ANT1', 'SAL_ANT2', 'SAL_ANT3', 'NUM_DIAS', 'Val_Des', 'EDO_MOV', 'Tab_Dism']:
                        if valor is None:
                            valores.append("0")
                        else:
                            valores.append(str(valor))
                    else:
                        # Para otros campos no especificados, intentamos adivinar el tipo
                        if valor is None:
                            valores.append("NULL")
                        elif isinstance(valor, (int, float)):
                            valores.append(str(valor))
                        else:
                            valores.append(f"'{valor}'")
            
            # Construir la sentencia SQL completa
            campos_str = ", ".join(campos)
            valores_str = ", ".join(valores)
            
            sql = f"INSERT INTO Movtos ({campos_str}) VALUES ({valores_str})"
            
            print(f"DEBUG: SQL a ejecutar: {sql}")
            
            # Ejecutar la consulta
            cursor = self.conn.cursor()
            cursor.execute(sql)
            self.conn.commit()
            
            return True
            
        except Exception as e:
            print(f"❌ Error al insertar movimiento: {str(e)}")
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Error adicional durante rollback: {rollback_error}")
            return False
    
    def procesar_baja(self, datos_mov):
        """
        Procesa una baja de trabajador, actualizando la tabla Asegura y creando 
        los registros correspondientes en la tabla Movtos.
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - FEC_INIC: Fecha de la baja
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            # Validar campos requeridos
            campos_requeridos = ['REG_PATR', 'NUM_AFIL', 'FEC_INIC']
            for campo in campos_requeridos:
                if campo not in datos_mov:
                    raise ValueError(f"Falta el campo requerido: {campo}")
            
            # Asegurar que NUM_AFIL tenga 11 dígitos
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            reg_patr = datos_mov['REG_PATR']
            fecha_baja = datos_mov['FEC_INIC']
            
            print(f"DEBUG: Procesando baja - REG_PATR={reg_patr}, NUM_AFIL={num_afil}, FECHA={fecha_baja}")
            
            # 1. Verificar si el asegurado existe
            cursor = self.conn.cursor()
            
            # Comprobar si existe usando consulta parametrizada
            query = "SELECT * FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(query, (reg_patr, num_afil))
            row = cursor.fetchone()
            
            if not row:
                raise ValueError(f"No se encontró el asegurado {num_afil} en el registro patronal {reg_patr}")
            
            # 2. Verificar si el último movimiento es una baja
            query_ultimo_mov = """
            SELECT TOP 1 TIP_MOVS, FEC_INIC 
            FROM Movtos 
            WHERE REG_PATR = ? AND NUM_AFIL = ? 
            ORDER BY FEC_INIC DESC
            """
            cursor.execute(query_ultimo_mov, (reg_patr, num_afil))
            ultimo_mov = cursor.fetchone()
            
            if ultimo_mov and ultimo_mov[0] == '02':  # Si el último movimiento es una baja
                mensaje_error = f"No se puede dar de baja al asegurado {num_afil} porque su último movimiento ya es una baja del {ultimo_mov[1]}. Debe procesar un reingreso antes de dar de baja nuevamente."
                print(f"ERROR: {mensaje_error}")
                raise ValueError(mensaje_error)
            
            # 3. Verificar si tiene crédito Infonavit
            columnas = [column[0] for column in cursor.description]
            num_credito = None
            tip_desc = None
            val_desc = None
            tiene_credito = False
            
            # Obtener índice de las columnas relevantes
            try:
                indice_tip_desc = columnas.index('TIP_DSC')
                indice_val_desc = columnas.index('VAL_DSC')
                indice_num_cre = columnas.index('Num_Cre')
                
                # Comprobar si tiene crédito (TIP_DSC no vacío)
                if row[indice_tip_desc] and str(row[indice_tip_desc]).strip() != '':
                    tiene_credito = True
                    tip_desc = row[indice_tip_desc]
                    val_desc = row[indice_val_desc]
                    
                    # Obtener número crédito si existe
                    if row[indice_num_cre] and str(row[indice_num_cre]).strip() != '':
                        num_credito = row[indice_num_cre]
                
                print(f"DEBUG: Asegurado tiene crédito: {tiene_credito}, num_credito: {num_credito}, tip_desc: {tip_desc}, val_desc: {val_desc}")
            
            except (ValueError, IndexError) as e:
                print(f"Advertencia al verificar crédito: {e}")
                print(f"Columnas disponibles: {columnas}")
                tiene_credito = False
            
            # 4. Determinar el número de baja (TIP_INC) contando bajas anteriores
            query = "SELECT COUNT(*) FROM Movtos WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '02'"
            cursor.execute(query, (reg_patr, num_afil))
            count_bajas = cursor.fetchone()[0]
            
            # El nuevo número será el conteo anterior + 1
            num_baja = count_bajas + 1
            print(f"DEBUG: Número de baja calculado: {num_baja}")
            
            # 5. Actualizar la tabla Asegura con la fecha de baja
            # En Access, las fechas van entre #fecha#
            query = "UPDATE Asegura SET FEC_BAJ = #" + fecha_baja + "# WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(query, (reg_patr, num_afil))
            
            # 6. Si tiene crédito Infonavit, crear movimiento tipo 16 (fin crédito)
            if tiene_credito and num_credito:
                datos_credito = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '16',  # Fin de crédito
                    'FEC_INIC': fecha_baja,
                    'CVE_MOVS': 'G',
                    'EDO_MOV': 0,
                    'ART_33': 'N',
                    'Num_Cre': num_credito,
                    'Tip_Des': tip_desc,
                    'Val_Des': val_desc if val_desc else 0,
                    'SAL_MOVT2': 0.0,
                    'SAL_MOVT3': 0.0,
                    'SAL_ANT1': 0.0,
                    'SAL_ANT2': 0.0,
                    'SAL_ANT3': 0.0
                }
                
                print(f"DEBUG: Insertando movimiento fin crédito: {datos_credito}")
                if not self.insertar_movimiento(datos_credito):
                    raise Exception("Error al insertar movimiento de fin de crédito")
            
            # 7. Crear movimiento tipo 02 (baja) con el número secuencial de baja
            datos_baja = {
                'REG_PATR': reg_patr,
                'NUM_AFIL': num_afil,
                'TIP_MOVS': '02',  # Baja
                'FEC_INIC': fecha_baja,
                'CVE_MOVS': 'G',
                'EDO_MOV': 0,
                'ART_33': 'N',
                'SAL_MOVT': 0,
                'SAL_MOVT2': 0,
                'SAL_MOVT3': 0,
                'TIP_INC': str(num_baja)  # Número secuencial de baja
            }
            
            print(f"DEBUG: Insertando movimiento baja: {datos_baja}")
            
            if not self.insertar_movimiento(datos_baja):
                raise Exception("Error al insertar movimiento de baja")
            
            # Confirmar transacción
            self.conn.commit()
            print("✅ Baja procesada correctamente")
            return True
            
        except Exception as e:
            print(f"❌ Error al procesar baja: {str(e)}")
            # Intentar rollback
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Error adicional durante rollback: {rollback_error}")
            return False
    
    def procesar_reingreso(self, datos_mov):
        """
        Procesa un reingreso de un trabajador (tipo 02).
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación (NSS)
                - FEC_INIC: Fecha de reingreso
                - TIP_MOVS: Tipo de movimiento (02)
                - SAL_MOVT: Salario al reingresar
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            # Validar campos requeridos
            campos_requeridos = ['REG_PATR', 'NUM_AFIL', 'FEC_INIC', 'TIP_MOVS', 'SAL_MOVT']
            for campo in campos_requeridos:
                if campo not in datos_mov:
                    raise ValueError(f"Falta el campo requerido: {campo}")
            
            # Asegurar formato correcto
            reg_patr = datos_mov['REG_PATR']
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            fecha_reingreso = datos_mov['FEC_INIC']
            salario = float(datos_mov['SAL_MOVT'])
            
            if len(num_afil) != 11:
                raise ValueError(f"El NSS debe tener 11 dígitos, se proporcionó: {num_afil}")
            
            print(f"DEBUG: Procesando reingreso - REG_PATR={reg_patr}, NUM_AFIL={num_afil}, SALARIO={salario}, FECHA={fecha_reingreso}")
            
            # Verificar si el asegurado existe en la base de datos
            cursor = self.conn.cursor()
            check_query = "SELECT COUNT(*) FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(check_query, (reg_patr, num_afil))
            exists = cursor.fetchone()[0] > 0
            
            # Si no existe, manejar como alta nueva
            if not exists:
                print(f"Asegurado {num_afil} no encontrado. Se procederá como alta nueva.")
                # Usar los mismos datos para el alta
                return self.procesar_alta(datos_mov)
            
            # Verificar si está dado de baja (debe estarlo para reingreso)
            status_query = "SELECT EDO_REG FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(status_query, (reg_patr, num_afil))
            estado = cursor.fetchone()[0]
            
            if estado != 'B':
                raise ValueError(f"El asegurado {num_afil} no está dado de baja (estado actual: {estado}). No se puede procesar reingreso.")
            
            # Obtener la fecha de baja para registro histórico
            baja_query = "SELECT FEC_BAJA FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(baja_query, (reg_patr, num_afil))
            fecha_baja = cursor.fetchone()[0]
            
            # Preparar datos del movimiento
            datos_movimiento = {
                'REG_PATR': reg_patr,
                'NUM_AFIL': num_afil,
                'TIP_MOVS': '02',  # Reingreso
                'FEC_INIC': fecha_reingreso,
                'FEC_ANT1': fecha_baja,  # Fecha de baja anterior
                'CVE_MOVS': 'S',
                'SAL_MOVT': salario,
                'EDO_MOV': 0
            }
            
            # Insertar el movimiento en la tabla Movtos
            print(f"DEBUG: Insertando movimiento de reingreso: {datos_movimiento}")
            if not self.insertar_movimiento(datos_movimiento):
                raise Exception("Error al insertar movimiento de reingreso")
            
            # Actualizar el registro del asegurado
            update_query = """
                UPDATE Asegura SET 
                EDO_REG = 'A',       -- Activo
                FEC_ALTA = ?,        -- Fecha de reingreso
                SAL_IMSS = ?,        -- Nuevo salario
                FEC_BAJA = NULL      -- Limpiar fecha de baja
                WHERE REG_PATR = ? AND NUM_AFIL = ?
            """
            cursor.execute(update_query, (fecha_reingreso, salario, reg_patr, num_afil))
            
            # Confirmar la transacción
            self.conn.commit()
            print(f"✅ Reingreso procesado correctamente para {num_afil} con fecha {fecha_reingreso}")
            return True
            
        except Exception as e:
            print(f"❌ Error al procesar reingreso: {str(e)}")
            traceback.print_exc()
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Error adicional durante rollback: {rollback_error}")
            return False
    
    def procesar_credito_infonavit(self, datos_mov):
        """
        Procesa un movimiento de inicio de crédito Infonavit (código 15), actualizando 
        la tabla Asegura y creando el registro correspondiente en la tabla Movtos.
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - FEC_INIC: Fecha de inicio del crédito
                - Tip_Des: Tipo de descuento (Porcentaje, Cuota Fija, Factor de Descuento)
                - Val_Des: Valor del descuento
                - Num_Cre: Número de crédito (opcional)
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            # Validar campos requeridos
            campos_requeridos = ['REG_PATR', 'NUM_AFIL', 'FEC_INIC', 'Tip_Des', 'Val_Des']
            for campo in campos_requeridos:
                if campo not in datos_mov:
                    raise ValueError(f"Falta el campo requerido para crédito: {campo}")
            
            # Asegurar que NUM_AFIL tenga 11 dígitos
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            reg_patr = datos_mov['REG_PATR']
            fecha_credito = datos_mov['FEC_INIC']
            tipo_descuento = datos_mov['Tip_Des']
            valor_descuento = datos_mov['Val_Des']
            
            # Generar número de crédito si no se proporciona
            numero_credito = datos_mov.get('Num_Cre')
            if not numero_credito:
                # Generar un número único basado en NSS + timestamp
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                numero_credito = f"{num_afil}{timestamp[-6:]}"
            
            # 1. Verificar si el asegurado existe y tiene un crédito activo
            cursor = self.conn.cursor()
            
            # Usar consulta parametrizada
            query_info = "SELECT TIP_DSC, VAL_DSC, FEC_DSC, Num_Cre, FEC_BAJ FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            
            print(f"Ejecutando consulta: {query_info}")
            # Probar sin parámetros
            cursor.execute(f"SELECT TIP_DSC, VAL_DSC, FEC_DSC, Num_Cre, FEC_BAJ FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'")
            asegurado_info = cursor.fetchone()
            
            if not asegurado_info:
                raise ValueError(f"No se encontró el asegurado {num_afil} en el registro patronal {reg_patr}")
            
            if asegurado_info.FEC_BAJ:
                raise ValueError(f"El asegurado {num_afil} tiene fecha de baja registrada ({asegurado_info.FEC_BAJ}), no se puede registrar un crédito")
            
            # 2. Actualizar la tabla Asegura con los datos del crédito
            query_update_asegura = """
            UPDATE Asegura
            SET TIP_DSC = ?, VAL_DSC = ?, FEC_DSC = ?, Num_Cre = ?
            WHERE REG_PATR = ? AND NUM_AFIL = ?
            """
            cursor.execute(query_update_asegura, (tipo_descuento, valor_descuento, fecha_credito, numero_credito, reg_patr, num_afil))
            
            # 3. Crear movimiento tipo 15 (inicio crédito)
            datos_credito = {
                'REG_PATR': reg_patr,
                'NUM_AFIL': num_afil,
                'TIP_MOVS': '15',  # Inicio crédito
                'FEC_INIC': fecha_credito,
                'CVE_MOVS': 'D',  # Código para crédito
                'EDO_MOV': 0,
                'Num_Cre': numero_credito,
                'Tip_Des': tipo_descuento,
                'Val_Des': valor_descuento,
                'Tab_Dism': datos_mov.get('Tab_Dism', 0)  # Tabla de disminución, 0 por defecto
            }
            
            print(f"Procesando inicio de crédito Infonavit para asegurado {num_afil} con número {numero_credito}")
            
            if not self.insertar_movimiento(datos_credito):
                raise Exception("Error al insertar movimiento de inicio de crédito")
            
            # Confirmar transacción
            self.conn.commit()
            return True
            
        except Exception as e:
            print(f"Error al procesar inicio de crédito: {str(e)}")
            # Intentar rollback
            try:
                self.conn.rollback()
            except Exception as rollback_error:
                print(f"Error adicional durante rollback: {rollback_error}")
            return False
    
    def procesar_ausencia(self, datos_mov):
        """Procesa un movimiento de ausencia (tipo 11)"""
        try:
            # Validar campos requeridos
            reg_patr = datos_mov.get('REG_PATR')
            num_afil = datos_mov.get('NUM_AFIL')
            fecha_ausencia = datos_mov.get('FEC_INIC')
            num_dias = datos_mov.get('NUM_DIAS')

            if not all([reg_patr, num_afil, fecha_ausencia, num_dias]):
                raise ValueError("Faltan campos requeridos para ausencia")

            # Validar que NUM_AFIL tenga 11 dígitos
            if len(str(num_afil)) != 11:
                raise ValueError("NUM_AFIL debe tener 11 dígitos")

            # Verificar que el asegurado existe y no está dado de baja
            query_asegura = """
            SELECT FEC_BAJ 
            FROM Asegura 
            WHERE REG_PATR = ? AND NUM_AFIL = ?
            """
            cursor = self.conn.cursor()
            cursor.execute(query_asegura, (reg_patr, num_afil))
            resultado = cursor.fetchone()

            if not resultado:
                raise ValueError(f"No se encontró al asegurado {num_afil} en la base de datos")

            if resultado.FEC_BAJ is not None:
                raise ValueError(f"El asegurado {num_afil} ya está dado de baja")

            # Validar que no exceda el límite de 7 días por mes
            if num_dias > 7:
                raise ValueError("El número de días de ausencia no puede ser mayor a 7")

            # Obtener el mes y año de la ausencia
            fecha = pd.to_datetime(fecha_ausencia)
            mes_ano = fecha.strftime('%m/%Y')
            logging.info(f"Validando ausencia para {num_afil} en {mes_ano} con {num_dias} días")

            # Verificar total de días de ausencia en el mes
            query_dias = """
            SELECT SUM(NUM_DIAS) as total_dias
            FROM Movtos
            WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '11'
            AND FORMAT(FEC_INIC, 'mm/yyyy') = ?
            """
            cursor.execute(query_dias, (reg_patr, num_afil, mes_ano))
            resultado = cursor.fetchone()
            total_dias = resultado.total_dias if resultado and resultado.total_dias is not None else 0
            logging.info(f"Días existentes en BD: {total_dias}, Días a agregar: {num_dias}")

            # Validar que la suma no exceda 7 días
            if total_dias + num_dias > 7:
                error_msg = f"El trabajador ya tiene {total_dias} días de ausencia en {mes_ano}. No puede exceder 7 días por mes"
                logging.error(error_msg)
                raise ValueError(error_msg)

            # Verificar número de incidencias en el mes
            query_incidencias = """
            SELECT COUNT(*) as total_incidencias
            FROM Movtos
            WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '11'
            AND FORMAT(FEC_INIC, 'mm/yyyy') = ?
            """
            cursor.execute(query_incidencias, (reg_patr, num_afil, mes_ano))
            resultado = cursor.fetchone()
            total_incidencias = resultado.total_incidencias if resultado else 0
            logging.info(f"Total de incidencias en el mes: {total_incidencias}")

            # Formar CON_SEC como texto concatenando "10" con el número de días
            con_sec = f"10{str(num_dias)}"
            logging.info(f"CON_SEC formado: {con_sec}")

            # Crear movimiento de ausencia
            datos_mov.update({
                'TIP_MOVS': '11',
                'CON_SEC': con_sec,
                'CVE_MOVS': 'C',
                'EDO_MOV': 0,
                'ART_33': 'N',
                'SAL_MOVT': 0,
                'SAL_MOVT2': 0,
                'SAL_MOVT3': 0
            })

            # Insertar movimiento
            if not self.insertar_movimiento(datos_mov):
                raise Exception("Error al insertar movimiento de ausencia")

            return True

        except Exception as e:
            logging.error(f"Error al procesar ausencia: {str(e)}")
            raise
    
    def procesar_modificacion_tipo_descuento(self, datos_mov):
        """
        Procesa un movimiento de modificación de tipo de descuento para Infonavit (tipo 18).
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - FEC_INIC: Fecha de modificación
                - TIP_MOVS: Tipo de movimiento (18)
                - Tip_Des: Tipo de descuento (Porcentaje, Cuota Fija, Factor de Descuento)
                - Val_Des: Valor del descuento (opcional)
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            print("Iniciando procesamiento de modificación tipo descuento")
            
            # Extraer datos básicos
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            reg_patr = datos_mov['REG_PATR']
            tipo_descuento = datos_mov['Tip_Des']
            fecha_modificacion = datos_mov['FEC_INIC']
            valor_descuento = datos_mov.get('Val_Des', None)  # Ahora acepta None para verificar si no se proporcionó
            
            print(f"Datos: NSS={num_afil}, REG_PAT={reg_patr}, TIPO_DESC={tipo_descuento}, VALOR={valor_descuento}")
            
            # Convertir tipo de descuento a código numérico y texto descriptivo
            tipo_descuento_codigo = 3  # Por defecto Factor de Descuento
            tipo_descuento_texto = "Factor de Descuento"  # Valor por defecto en texto
            
            if isinstance(tipo_descuento, str):
                tipo_lower = tipo_descuento.lower().strip()
                if 'porcentaje' in tipo_lower:
                    tipo_descuento_codigo = 1
                    tipo_descuento_texto = "Porcentaje"
                elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
                    tipo_descuento_codigo = 2
                    tipo_descuento_texto = "Cuota Fija"
                elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
                    tipo_descuento_codigo = 3
                    tipo_descuento_texto = "Factor de Descuento"
            elif isinstance(tipo_descuento, (int, float)):
                tipo_descuento_codigo = int(tipo_descuento)
                if tipo_descuento_codigo == 1:
                    tipo_descuento_texto = "Porcentaje"
                elif tipo_descuento_codigo == 2:
                    tipo_descuento_texto = "Cuota Fija"
                elif tipo_descuento_codigo == 3:
                    tipo_descuento_texto = "Factor de Descuento"
                
            print(f"Tipo de descuento código: {tipo_descuento_codigo}, texto: {tipo_descuento_texto}")
            
            # Consultar al asegurado 
            cursor = self.conn.cursor()
            
            # Obtener información actual del asegurado para mantener valores
            consulta = f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            print(f"SQL: {consulta}")
            cursor.execute(consulta)
            asegurado = cursor.fetchone()
            
            if not asegurado:
                print(f"No se encontró el asegurado {num_afil}")
                return False
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            print(f"Columnas disponibles: {columnas}")
            
            # Función para buscar un campo en varias variantes posibles
            def obtener_valor_campo(row, variantes, default=None):
                for variante in variantes:
                    try:
                        idx = columnas.index(variante)
                        if row[idx] is not None:
                            return row[idx]
                    except ValueError:
                        continue
                return default
            
            # Obtener información del crédito con diferentes variantes de nombres
            numero_credito = obtener_valor_campo(
                asegurado, 
                ['Num_Cre', 'NUM_CRE', 'NumCre', 'NUMCRE', 'Numero_Credito'], 
                ''
            )
            
            # Verificar tipo de descuento actual
            tipo_dsc_actual = obtener_valor_campo(
                asegurado, 
                ['TIP_DSC', 'Tip_Dsc', 'TipDsc', 'TIPDSC', 'Tipo_Descuento'], 
                None
            )
            
            # Verificar si está dado de baja
            fecha_baja = obtener_valor_campo(
                asegurado, 
                ['FEC_BAJ', 'Fec_Baj', 'FecBaj', 'FECBAJ', 'Fecha_Baja'], 
                None
            )
            
            # Obtener valor de descuento actual
            val_dsc_actual = obtener_valor_campo(
                asegurado, 
                ['VAL_DSC', 'Val_Dsc', 'ValDsc', 'VALDSC', 'Valor_Descuento'], 
                0
            )
            
            # Obtener salario actual
            salario_actual = obtener_valor_campo(
                asegurado, 
                ['SAL_IMSS', 'Sal_Imss', 'SalImss', 'SALIMSS', 'Salario'], 
                0
            )
            
            print(f"Datos obtenidos: Número crédito={numero_credito}, Tipo descuento={tipo_dsc_actual}, " +
                  f"Valor descuento={val_dsc_actual}, Fecha baja={fecha_baja}, Salario={salario_actual}")
            
            # Verificar que tenga crédito activo
            if tipo_dsc_actual is None or tipo_dsc_actual == 0:
                print(f"El asegurado {num_afil} no tiene crédito activo")
                return False
                
            # Verificar que no esté dado de baja
            if fecha_baja is not None:
                print(f"El asegurado {num_afil} está dado de baja con fecha {fecha_baja}")
                return False
            
            # Si no se proporcionó valor de descuento, usar el actual
            if valor_descuento is None:
                valor_descuento = val_dsc_actual
                print(f"Usando valor de descuento actual: {valor_descuento}")
            
            # Si no hay salario en los datos del movimiento, usar el actual
            salario_movimiento = datos_mov.get('SAL_MOVT', salario_actual)
            
            # NUEVA IMPLEMENTACIÓN: Obtener el último movimiento tipo 15 para este asegurado
            # para usar todos sus valores exactamente como base
            query_mov_15 = """
            SELECT TOP 1 * FROM Movtos 
            WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '15'
            ORDER BY FEC_INIC DESC
            """
            cursor.execute(query_mov_15, (reg_patr, num_afil))
            mov_15 = cursor.fetchone()
            
            # Si encontramos un movimiento tipo 15, usamos sus valores como base
            if mov_15:
                print("Encontrado movimiento tipo 15 anterior para copiar valores")
                # Obtener los nombres de columnas del resultado
                cols_movimiento = [column[0] for column in cursor.description]
                
                # Crear diccionario con los valores del movimiento tipo 15
                base_mov = {}
                for i, col in enumerate(cols_movimiento):
                    valor = mov_15[i]
                    # Convertir None a valores adecuados según el tipo de campo
                    if valor is None:
                        if col in ['SAL_MOVT', 'SAL_MOVT2', 'SAL_MOVT3', 'NUM_DIAS', 'Val_Des', 'Tab_Dism']:
                            valor = 0
                        elif col in ['CVE_MOVS', 'ART_33', 'Num_Cre']:
                            valor = ''
                    
                    # Solo incluir campos válidos para el movimiento tipo 18
                    if col not in ['ID', 'TIP_MOVS', 'FEC_INIC', 'CVE_MOVS']:  # Excluir estos campos y CVE_MOVS
                        base_mov[col] = valor
                
                # Preparar el movimiento tipo 18 basado en el tipo 15, pero con los nuevos valores
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '18',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',  # Letra C para tipo 18
                    'Tip_Des': tipo_descuento_texto,  # Nuevo tipo en formato texto
                    'Val_Des': valor_descuento,  # Nuevo valor
                    'EDO_MOV': 0,  # Siempre 0 para nuevos movimientos
                    **base_mov  # Incluir todos los demás campos del movimiento tipo 15
                }
                
                # Asegurar que NUM_DIAS existe y es 0
                datos_movimiento['NUM_DIAS'] = base_mov.get('NUM_DIAS', 0)
                
                # Mantener el número de crédito original
                datos_movimiento['Num_Cre'] = base_mov.get('Num_Cre', numero_credito)
                
                # Si no hay Tab_Dism en el movimiento original, agregar con valor 0
                if 'Tab_Dism' not in datos_movimiento:
                    datos_movimiento['Tab_Dism'] = 0
            else:
                print("No se encontró movimiento tipo 15 anterior, usando valores por defecto")
                # Crear movimiento tipo 18 con valores por defecto
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '18',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',
                    'EDO_MOV': 0,
                    'ART_33': 'N',
                    'SAL_MOVT': salario_movimiento,
                    'SAL_MOVT2': 0,
                    'SAL_MOVT3': 0,
                    'NUM_DIAS': 0,
                    'Tip_Des': tipo_descuento_texto,  # Mantener el mismo tipo de descuento
                    'Val_Des': valor_descuento,  # Nuevo valor de descuento
                    'Num_Cre': numero_credito,
                    'Tab_Dism': 0,
                    'SAL_ANT1': 0,
                    'SAL_ANT2': 0,
                    'SAL_ANT3': 0
                }
            
            print(f"Insertando movimiento: {datos_movimiento}")
            if not self.insertar_movimiento(datos_movimiento):
                raise Exception("Error al insertar movimiento tipo 18")
            
            # Actualizar registro en Asegura con el código numérico
            update_query = f"UPDATE Asegura SET "
            
            # Buscar el nombre correcto de la columna para TIP_DSC
            nombre_col_tipo = next((col for col in columnas if col.upper() == 'TIP_DSC'), 'TIP_DSC')
            nombre_col_valor = next((col for col in columnas if col.upper() == 'VAL_DSC'), 'VAL_DSC')
            
            update_query += f"{nombre_col_tipo} = {tipo_descuento_codigo}, {nombre_col_valor} = {valor_descuento} "
            update_query += f"WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            
            print(f"SQL UPDATE: {update_query}")
            cursor.execute(update_query)
            print(f"Filas actualizadas: {cursor.rowcount}")
            
            # Confirmar cambios
            self.conn.commit()
            print("✅ Modificación de tipo de descuento completada con éxito")
            return True
            
        except Exception as e:
            print(f"❌ Error en procesar_modificacion_tipo_descuento: {str(e)}")
            traceback.print_exc()
            try:
                self.conn.rollback()
            except:
                pass
            return False
            
    def procesar_modificacion_valor_descuento(self, datos_mov):
        """
        Procesa un movimiento de modificación de valor de descuento para Infonavit (tipo 19).
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - FEC_INIC: Fecha de modificación
                - TIP_MOVS: Tipo de movimiento (19)
                - Val_Des: Valor del descuento nuevo
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            print("Iniciando procesamiento de modificación valor descuento")
            
            # Extraer datos básicos
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            reg_patr = datos_mov['REG_PATR']
            fecha_modificacion = datos_mov['FEC_INIC']
            
            # Verificar que se haya proporcionado un valor de descuento
            if 'Val_Des' not in datos_mov or datos_mov['Val_Des'] is None:
                raise ValueError("Se requiere el valor de descuento para el movimiento tipo 19")
            
            valor_descuento = datos_mov['Val_Des']
            print(f"Datos: NSS={num_afil}, REG_PAT={reg_patr}, VALOR={valor_descuento}")
            
            # Consultar al asegurado 
            cursor = self.conn.cursor()
            
            # Obtener información actual del asegurado para mantener valores
            consulta = f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            print(f"SQL: {consulta}")
            cursor.execute(consulta)
            asegurado = cursor.fetchone()
            
            if not asegurado:
                print(f"No se encontró el asegurado {num_afil}")
                return False
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            print(f"Columnas disponibles: {columnas}")
            
            # Función para buscar un campo en varias variantes posibles
            def obtener_valor_campo(row, variantes, default=None):
                for variante in variantes:
                    try:
                        idx = columnas.index(variante)
                        if row[idx] is not None:
                            return row[idx]
                    except ValueError:
                        continue
                return default
            
            # Obtener información del crédito con diferentes variantes de nombres
            numero_credito = obtener_valor_campo(
                asegurado, 
                ['Num_Cre', 'NUM_CRE', 'NumCre', 'NUMCRE', 'Numero_Credito'], 
                ''
            )
            
            # Verificar tipo de descuento actual
            tipo_dsc_actual = obtener_valor_campo(
                asegurado, 
                ['TIP_DSC', 'Tip_Dsc', 'TipDsc', 'TIPDSC', 'Tipo_Descuento'], 
                None
            )
            
            # Obtener el texto descriptivo del tipo de descuento actual
            tipo_descuento_texto = "Factor de Descuento"  # Valor por defecto
            
            # Convertir tipo de descuento numérico a texto descriptivo
            if tipo_dsc_actual is not None:
                # Si es un valor numérico o una cadena que representa un número
                if isinstance(tipo_dsc_actual, (int, float)) or (isinstance(tipo_dsc_actual, str) and tipo_dsc_actual.strip().isdigit()):
                    tipo_dsc_codigo = int(float(tipo_dsc_actual))
                    tipo_descuento_texto = {
                        1: "Porcentaje",
                        2: "Cuota Fija",
                        3: "Factor de Descuento"
                    }.get(tipo_dsc_codigo, "Factor de Descuento")
                # Si es una cadena de texto, normalizarla
                elif isinstance(tipo_dsc_actual, str):
                    tipo_lower = tipo_dsc_actual.lower().strip()
                    if 'porcentaje' in tipo_lower or tipo_lower == '1':
                        tipo_descuento_texto = "Porcentaje"
                    elif 'cuota' in tipo_lower and 'fija' in tipo_lower or tipo_lower == '2':
                        tipo_descuento_texto = "Cuota Fija"
                    elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']) or tipo_lower == '3':
                        tipo_descuento_texto = "Factor de Descuento"
            
            # Verificar si está dado de baja
            fecha_baja = obtener_valor_campo(
                asegurado, 
                ['FEC_BAJ', 'Fec_Baj', 'FecBaj', 'FECBAJ', 'Fecha_Baja'], 
                None
            )
            
            # Obtener salario actual
            salario_actual = obtener_valor_campo(
                asegurado, 
                ['SAL_IMSS', 'Sal_Imss', 'SalImss', 'SALIMSS', 'Salario'], 
                0
            )
            
            print(f"Datos obtenidos: Número crédito={numero_credito}, Tipo descuento={tipo_dsc_actual}, " +
                  f"Tipo texto={tipo_descuento_texto}, Fecha baja={fecha_baja}, Salario={salario_actual}")
            
            # Verificar que tenga crédito activo
            if tipo_dsc_actual is None or tipo_dsc_actual == 0:
                print(f"El asegurado {num_afil} no tiene crédito activo")
                return False
                
            # Verificar que no esté dado de baja
            if fecha_baja is not None:
                print(f"El asegurado {num_afil} está dado de baja con fecha {fecha_baja}")
                return False
            
            # NUEVA IMPLEMENTACIÓN: Obtener el último movimiento tipo 15 para este asegurado
            # para usar todos sus valores exactamente como base
            query_mov_15 = """
            SELECT TOP 1 * FROM Movtos 
            WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '15'
            ORDER BY FEC_INIC DESC
            """
            cursor.execute(query_mov_15, (reg_patr, num_afil))
            mov_15 = cursor.fetchone()
            
            # Si encontramos un movimiento tipo 15, usamos sus valores como base
            if mov_15:
                print("Encontrado movimiento tipo 15 anterior para copiar valores")
                # Obtener los nombres de columnas del resultado
                cols_movimiento = [column[0] for column in cursor.description]
                
                # Crear diccionario con los valores del movimiento tipo 15
                base_mov = {}
                for i, col in enumerate(cols_movimiento):
                    valor = mov_15[i]
                    # Convertir None a valores adecuados según el tipo de campo
                    if valor is None:
                        if col in ['SAL_MOVT', 'SAL_MOVT2', 'SAL_MOVT3', 'NUM_DIAS', 'Tab_Dism']:
                            valor = 0
                        elif col in ['CVE_MOVS', 'ART_33']:
                            valor = ''
                    
                    # Solo incluir campos válidos para el movimiento tipo 19
                    if col not in ['ID', 'TIP_MOVS', 'FEC_INIC', 'Val_Des', 'CVE_MOVS']:  # Excluir estos campos y CVE_MOVS
                        base_mov[col] = valor
                
                # Preparar el movimiento tipo 19 basado en el tipo 15, pero con el nuevo valor
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '19',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',  # Letra C para tipo 19, igual que para el 18
                    'Tip_Des': tipo_descuento_texto,  # Mantener el mismo tipo de descuento, en texto
                    'Val_Des': valor_descuento,  # Nuevo valor de descuento
                    'EDO_MOV': 0,  # Siempre 0 para nuevos movimientos
                    **base_mov  # Incluir todos los demás campos del movimiento tipo 15
                }
                
                # Asegurar que NUM_DIAS existe y es 0
                datos_movimiento['NUM_DIAS'] = base_mov.get('NUM_DIAS', 0)
                
                # Mantener el número de crédito original
                datos_movimiento['Num_Cre'] = base_mov.get('Num_Cre', numero_credito)
                
                # Si no hay Tab_Dism en el movimiento original, agregar con valor 0
                if 'Tab_Dism' not in datos_movimiento:
                    datos_movimiento['Tab_Dism'] = 0
            else:
                print("No se encontró movimiento tipo 15 anterior, usando valores por defecto")
                # Crear movimiento tipo 19 con valores por defecto
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '19',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',
                    'EDO_MOV': 0,
                    'ART_33': 'N',
                    'SAL_MOVT': datos_mov.get('SAL_MOVT', salario_actual),
                    'SAL_MOVT2': 0,
                    'SAL_MOVT3': 0,
                    'NUM_DIAS': 0,
                    'Tip_Des': tipo_descuento_texto,  # Mantener el mismo tipo de descuento
                    'Val_Des': valor_descuento,  # Nuevo valor de descuento
                    'Num_Cre': numero_credito,
                    'Tab_Dism': 0,
                    'SAL_ANT1': 0,
                    'SAL_ANT2': 0,
                    'SAL_ANT3': 0
                }
            
            print(f"Insertando movimiento: {datos_movimiento}")
            if not self.insertar_movimiento(datos_movimiento):
                raise Exception("Error al insertar movimiento tipo 19")
            
            # Actualizar registro en Asegura con el nuevo valor de descuento
            update_query = f"UPDATE Asegura SET "
            
            # Buscar el nombre correcto de la columna para VAL_DSC
            nombre_col_valor = next((col for col in columnas if col.upper() == 'VAL_DSC'), 'VAL_DSC')
            
            update_query += f"{nombre_col_valor} = {valor_descuento} "
            update_query += f"WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            
            print(f"SQL UPDATE: {update_query}")
            cursor.execute(update_query)
            print(f"Filas actualizadas: {cursor.rowcount}")
            
            # Confirmar cambios
            self.conn.commit()
            print("✅ Modificación de valor de descuento completada con éxito")
            return True
            
        except Exception as e:
            print(f"❌ Error en procesar_modificacion_valor_descuento: {str(e)}")
            traceback.print_exc()
            try:
                self.conn.rollback()
            except:
                pass
            return False
    
    def procesar_modificacion_numero_credito(self, datos_mov):
        """
        Procesa un movimiento de modificación de número de crédito para Infonavit (tipo 20).
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - FEC_INIC: Fecha de modificación
                - TIP_MOVS: Tipo de movimiento (20)
                - Num_Cre: Número de crédito nuevo
        
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            print("Iniciando procesamiento de modificación número de crédito")
            
            # Extraer datos básicos
            num_afil = str(datos_mov['NUM_AFIL']).zfill(11)
            reg_patr = datos_mov['REG_PATR']
            fecha_modificacion = datos_mov['FEC_INIC']
            
            # Verificar que se haya proporcionado un número de crédito
            if 'Num_Cre' not in datos_mov or not datos_mov['Num_Cre']:
                raise ValueError("Se requiere el número de crédito para el movimiento tipo 20")
            
            nuevo_numero_credito = datos_mov['Num_Cre']
            print(f"Datos: NSS={num_afil}, REG_PAT={reg_patr}, NUEVO_CREDITO={nuevo_numero_credito}")
            
            # Consultar al asegurado 
            cursor = self.conn.cursor()
            
            # Obtener información actual del asegurado para mantener valores
            consulta = f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            print(f"SQL: {consulta}")
            cursor.execute(consulta)
            asegurado = cursor.fetchone()
            
            if not asegurado:
                print(f"No se encontró el asegurado {num_afil}")
                return False
            
            # Obtener nombres de columnas
            columnas = [column[0] for column in cursor.description]
            print(f"Columnas disponibles: {columnas}")
            
            # Función para buscar un campo en varias variantes posibles
            def obtener_valor_campo(row, variantes, default=None):
                for variante in variantes:
                    try:
                        idx = columnas.index(variante)
                        if row[idx] is not None:
                            return row[idx]
                    except ValueError:
                        continue
                return default
            
            # Obtener información del crédito actual con diferentes variantes de nombres
            numero_credito_actual = obtener_valor_campo(
                asegurado, 
                ['Num_Cre', 'NUM_CRE', 'NumCre', 'NUMCRE', 'Numero_Credito'], 
                ''
            )
            
            # Verificar tipo de descuento actual
            tipo_dsc_actual = obtener_valor_campo(
                asegurado, 
                ['TIP_DSC', 'Tip_Dsc', 'TipDsc', 'TIPDSC', 'Tipo_Descuento'], 
                None
            )
            
            # Obtener el texto descriptivo del tipo de descuento actual
            tipo_descuento_texto = "Factor de Descuento"  # Valor por defecto
            if tipo_dsc_actual == 1:
                tipo_descuento_texto = "Porcentaje"
            elif tipo_dsc_actual == 2:
                tipo_descuento_texto = "Cuota Fija"
            elif tipo_dsc_actual == 3:
                tipo_descuento_texto = "Factor de Descuento"
            
            # Verificar si está dado de baja
            fecha_baja = obtener_valor_campo(
                asegurado, 
                ['FEC_BAJ', 'Fec_Baj', 'FecBaj', 'FECBAJ', 'Fecha_Baja'], 
                None
            )
            
            # Obtener valor de descuento actual
            valor_descuento_actual = obtener_valor_campo(
                asegurado, 
                ['VAL_DSC', 'Val_Dsc', 'ValDsc', 'VALDSC', 'Valor_Descuento'], 
                0
            )
            
            # Obtener salario actual
            salario_actual = obtener_valor_campo(
                asegurado, 
                ['SAL_IMSS', 'Sal_Imss', 'SalImss', 'SALIMSS', 'Salario'], 
                0
            )
            
            print(f"Datos obtenidos: Número crédito actual={numero_credito_actual}, Tipo descuento={tipo_dsc_actual}, " +
                  f"Tipo texto={tipo_descuento_texto}, Fecha baja={fecha_baja}, Salario={salario_actual}")
            
            # Verificar que tenga crédito activo
            if tipo_dsc_actual is None or tipo_dsc_actual == 0:
                print(f"El asegurado {num_afil} no tiene crédito activo")
                return False
                
            # Verificar que no esté dado de baja
            if fecha_baja is not None:
                print(f"El asegurado {num_afil} está dado de baja con fecha {fecha_baja}")
                return False
            
            # NUEVA IMPLEMENTACIÓN: Obtener el último movimiento tipo 15 para este asegurado
            # para usar todos sus valores exactamente como base
            query_mov_15 = """
            SELECT TOP 1 * FROM Movtos 
            WHERE REG_PATR = ? AND NUM_AFIL = ? AND TIP_MOVS = '15'
            ORDER BY FEC_INIC DESC
            """
            cursor.execute(query_mov_15, (reg_patr, num_afil))
            mov_15 = cursor.fetchone()
            
            # Si encontramos un movimiento tipo 15, usamos sus valores como base
            if mov_15:
                print("Encontrado movimiento tipo 15 anterior para copiar valores")
                # Obtener los nombres de columnas del resultado
                cols_movimiento = [column[0] for column in cursor.description]
                
                # Crear diccionario con los valores del movimiento tipo 15
                base_mov = {}
                for i, col in enumerate(cols_movimiento):
                    valor = mov_15[i]
                    # Convertir None a valores adecuados según el tipo de campo
                    if valor is None:
                        if col in ['SAL_MOVT', 'SAL_MOVT2', 'SAL_MOVT3', 'NUM_DIAS', 'Tab_Dism', 'Val_Des']:
                            valor = 0
                        elif col in ['CVE_MOVS', 'ART_33']:
                            valor = ''
                    
                    # Solo incluir campos válidos para el movimiento tipo 20
                    if col not in ['ID', 'TIP_MOVS', 'FEC_INIC', 'Num_Cre', 'CVE_MOVS']:  # Excluir estos campos y CVE_MOVS
                        base_mov[col] = valor
                
                # Preparar el movimiento tipo 20 basado en el tipo 15, pero con el nuevo número de crédito
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '20',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',  # Letra C para tipo 20, igual que para el 18 y 19
                    'Tip_Des': tipo_descuento_texto,  # Mantener el mismo tipo de descuento, en texto
                    'Val_Des': valor_descuento_actual,  # Mantener el mismo valor de descuento
                    'EDO_MOV': 0,  # Siempre 0 para nuevos movimientos
                    'Num_Cre': nuevo_numero_credito,  # Nuevo número de crédito
                    **base_mov  # Incluir todos los demás campos del movimiento tipo 15
                }
                
                # Asegurar que NUM_DIAS existe y es 0
                datos_movimiento['NUM_DIAS'] = base_mov.get('NUM_DIAS', 0)
                
                # Si no hay Tab_Dism en el movimiento original, agregar con valor 0
                if 'Tab_Dism' not in datos_movimiento:
                    datos_movimiento['Tab_Dism'] = 0
            else:
                print("No se encontró movimiento tipo 15 anterior, usando valores por defecto")
                # Crear movimiento tipo 20 con valores por defecto
                datos_movimiento = {
                    'REG_PATR': reg_patr,
                    'NUM_AFIL': num_afil,
                    'TIP_MOVS': '20',
                    'FEC_INIC': fecha_modificacion,
                    'CVE_MOVS': 'C',
                    'EDO_MOV': 0,
                    'ART_33': 'N',
                    'SAL_MOVT': datos_mov.get('SAL_MOVT', salario_actual),
                    'SAL_MOVT2': 0,
                    'SAL_MOVT3': 0,
                    'NUM_DIAS': 0,
                    'Tip_Des': tipo_descuento_texto,  # Mantener el mismo tipo de descuento
                    'Val_Des': valor_descuento_actual,  # Mantener el mismo valor de descuento
                    'Num_Cre': nuevo_numero_credito,  # Nuevo número de crédito
                    'Tab_Dism': 0,
                    'SAL_ANT1': 0,
                    'SAL_ANT2': 0,
                    'SAL_ANT3': 0
                }
            
            print(f"Insertando movimiento: {datos_movimiento}")
            if not self.insertar_movimiento(datos_movimiento):
                raise Exception("Error al insertar movimiento tipo 20")
            
            # Actualizar registro en Asegura con el nuevo número de crédito
            query_update_asegura = """
            UPDATE Asegura
            SET PAG_INFO = ?
            WHERE REG_PATR = ? AND NUM_AFIL = ?
            """
            cursor.execute(query_update_asegura, (nuevo_numero_credito, reg_patr, num_afil))
            print(f"Filas actualizadas: {cursor.rowcount}")
            
            # Confirmar cambios
            self.conn.commit()
            print("✅ Modificación de número de crédito completada con éxito")
            return True
            
        except Exception as e:
            print(f"❌ Error en procesar_modificacion_numero_credito: {str(e)}")
            traceback.print_exc()
            try:
                self.conn.rollback()
            except:
                pass
            return False
    
    def procesar_movimiento(self, datos_mov):
        """
        Procesa un movimiento según su tipo, delegando a los métodos específicos.
        
        Args:
            datos_mov (dict): Diccionario con los datos del movimiento.
                Debe contener al menos:
                - REG_PATR: Registro patronal
                - NUM_AFIL: Número de afiliación
                - TIP_MOVS: Tipo de movimiento
                - FEC_INIC: Fecha del movimiento
                
        Returns:
            bool: True si el proceso fue exitoso, False en caso contrario
        """
        try:
            # Validar campos requeridos comunes a todos los movimientos
            campos_requeridos = ['REG_PATR', 'NUM_AFIL', 'TIP_MOVS', 'FEC_INIC']
            for campo in campos_requeridos:
                if campo not in datos_mov:
                    raise ValueError(f"Falta el campo requerido: {campo}")
            
            tipo_movimiento = datos_mov['TIP_MOVS']
            print(f"Procesando movimiento tipo {tipo_movimiento}")

            # Validar que no existan otros movimientos en la misma fecha
            cursor = self.conn.cursor()
            fecha_movimiento = datos_mov['FEC_INIC']
            reg_patr = datos_mov['REG_PATR']
            num_afil = datos_mov['NUM_AFIL']

            query_movimientos = """
            SELECT COUNT(*) FROM Movtos 
            WHERE REG_PATR = ? 
            AND NUM_AFIL = ? 
            AND FEC_INIC = ?
            """
            cursor.execute(query_movimientos, (reg_patr, num_afil, fecha_movimiento))
            movimientos_misma_fecha = cursor.fetchone()[0]

            if movimientos_misma_fecha > 0:
                raise ValueError(f"Ya existe un movimiento para el trabajador {num_afil} en la fecha {fecha_movimiento}. No se pueden procesar múltiples movimientos en la misma fecha.")
            
            # Delegar al método específico según el tipo de movimiento
            if tipo_movimiento == '02':
                # Baja
                return self.procesar_baja(datos_mov)
            elif tipo_movimiento == '07':
                # Modificación de salario
                return self.procesar_modificacion_salario(datos_mov)
            elif tipo_movimiento == '08':
                # Reingreso
                return self.procesar_reingreso(datos_mov)
            elif tipo_movimiento == '11':
                # Ausencia
                return self.procesar_ausencia(datos_mov)
            elif tipo_movimiento == '12':
                # Incapacidad
                return self.procesar_incapacidad(datos_mov)
            elif tipo_movimiento == '15':
                # Inicio crédito Infonavit
                return self.procesar_credito_infonavit(datos_mov)
            elif tipo_movimiento == '16':
                # Suspensión de crédito
                return self.procesar_suspension_credito(datos_mov)
            elif tipo_movimiento == '17':
                # Reinicio de crédito
                return self.procesar_reinicio_credito(datos_mov)
            elif tipo_movimiento == '18':
                # Modificación de tipo de descuento
                return self.procesar_modificacion_tipo_descuento(datos_mov)
            elif tipo_movimiento == '19':
                # Modificación de valor de descuento
                return self.procesar_modificacion_valor_descuento(datos_mov)
            elif tipo_movimiento == '20':
                # Modificación de número de crédito
                return self.procesar_modificacion_numero_credito(datos_mov)
            else:
                # Para otros tipos de movimiento, usar el método genérico
                return self.insertar_movimiento(datos_mov)
                
        except Exception as e:
            print(f"❌ Error al procesar movimiento tipo {datos_mov.get('TIP_MOVS')}: {str(e)}")
            traceback.print_exc()
            return False