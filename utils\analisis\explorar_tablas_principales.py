"""
Exploración detallada de las tablas Asegura, Afiliacion y Trabs de SUA.
Este script utiliza la clase ConectorSUA para explorar en detalle
la estructura y los datos de las tablas relacionadas con trabajadores.
"""

import os
import sys
import pandas as pd
import json
import getpass
from pathlib import Path

# Agregar el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importar el conector
from conectores.conector_sua import ConectorSUA

def crear_directorio_si_no_existe(directorio):
    """Crea un directorio si no existe"""
    Path(directorio).mkdir(parents=True, exist_ok=True)

def guardar_dataframe_a_csv(df, nombre_archivo, directorio="resultados"):
    """Guarda un DataFrame en un archivo CSV"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{nombre_archivo}.csv")
    df.to_csv(ruta_completa, index=False, encoding='utf-8-sig')
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def guardar_diccionario_a_json(diccionario, nombre_archivo, directorio="resultados"):
    """Guarda un diccionario en un archivo JSON"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{nombre_archivo}.json")
    with open(ruta_completa, 'w', encoding='utf-8') as f:
        json.dump(diccionario, f, indent=4, ensure_ascii=False)
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def guardar_estructura_tabla_markdown(tabla, columnas, metadatos, directorio="resultados"):
    """Guarda la estructura de una tabla en formato Markdown"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{tabla}_estructura.md")
    
    with open(ruta_completa, 'w', encoding='utf-8') as f:
        f.write(f"# Estructura de la tabla {tabla}\n\n")
        f.write("| # | Columna | Tipo | Tamaño | Permite NULL |\n")
        f.write("|---|---------|------|--------|-------------|\n")
        
        for i, col in enumerate(columnas, 1):
            if col in metadatos:
                metadata = metadatos[col]
                tipo = str(metadata['tipo']).replace("<class '", "").replace("'>", "")
                tamaño = metadata['tamaño_interno'] or 'N/A'
                permite_null = "Sí" if metadata['permite_nulos'] else "No"
                f.write(f"| {i} | {col} | {tipo} | {tamaño} | {permite_null} |\n")
            else:
                f.write(f"| {i} | {col} | N/A | N/A | N/A |\n")
    
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def analizar_tabla(conector, tabla, limite=50):
    """Analiza una tabla y guarda sus datos y estructura"""
    print(f"\n{'='*50}")
    print(f"ANALIZANDO TABLA: {tabla}")
    print(f"{'='*50}\n")
    
    # Verificar si la tabla existe
    if not conector.existe_tabla(tabla):
        print(f"La tabla {tabla} no existe en la base de datos.")
        return None
    
    # Obtener estructura
    columnas = conector.obtener_estructura_tabla(tabla)
    print(f"\nEstructura de la tabla {tabla} ({len(columnas)} columnas):")
    for i, col in enumerate(columnas, 1):
        print(f"{i}. {col}")
    
    # Obtener metadatos detallados
    metadatos = conector.obtener_metadata_tabla(tabla)
    
    # Guardar estructura en Markdown
    ruta_md = guardar_estructura_tabla_markdown(tabla, columnas, metadatos)
    
    # Guardar metadatos
    ruta_json = guardar_diccionario_a_json(metadatos, f"{tabla}_metadatos")
    
    # Obtener muestra de datos
    df = conector.obtener_datos_tabla(tabla, limite=limite)
    if not df.empty:
        print(f"\nMuestra de {len(df)} registros de {tabla}:")
        print(df.head().to_string())
        
        # Información estadística básica
        print(f"\nInformación estadística de {tabla}:")
        info = {
            "filas_muestra": len(df),
            "columnas": len(df.columns),
            "tipos_datos": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "valores_nulos": {col: int(df[col].isna().sum()) for col in df.columns},
            "valores_unicos": {col: int(df[col].nunique()) for col in df.columns if df[col].dtype != 'object' or len(df[col].unique()) < 50}
        }
        
        for key, value in info.items():
            if key != "tipos_datos" and key != "valores_nulos" and key != "valores_unicos":
                print(f"  - {key}: {value}")
        
        # Guardar datos y estadísticas
        ruta_csv = guardar_dataframe_a_csv(df, f"{tabla}_muestra")
        ruta_stats = guardar_diccionario_a_json(info, f"{tabla}_estadisticas")
        
        return {
            "tabla": tabla,
            "columnas": columnas,
            "metadatos": metadatos,
            "estadisticas": info,
            "archivos": {
                "estructura_md": ruta_md,
                "metadatos_json": ruta_json,
                "muestra_csv": ruta_csv,
                "estadisticas_json": ruta_stats
            }
        }
    else:
        print(f"No se pudieron obtener datos de la tabla {tabla}")
        return None

def imprimir_relaciones_potenciales(tablas_data):
    """Identifica y muestra posibles relaciones entre las tablas"""
    if not all(tablas_data.values()):
        return
    
    print("\n\n" + "="*50)
    print("ANÁLISIS DE RELACIONES POTENCIALES")
    print("="*50)
    
    # Obtener conjuntos de columnas para cada tabla
    conjuntos_columnas = {tabla: set(data["columnas"]) for tabla, data in tablas_data.items() if data}
    
    # Encontrar columnas comunes entre pares de tablas
    for i, (tabla1, cols1) in enumerate(conjuntos_columnas.items()):
        for tabla2, cols2 in list(conjuntos_columnas.items())[i+1:]:
            comunes = cols1.intersection(cols2)
            if comunes:
                print(f"\nColumnas comunes entre {tabla1} y {tabla2}:")
                for col in sorted(comunes):
                    print(f"  - {col}")
    
    # Buscar patrones en nombres (como ID, Clave, NSS, etc.)
    print("\nPosibles columnas de identificación:")
    for tabla, cols in conjuntos_columnas.items():
        id_cols = [col for col in cols if any(key in col.lower() for key in ["id", "clave", "registro", "nss", "num"])]
        if id_cols:
            print(f"  En {tabla}: {', '.join(id_cols)}")

def solicitar_credenciales():
    """Solicita al usuario la ruta y contraseña de la base de datos"""
    print("\n=== CONFIGURACIÓN DE LA BASE DE DATOS ===")
    
    # Solicitar ruta de la base de datos
    ruta_predeterminada = r'C:\Cobranza\SUA\SUA.MDB'
    ruta_input = input(f"Ruta a la base de datos SUA [Enter para usar {ruta_predeterminada}]: ")
    ruta_bd = ruta_input.strip() if ruta_input.strip() else ruta_predeterminada
    
    # Verificar si la base existe
    if not os.path.exists(ruta_bd):
        print(f"¡Advertencia! La ruta {ruta_bd} no existe.")
        opciones = input("¿Desea continuar de todos modos? (s/n): ").lower()
        if opciones != 's':
            print("Operación cancelada.")
            sys.exit(0)
    
    # Usar contraseña fija
    password = "S5@N52V49"
    print("Usando contraseña predeterminada para la base de datos.")
    
    return ruta_bd, password

def main():
    """Función principal"""
    print("Iniciando exploración de las tablas Asegura, Afiliacion y Trabs...\n")
    
    # Solicitar credenciales
    ruta_bd, password = solicitar_credenciales()
    
    # Inicializar el conector con la ruta y contraseña proporcionadas
    conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
    
    # Verificar conexión
    if not conector.conectar():
        print("Reintentando con la contraseña predeterminada...")
        conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
        if not conector.conectar():
            print("No se pudo establecer conexión con la base de datos. Abortando.")
            sys.exit(1)
    
    try:
        # Tablas a analizar
        tablas_objetivo = ['Asegura', 'Afiliacion', 'Trabs']
        resultados = {}
        
        # Analizar cada tabla
        for tabla in tablas_objetivo:
            resultados[tabla] = analizar_tabla(conector, tabla)
        
        # Analizar posibles relaciones entre las tablas
        imprimir_relaciones_potenciales(resultados)
        
        # Guardar resumen del análisis
        resumen = {
            "tablas_analizadas": tablas_objetivo,
            "fecha_analisis": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
            "resultados": {k: v["archivos"] if v else None for k, v in resultados.items()}
        }
        guardar_diccionario_a_json(resumen, "resumen_analisis_trabajadores")
        
        print("\nAnálisis completado con éxito.")
        
    finally:
        # Cerrar conexión
        conector.desconectar()

if __name__ == "__main__":
    main() 