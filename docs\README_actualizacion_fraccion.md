# Módulo de Actualización de Fracciones SUA

## Descripción

Este módulo permite actualizar la fracción (campo `Fraccion` de la tabla `Patron`) de registros patronales existentes en la base de datos SUA de manera masiva a través de un archivo Excel.

## Características

- Interfaz gráfica para la selección de archivos y supervisión del proceso
- Validación de datos antes de realizar actualizaciones
- Registro detallado de operaciones y errores
- Actualización segura que verifica la existencia del patrón antes de intentar actualizarlo
- Compatibilidad con la estructura de datos de la tabla `Patron` de SUA
- Generación de reportes de errores para análisis posterior

## Requisitos del archivo Excel

El archivo Excel debe contener al menos las siguientes columnas:

- **Registro Patronal**: El número de registro patronal existente en la base de datos
- **Fracción**: La nueva fracción a asignar al patrón

## Archivos incluidos

- `actualizar_fraccion_patrones.py`: Aplicación principal con interfaz gráfica
- `probar_actualizacion_fraccion.py`: Script de prueba para generar un archivo Excel de actualización y probar el proceso

## Instrucciones de uso

### Aplicación gráfica

1. Ejecute el script `actualizar_fraccion_patrones.py`
2. Seleccione la base de datos SUA (MDB) utilizando el botón "Examinar..."
3. Seleccione el archivo Excel con los datos de las fracciones a actualizar
4. Pruebe la conexión a la base de datos con el botón "Probar Conexión"
5. Valide el archivo Excel con el botón "Validar Excel"
6. Inicie el proceso de actualización con el botón "Actualizar Fracciones"
7. Supervise el progreso en el área de registro de actividad
8. Los errores se guardarán en la carpeta `resultados` para referencia futura

### Script de prueba

El script `probar_actualizacion_fraccion.py` permite:

1. Generar un archivo Excel de ejemplo con patrones existentes
2. Asignar nuevas fracciones aleatorias a los patrones seleccionados
3. Realizar una prueba de actualización de fracciones
4. Verificar que las actualizaciones se realicen correctamente

Para ejecutar:

```
python probar_actualizacion_fraccion.py
```

## Proceso interno

1. **Verificación de existencia de patrones**: Antes de intentar actualizar, se verifica que el registro patronal exista en la base de datos.
2. **Normalización de fracciones**: Las fracciones se normalizan a un formato de 3 dígitos con ceros a la izquierda (ejemplo: "043" en lugar de "43").
3. **Actualización en la base de datos**: Se ejecuta una consulta UPDATE para modificar el campo `Fraccion` del patrón.
4. **Verificación de la actualización**: Después de cada actualización, se verifica que el cambio se haya aplicado correctamente.
5. **Registro de resultados**: Se lleva un registro detallado de las operaciones exitosas y errores.

## Estadísticas generadas

Durante el proceso, la aplicación mantiene las siguientes estadísticas:

- **Patrones actualizados**: Número de patrones que se actualizaron exitosamente
- **Patrones no encontrados**: Número de patrones que no se encontraron en la base de datos
- **Errores de actualización**: Número de errores ocurridos durante el proceso de actualización

## Consideraciones importantes

1. El script siempre utiliza la contraseña predeterminada "S5@N52V49" para conectarse a la base de datos SUA.
2. Las fracciones se formatean automáticamente con tres dígitos utilizando ceros a la izquierda (ejemplo: "043").
3. El campo `Fraccion` en la tabla `Patron` es de tipo texto, lo que permite mantener los ceros iniciales.
4. El sistema mostrará advertencias cuando un patrón no se encuentre en la base de datos.
5. Las actualizaciones se realizan una por una, por lo que el proceso puede tomar tiempo si hay muchos registros.

## Tratamiento especial de fracciones

Para garantizar que las fracciones mantengan el formato correcto con ceros a la izquierda:

1. Se utiliza la función `zfill(3)` de Python para formatear la fracción con 3 dígitos (ejemplo: "001", "043", "123").
2. En la consulta SQL, la fracción se pasa como texto `'{fraccion}'` para preservar el formato.
3. Al leer datos desde Excel, los valores numéricos se convierten a texto antes de formatearlos.

Ejemplo de formateo:
```python
# Formatear fracción con ceros a la izquierda (3 dígitos)
fraccion_valor = "43"
fraccion_formateada = fraccion_valor.zfill(3)  # Resultado: "043"
```

## Integración con el sistema SUA-Tool

Este módulo está diseñado para integrarse con la suite de herramientas SUA-Tool, compartiendo:

- El conector a la base de datos SUA (`ConectorSUA`)
- El formato de logs y reportes
- La estructura de directorios para resultados

## Resolución de problemas

Si encuentra problemas durante la actualización, verifique:

1. Que la base de datos SUA esté accesible y la ruta sea correcta
2. Que el archivo Excel tenga el formato esperado con todas las columnas necesarias
3. Que los registros patronales en el Excel existan en la base de datos SUA
4. Que no haya errores de formato en las fracciones

Los archivos de error generados en la carpeta `resultados` proporcionarán información detallada sobre cualquier problema encontrado durante la ejecución. 