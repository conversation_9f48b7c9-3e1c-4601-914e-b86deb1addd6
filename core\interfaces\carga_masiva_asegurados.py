"""
Herramienta de carga masiva de asegurados desde Excel.
Esta aplicación permite al usuario seleccionar un archivo Excel con los datos
de los asegurados a cargar y los procesa insertándolos en la base de datos SUA.
"""

import os
import sys
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.scrolledtext import ScrolledText
import threading
import datetime
from pathlib import Path
from calendar import month_name
import json

# Agregar directorio raíz al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar conector y modelos
from conectores.conector_sua import ConectorSUA

class CargaMasivaAseguradosApp:
    def __init__(self, root):
        """Inicializa la aplicación de carga masiva de asegurados"""
        self.root = root
        self.root.title("Carga Masiva de Asegurados - SUA Tool")
        self.root.geometry("900x600")
        self.root.minsize(800, 500)
        
        # Variables
        self.archivo_excel = tk.StringVar()
        self.ruta_bd = tk.StringVar(value=r'C:\Cobranza\SUA\SUA.MDB')
        self.conector = None
        self.procesando = False
        
        # Mapeos para los tipos de trabajador, tipo de salario y tipo de jornada
        self.mapeo_tipo_trabajador = {
            'Permanente': 1,
            'Eventual': 2, 
            'Eventual Construccion': 3
        }
        
        self.mapeo_tipo_salario = {
            'Fijo': 0,
            'Variable': 1,
            'Mixto': 2
        }
        
        self.mapeo_tipo_jornada = {
            'Jornada Completa': 0,
            '1 dia': 1,
            '2 dias': 2,
            '3 dias': 3,
            '4 dias': 4,
            '5 dias': 5,
            'Jornada Reducida': 6
        }
        
        # Configurar estilo
        self.configurar_estilo()
        
        # Crear interfaz
        self.crear_interfaz()
        
        # Centro de mensajes
        self.log("Bienvenido a la herramienta de carga masiva de asegurados.")
        self.log("Por favor, seleccione un archivo Excel y configure los parámetros.")
    
    def configurar_estilo(self):
        """Configura el estilo de la aplicación"""
        self.style = ttk.Style()
        
        # Configurar estilo para botones y otros widgets
        self.style.configure("TButton", padding=6, relief="flat", background="#2196F3")
        self.style.map("TButton", 
                       foreground=[('active', 'white'), ('!disabled', 'black')],
                       background=[('active', '#1976D2'), ('!disabled', '#2196F3')])
        
        self.style.configure("Header.TLabel", font=("Segoe UI", 12, "bold"))
        self.style.configure("TFrame", background="#f5f5f5")
        self.style.configure("TLabel", background="#f5f5f5")
    
    def crear_interfaz(self):
        """Crea la interfaz gráfica"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # Sección de configuración
        ttk.Label(main_frame, text="Configuración", style="Header.TLabel").pack(pady=(0, 10), anchor="w")
        
        config_frame = ttk.Frame(main_frame)
        config_frame.pack(fill="x", pady=(0, 10))
        
        # Ruta de la base de datos
        ttk.Label(config_frame, text="Base de datos SUA:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(config_frame, textvariable=self.ruta_bd, width=60).grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", command=self.seleccionar_bd).grid(row=0, column=2, padx=5, pady=5)
        
        # Archivo Excel
        ttk.Label(config_frame, text="Archivo Excel:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(config_frame, textvariable=self.archivo_excel, width=60).grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", command=self.seleccionar_excel).grid(row=1, column=2, padx=5, pady=5)
        
        config_frame.columnconfigure(1, weight=1)
        
        # Sección de acciones
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x", pady=10)
        
        self.btn_probar = ttk.Button(action_frame, text="Probar Conexión", command=self.probar_conexion)
        self.btn_probar.pack(side="left", padx=5)
        
        self.btn_validar = ttk.Button(action_frame, text="Validar Excel", command=self.validar_excel)
        self.btn_validar.pack(side="left", padx=5)
        
        self.btn_procesar = ttk.Button(action_frame, text="Procesar Registros", command=self.iniciar_procesamiento)
        self.btn_procesar.pack(side="left", padx=5)
        
        self.btn_limpiar = ttk.Button(action_frame, text="Limpiar Log", command=self.limpiar_log)
        self.btn_limpiar.pack(side="right", padx=5)
        
        # Barra de progreso
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress.pack(fill="x", pady=10)
        
        # Área de logs
        ttk.Label(main_frame, text="Registro de actividad", style="Header.TLabel").pack(pady=(10, 5), anchor="w")
        self.log_area = ScrolledText(main_frame, height=15, wrap=tk.WORD)
        self.log_area.pack(fill="both", expand=True)
        self.log_area.config(state=tk.DISABLED)
        
        # Barra de estado
        self.status_var = tk.StringVar(value="Listo")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(side="bottom", fill="x")
    
    def seleccionar_bd(self):
        """Abre un diálogo para seleccionar la base de datos SUA"""
        ruta = filedialog.askopenfilename(
            title="Seleccionar base de datos SUA",
            filetypes=[("Archivos Access", "*.mdb"), ("Todos los archivos", "*.*")]
        )
        if ruta:
            self.ruta_bd.set(ruta)
            self.log(f"Base de datos seleccionada: {ruta}")
    
    def seleccionar_excel(self):
        """Abre un diálogo para seleccionar el archivo Excel"""
        ruta = filedialog.askopenfilename(
            title="Seleccionar archivo Excel",
            filetypes=[("Archivos Excel", "*.xlsx *.xls"), ("Todos los archivos", "*.*")]
        )
        if ruta:
            self.archivo_excel.set(ruta)
            self.log(f"Archivo Excel seleccionado: {ruta}")
    
    def probar_conexion(self):
        """Prueba la conexión a la base de datos SUA"""
        self.status_var.set("Probando conexión...")
        self.btn_probar.config(state=tk.DISABLED)
        
        def _probar():
            try:
                ruta_bd = self.ruta_bd.get()
                
                if not os.path.exists(ruta_bd):
                    self.log("Error: La ruta de la base de datos no existe.", error=True)
                    return
                
                self.conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
                
                if self.conector.conectar():
                    self.log("Conexión exitosa a la base de datos SUA.", success=True)
                    # Verificar tablas necesarias
                    tablas = ["Asegura", "Afiliacion", "Trabs"]
                    todas_existen = True
                    for tabla in tablas:
                        if not self.conector.existe_tabla(tabla):
                            self.log(f"Advertencia: No se encontró la tabla {tabla}.", warning=True)
                            todas_existen = False
                    
                    if todas_existen:
                        self.log("Se encontraron todas las tablas necesarias.", success=True)
                    
                    self.conector.desconectar()
                else:
                    self.log("Error: No se pudo conectar a la base de datos.", error=True)
            
            except Exception as e:
                self.log(f"Error al probar la conexión: {str(e)}", error=True)
            
            finally:
                self.btn_probar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_probar)
        thread.daemon = True
        thread.start()
    
    def validar_excel(self):
        """Valida el archivo Excel seleccionado"""
        self.status_var.set("Validando Excel...")
        self.btn_validar.config(state=tk.DISABLED)
        
        def _validar():
            try:
                ruta_excel = self.archivo_excel.get()
                
                if not os.path.exists(ruta_excel):
                    self.log("Error: La ruta del archivo Excel no existe.", error=True)
                    return
                
                # Leer el archivo Excel
                try:
                    # Intentar leer la hoja 'Asegurados'
                    df = pd.read_excel(ruta_excel, sheet_name='Asegurados')
                    self.log(f"Archivo Excel leído correctamente. Hoja 'Asegurados' encontrada.")
                except Exception as e:
                    self.log(f"Error al leer el archivo Excel: {str(e)}", error=True)
                    return
                
                # Verificar columnas requeridas
                columnas_requeridas = [
                    'Registro Patronal', 'Número de Seguridad Social', 'RFC', 'CURP', 
                    'Primer apellido', 'Segundo apellido', 'Nombre(s)', 
                    'Tipo de trabajador', 'Tipo de salario', 'Tipo de jornada', 
                    'Fecha de alta', 'Salario diario integrado'
                ]
                
                columnas_faltantes = [col for col in columnas_requeridas if col not in df.columns]
                
                if columnas_faltantes:
                    self.log(f"Error: Faltan las siguientes columnas en el Excel: {', '.join(columnas_faltantes)}", error=True)
                    return
                
                # Verificar que haya registros
                if len(df) == 0:
                    self.log("Error: El archivo Excel no contiene registros.", error=True)
                    return
                
                # Verificar que los registros patronales existan
                registros_patronales = df['Registro Patronal'].unique()
                self.log(f"Se encontraron {len(registros_patronales)} registros patronales distintos.")
                
                # Verificar la existencia de registros patronales solo si hay conexión
                if self.conector is None or not self.conector.conectar():
                    self.conector = ConectorSUA(ruta_bd=self.ruta_bd.get(), password="S5@N52V49")
                    if not self.conector.conectar():
                        self.log("Advertencia: No se pudo conectar a la base de datos para verificar registros patronales.", warning=True)
                        return
                
                registros_validos = 0
                for reg_pat in registros_patronales:
                    query = f"SELECT COUNT(*) AS Total FROM Patron WHERE REG_PAT = '{reg_pat}'"
                    resultado = self.conector.ejecutar_consulta(query)
                    if resultado and resultado[0]['Total'] > 0:
                        registros_validos += 1
                        self.log(f"Registro patronal '{reg_pat}' encontrado en la base de datos.")
                    else:
                        self.log(f"Advertencia: El registro patronal '{reg_pat}' no existe en la base de datos.", warning=True)
                
                # Verificar otros datos
                self.log(f"Verificando datos de {len(df)} asegurados...")
                
                # Verificar NSS (debe tener 11 dígitos)
                # Primero convertimos a string y aplicamos zfill para preservar los ceros iniciales
                df['Número de Seguridad Social'] = df['Número de Seguridad Social'].astype(str).apply(lambda x: x.zfill(11))
                
                # Ahora verificamos el formato correcto
                nss_invalidos = df[~df['Número de Seguridad Social'].str.match(r'^\d{11}$')]
                if not nss_invalidos.empty:
                    self.log(f"Advertencia: {len(nss_invalidos)} NSS no tienen el formato correcto (11 dígitos).", warning=True)
                    
                # Asegurar que todos los NSS tengan 11 dígitos en el dataframe
                self.log("Formateando NSS para asegurar 11 dígitos...")
                df['Número de Seguridad Social'] = df['Número de Seguridad Social'].astype(str).apply(lambda x: x.zfill(11))
                self.log("Formato de NSS completado.")
                
                # Verificar CURP (debe tener 18 caracteres)
                curp_invalidos = df[~df['CURP'].astype(str).str.match(r'^[A-Z0-9]{18}$')]
                if not curp_invalidos.empty:
                    self.log(f"Advertencia: {len(curp_invalidos)} CURP no tienen el formato correcto (18 caracteres alfanuméricos).", warning=True)
                
                # Verificar RFC (debe tener 13 caracteres)
                rfc_invalidos = df[~df['RFC'].astype(str).str.match(r'^[A-Z0-9]{13}$')]
                if not rfc_invalidos.empty:
                    self.log(f"Advertencia: {len(rfc_invalidos)} RFC no tienen el formato correcto (13 caracteres alfanuméricos).", warning=True)
                
                # Verificar que la fecha de alta sea válida
                try:
                    df['Fecha de alta'] = pd.to_datetime(df['Fecha de alta'], errors='coerce')
                    fechas_invalidas = df[df['Fecha de alta'].isnull()]
                    if not fechas_invalidas.empty:
                        self.log(f"Advertencia: {len(fechas_invalidas)} registros tienen fechas de alta inválidas.", warning=True)
                except Exception as e:
                    self.log(f"Error al validar fechas: {str(e)}", error=True)
                
                # Verificar que el salario diario sea numérico
                try:
                    df['Salario diario integrado'] = pd.to_numeric(df['Salario diario integrado'], errors='coerce')
                    salarios_invalidos = df[df['Salario diario integrado'].isnull()]
                    if not salarios_invalidos.empty:
                        self.log(f"Advertencia: {len(salarios_invalidos)} registros tienen salarios inválidos.", warning=True)
                except Exception as e:
                    self.log(f"Error al validar salarios: {str(e)}", error=True)
                
                # Verificar tipos de trabajador
                tipos_trabajador_invalidos = df[~df['Tipo de trabajador'].isin(self.mapeo_tipo_trabajador.keys())]
                if not tipos_trabajador_invalidos.empty:
                    self.log(f"Advertencia: {len(tipos_trabajador_invalidos)} registros tienen tipos de trabajador inválidos.", warning=True)
                    self.log(f"Tipos válidos: {', '.join(self.mapeo_tipo_trabajador.keys())}")
                
                # Verificar tipos de salario
                tipos_salario_invalidos = df[~df['Tipo de salario'].isin(self.mapeo_tipo_salario.keys())]
                if not tipos_salario_invalidos.empty:
                    self.log(f"Advertencia: {len(tipos_salario_invalidos)} registros tienen tipos de salario inválidos.", warning=True)
                    self.log(f"Tipos válidos: {', '.join(self.mapeo_tipo_salario.keys())}")
                
                # Verificar tipos de jornada
                tipos_jornada_invalidos = df[~df['Tipo de jornada'].isin(self.mapeo_tipo_jornada.keys())]
                if not tipos_jornada_invalidos.empty:
                    self.log(f"Advertencia: {len(tipos_jornada_invalidos)} registros tienen tipos de jornada inválidos.", warning=True)
                    self.log(f"Tipos válidos: {', '.join(self.mapeo_tipo_jornada.keys())}")
                
                # Verificar UMF
                umf_invalidos = df[~df['UMF'].fillna('').astype(str).str.match(r'^\d{0,3}$')]
                if not umf_invalidos.empty:
                    self.log(f"Advertencia: {len(umf_invalidos)} registros tienen UMF con formato inválido.", warning=True)
                
                # Verificar códigos postales
                cp_invalidos = df[~df['Código Postal'].fillna('').astype(str).str.match(r'^\d{0,5}$')]
                if not cp_invalidos.empty:
                    self.log(f"Advertencia: {len(cp_invalidos)} registros tienen códigos postales inválidos.", warning=True)
                
                # Resultado final
                self.log(f"Validación completada. Se encontraron {len(df)} registros para procesar.", success=True)
                
            except Exception as e:
                self.log(f"Error durante la validación: {str(e)}", error=True)
                import traceback
                traceback.print_exc()
            
            finally:
                self.btn_validar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_validar)
        thread.daemon = True
        thread.start()
    
    def iniciar_procesamiento(self):
        """Inicia el procesamiento de los registros"""
        if self.procesando:
            messagebox.showwarning("Procesando", "Ya hay un proceso en ejecución.")
            return
        
        ruta_bd = self.ruta_bd.get()
        ruta_excel = self.archivo_excel.get()
        
        if not os.path.exists(ruta_bd):
            self.log("Error: La ruta de la base de datos no existe.", error=True)
            return
        
        if not os.path.exists(ruta_excel):
            self.log("Error: La ruta del archivo Excel no existe.", error=True)
            return
        
        # Desactivar botones durante el procesamiento
        self.procesando = True
        self.btn_procesar.config(state=tk.DISABLED)
        self.status_var.set("Procesando...")
        
        # Iniciar en un hilo separado
        thread = threading.Thread(target=lambda: self._procesar(ruta_bd, ruta_excel))
        thread.daemon = True
        thread.start()
    
    def _procesar(self, ruta_bd, ruta_excel):
        """Procesa los registros del archivo Excel"""
        try:
            # Conectar a la base de datos
            self.conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not self.conector.conectar():
                self.log("Error: No se pudo conectar a la base de datos.", error=True)
                return
            
            # Leer datos del Excel
            try:
                df = pd.read_excel(ruta_excel, sheet_name='Asegurados')
                if df.empty:
                    self.log("Error: No hay datos en la hoja 'Asegurados'.", error=True)
                    return
                    
                # Pre-procesar los tipos de descuento para reemplazar "Cuota Fija" por 2
                if 'Tipo Descuento' in df.columns:
                    # Mostrar los valores de tipo de descuento antes del procesamiento
                    tipos_unicos = df['Tipo Descuento'].unique()
                    self.log(f"Tipos de descuento encontrados en Excel: {tipos_unicos}")
                    
                    # Reemplazar 'Cuota Fija' por 2 (conversión explícita)
                    df['Tipo Descuento'] = df['Tipo Descuento'].apply(
                        lambda x: 2 if isinstance(x, str) and 'cuota' in x.lower() and 'fija' in x.lower() else x
                    )
                    # Reemplazar 'Porcentaje' por 1
                    df['Tipo Descuento'] = df['Tipo Descuento'].apply(
                        lambda x: 1 if isinstance(x, str) and 'porcentaje' in x.lower() else x
                    )
                    # Reemplazar 'Factor' o 'VSM' por 3
                    df['Tipo Descuento'] = df['Tipo Descuento'].apply(
                        lambda x: 3 if isinstance(x, str) and ('factor' in x.lower() or 'vsm' in x.lower() or 'salario' in x.lower()) else x
                    )
                    
                    # Mostrar los valores después del procesamiento
                    tipos_unicos_post = df['Tipo Descuento'].unique()
                    self.log(f"Tipos de descuento después del preprocesamiento: {tipos_unicos_post}")
                    
            except Exception as e:
                self.log(f"Error al leer el archivo Excel: {str(e)}", error=True)
                return
            
            # Resto del código sin cambios
            total_registros = len(df)
            self.log(f"Iniciando procesamiento de {total_registros} registros...")
            
            # Inicializar contadores para estadísticas
            estadisticas = {
                'exitosos': 0,
                'asegura_exitosos': 0,
                'afiliacion_exitosos': 0,
                'trabs_exitosos': 0,
                'movtos_exitosos': 0,
                'errores': 0,
                'duplicados': 0
            }
            
            # Inicializar archivo de errores
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            directorio_resultados = "resultados"
            Path(directorio_resultados).mkdir(parents=True, exist_ok=True)
            archivo_errores = f"{directorio_resultados}/errores_carga_asegurados_{timestamp}.txt"
            
            with open(archivo_errores, 'w', encoding='utf-8') as f_errores:
                f_errores.write(f"ERRORES EN CARGA MASIVA DE ASEGURADOS\n")
                f_errores.write(f"Fecha: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f_errores.write(f"Archivo: {ruta_excel}\n\n")
            
            # Procesar cada registro
            for i, fila in df.iterrows():
                # Actualizar progreso
                progreso = int((i + 1) / total_registros * 100)
                self.update_progress(progreso)
                
                try:
                    # Verificar si el asegurado ya existe
                    # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
                    num_afil = str(fila['Número de Seguridad Social']).zfill(11)
                    reg_pat = str(fila['Registro Patronal'])
                    
                    if self.verificar_existencia_asegurado(num_afil, reg_pat):
                        self.log(f"Registro {i+1}: El asegurado {num_afil} ya existe para el patrón {reg_pat}.", warning=True)
                        estadisticas['duplicados'] += 1
                        with open(archivo_errores, 'a', encoding='utf-8') as f_errores:
                            f_errores.write(f"Fila {i+1}: El asegurado {num_afil} ya existe para el patrón {reg_pat}\n")
                        continue
                    
                    # 1. Insertar en Asegura
                    asegura_ok = self.insertar_asegura(fila)
                    if asegura_ok:
                        estadisticas['asegura_exitosos'] += 1
                    
                    # 2. Insertar en Afiliacion
                    afiliacion_ok = self.insertar_afiliacion(fila)
                    if afiliacion_ok:
                        estadisticas['afiliacion_exitosos'] += 1
                    
                    # 3. Insertar en Trabs
                    trabs_ok = self.insertar_trabs(fila)
                    if trabs_ok:
                        estadisticas['trabs_exitosos'] += 1
                        
                    # 4. Insertar en Movtos (alta)
                    movtos_ok = self.insertar_movtos(fila)
                    if movtos_ok:
                        estadisticas['movtos_exitosos'] += 1
                    
                    # Verificar si todas las inserciones fueron exitosas
                    if asegura_ok and afiliacion_ok and trabs_ok and movtos_ok:
                        estadisticas['exitosos'] += 1
                        self.log(f"Registro {i+1}: Asegurado {num_afil} procesado correctamente.")
                    else:
                        estadisticas['errores'] += 1
                        error_msg = f"Registro {i+1}: Error al procesar asegurado {num_afil}. "
                        error_msg += f"Asegura: {'OK' if asegura_ok else 'Error'}, "
                        error_msg += f"Afiliacion: {'OK' if afiliacion_ok else 'Error'}, "
                        error_msg += f"Trabs: {'OK' if trabs_ok else 'Error'}, "
                        error_msg += f"Movtos: {'OK' if movtos_ok else 'Error'}"
                        self.log(error_msg, error=True)
                        
                        with open(archivo_errores, 'a', encoding='utf-8') as f_errores:
                            f_errores.write(f"Fila {i+1}: {error_msg}\n")
                
                except Exception as e:
                    # Registrar error específico para este registro
                    estadisticas['errores'] += 1
                    error_msg = f"Registro {i+1}: Error inesperado al procesar asegurado. {str(e)}"
                    self.log(error_msg, error=True)
                    
                    with open(archivo_errores, 'a', encoding='utf-8') as f_errores:
                        f_errores.write(f"Fila {i+1}: {error_msg}\n")
            
            # Mostrar resumen
            self.log("\n" + "="*50)
            self.log("RESUMEN DE PROCESAMIENTO")
            self.log("="*50)
            self.log(f"Total de registros procesados: {total_registros}")
            self.log(f"Registros exitosos: {estadisticas['exitosos']}")
            self.log(f"Registros duplicados (omitidos): {estadisticas['duplicados']}")
            self.log(f"Registros con errores: {estadisticas['errores']}")
            self.log(f"Inserciones en tabla Asegura: {estadisticas['asegura_exitosos']}")
            self.log(f"Inserciones en tabla Afiliacion: {estadisticas['afiliacion_exitosos']}")
            self.log(f"Inserciones en tabla Trabs: {estadisticas['trabs_exitosos']}")
            self.log(f"Inserciones en tabla Movtos: {estadisticas['movtos_exitosos']}")
            self.log("\nArchivo de errores: " + archivo_errores)
            self.log("="*50)
            
            # Guardar estadísticas en un archivo JSON
            with open(f"{directorio_resultados}/estadisticas_carga_{timestamp}.json", 'w', encoding='utf-8') as f_stats:
                json.dump({
                    "fecha": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "archivo_excel": ruta_excel,
                    "archivo_bd": ruta_bd,
                    "estadisticas": estadisticas,
                    "archivo_errores": archivo_errores
                }, f_stats, indent=4)
            
            # Cerrar la conexión
            self.conector.desconectar()
            
        except Exception as e:
            self.log(f"Error general en el procesamiento: {str(e)}", error=True)
        finally:
            # Restaurar botones
            def restore_buttons():
                self.procesando = False
                self.btn_procesar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
            self.root.after(0, restore_buttons)
    
    def log(self, mensaje, error=False, warning=False, success=False):
        """Agrega un mensaje al área de logs"""
        self.log_area.config(state=tk.NORMAL)
        
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        if error:
            self.log_area.insert(tk.END, f"[{timestamp}] ERROR: {mensaje}\n", "error")
            self.log_area.tag_configure("error", foreground="red")
        elif warning:
            self.log_area.insert(tk.END, f"[{timestamp}] AVISO: {mensaje}\n", "warning")
            self.log_area.tag_configure("warning", foreground="orange")
        elif success:
            self.log_area.insert(tk.END, f"[{timestamp}] ÉXITO: {mensaje}\n", "success")
            self.log_area.tag_configure("success", foreground="green")
        else:
            self.log_area.insert(tk.END, f"[{timestamp}] {mensaje}\n")
        
        self.log_area.see(tk.END)
        self.log_area.config(state=tk.DISABLED)
    
    def limpiar_log(self):
        """Limpia el área de logs"""
        self.log_area.config(state=tk.NORMAL)
        self.log_area.delete(1.0, tk.END)
        self.log_area.config(state=tk.DISABLED)
        self.log("Log limpiado.")
    
    def update_progress(self, value):
        """Actualiza la barra de progreso"""
        self.progress_var.set(value)
        self.root.update_idletasks()

    def obtener_clave_municipio_por_cp(self, codigo_postal):
        """Obtiene la clave de municipio basado en el código postal"""
        if not codigo_postal or pd.isna(codigo_postal):
            return "E53"  # Valor por defecto
        
        cp_str = str(int(codigo_postal)).zfill(5)
        
        query = f"SELECT TOP 1 CVE_MUN FROM Canamu WHERE CPP_MUN LIKE '{cp_str[:3]}%'"
        resultado = self.conector.ejecutar_consulta(query)
        
        if resultado and len(resultado) > 0:
            return resultado[0]['CVE_MUN']
        else:
            return "E53"  # Valor por defecto para Guadalajara
    
    def obtener_entidad_por_cp(self, codigo_postal):
        """Obtiene el ID de entidad basado en el código postal"""
        if not codigo_postal or pd.isna(codigo_postal):
            return "14"  # Valor por defecto para Jalisco
        
        cp_str = str(int(codigo_postal)).zfill(5)
        
        query = f"SELECT TOP 1 ENT_MUN FROM Canamu WHERE CPP_MUN LIKE '{cp_str[:3]}%'"
        resultado = self.conector.ejecutar_consulta(query)
        
        if resultado and len(resultado) > 0:
            return resultado[0]['ENT_MUN']
        else:
            return "14"  # Valor por defecto para Jalisco
    
    def obtener_fecha_nacimiento_de_curp(self, curp):
        """Extrae la fecha de nacimiento de una CURP"""
        try:
            if not curp or len(curp) < 10:
                return None
            
            # La CURP tiene el formato: AAAA (Pos 4-10)
            # Donde AAAA tiene el formato AABBCC (AA=año, BB=mes, CC=día)
            aa = curp[4:6]
            mm = curp[6:8]
            dd = curp[8:10]
            
            # Determinar el siglo (19xx o 20xx)
            año_completo = ""
            if int(aa) < 25:  # Asumimos que si es menor a 25, es del siglo XXI
                año_completo = f"20{aa}"
            else:
                año_completo = f"19{aa}"
            
            # Crear la fecha en formato yyyy-mm-dd
            fecha_nacimiento = f"{año_completo}-{mm}-{dd}"
            return pd.to_datetime(fecha_nacimiento)
            
        except Exception as e:
            print(f"Error al extraer fecha de CURP {curp}: {e}")
            return None
    
    def verificar_existencia_asegurado(self, num_afil, reg_pat):
        """Verifica si ya existe un asegurado con el mismo NSS y registro patronal"""
        # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
        num_afil_formateado = str(num_afil).zfill(11)
        
        query = f"SELECT COUNT(*) AS Total FROM Asegura WHERE NUM_AFIL = '{num_afil_formateado}' AND REG_PATR = '{reg_pat}'"
        resultado = self.conector.ejecutar_consulta(query)
        
        if resultado and resultado[0]['Total'] > 0:
            return True
        return False
    
    def insertar_asegura(self, datos):
        """Inserta un registro en la tabla Asegura"""
        try:
            # Extraer datos del registro
            reg_pat = datos['Registro Patronal']
            
            # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
            num_afil = str(datos['Número de Seguridad Social']).zfill(11)
            
            # Verificación detallada de los datos Infonavit para los registros problemáticos
            if 'Numero Credito Infonavit' in datos and pd.notna(datos['Numero Credito Infonavit']):
                infonavit_info = f"INFO DETALLADA PARA NSS {num_afil}:\n"
                infonavit_info += f"Número Crédito: {datos['Numero Credito Infonavit']} (tipo: {type(datos['Numero Credito Infonavit'])})\n"
                
                if 'Tipo Descuento' in datos and pd.notna(datos['Tipo Descuento']):
                    infonavit_info += f"Tipo Descuento Original: {datos['Tipo Descuento']} (tipo: {type(datos['Tipo Descuento'])})\n"
                else:
                    infonavit_info += "Tipo Descuento: No especificado\n"
                    
                if 'Valor Descuento' in datos and pd.notna(datos['Valor Descuento']):
                    infonavit_info += f"Valor Descuento Original: {datos['Valor Descuento']} (tipo: {type(datos['Valor Descuento'])})\n"
                else:
                    infonavit_info += "Valor Descuento: No especificado\n"
                    
                self.log(infonavit_info)
            
            curp = datos['CURP']
            rfc = datos['RFC']
            
            # Verificar si ya existe
            if self.verificar_existencia_asegurado(num_afil, reg_pat):
                self.log(f"El asegurado con NSS {num_afil} ya existe para el patrón {reg_pat}.", warning=True)
                return False
            
            # Construir nombre completo
            primer_apellido = datos['Primer apellido'] if pd.notna(datos['Primer apellido']) else ""
            segundo_apellido = datos['Segundo apellido'] if pd.notna(datos['Segundo apellido']) else ""
            nombres = datos['Nombre(s)'] if pd.notna(datos['Nombre(s)']) else ""
            
            # Formato especial de SUA con $ como separador
            nom_aseg = f"{primer_apellido}${segundo_apellido}${nombres}".upper()
            
            # Convertir fecha de alta
            fecha_alta = pd.to_datetime(datos['Fecha de alta']).strftime("%Y-%m-%d")
            
            # Obtener valores de mapeos
            tipo_trabajador = self.mapeo_tipo_trabajador.get(datos['Tipo de trabajador'], 1)
            tipo_jornada = self.mapeo_tipo_jornada.get(datos['Tipo de jornada'], 0)
            
            # Obtener salario
            salario = float(datos['Salario diario integrado']) if pd.notna(datos['Salario diario integrado']) else 0.0
            
            # Valores por defecto u opcionales
            cve_ubc = datos['Ubicación'] if pd.notna(datos['Ubicación']) else "OBRA A"
            tmp_nom = f"{primer_apellido} {segundo_apellido} {nombres}".upper()
            
            # Número de crédito Infonavit
            pag_info = ""
            if 'Numero Credito Infonavit' in datos and pd.notna(datos['Numero Credito Infonavit']) and str(datos['Numero Credito Infonavit']).strip():
                pag_info = str(datos['Numero Credito Infonavit']).strip()
            
            # Tipo y valor de descuento - SIEMPRE como valores numéricos para la tabla Asegura
            # Valor por defecto para tipo de descuento (0 = Sin descuento)
            tip_dsc = 0
            val_dsc = 0.0
            
            # Si tiene número de crédito pero no tiene tipo, usar 2 (Cuota Fija) por defecto
            if pag_info and pag_info.strip():
                tip_dsc = 2  # Valor por defecto si hay crédito: Cuota Fija
            
            # Procesar tipo de descuento si está presente
            if 'Tipo Descuento' in datos and pd.notna(datos['Tipo Descuento']):
                tipo_descuento = datos['Tipo Descuento']
                
                # Este paso debería ser innecesario si el preprocesamiento funcionó
                # Pero lo mantenemos como respaldo
                
                # Si es un número, usar directamente
                if isinstance(tipo_descuento, (int, float)):
                    tip_dsc = int(tipo_descuento)
                # Si es string que contiene un número, convertir
                elif isinstance(tipo_descuento, str) and tipo_descuento.strip().isdigit():
                    tip_dsc = int(tipo_descuento)
                # Si es un texto descriptivo, convertir a su código numérico
                elif isinstance(tipo_descuento, str):
                    tipo_lower = tipo_descuento.lower().strip()
                    if 'porcentaje' in tipo_lower:
                        tip_dsc = 1
                    elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
                        tip_dsc = 2
                    elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
                        tip_dsc = 3
            
            # IMPORTANTE: Forzar tipo_dsc como entero aquí
            tip_dsc = int(tip_dsc)
            
            # Procesar valor de descuento si está presente
            if 'Valor Descuento' in datos and pd.notna(datos['Valor Descuento']):
                try:
                    val_dsc = float(datos['Valor Descuento'])
                except (ValueError, TypeError):
                    val_dsc = 0.0  # Valor seguro si no se puede convertir
            
            # Obtener código de municipio
            cve_mun = self.obtener_clave_municipio_por_cp(datos['Código Postal'])
            
            # Fecha de descuento (solo si hay crédito Infonavit)
            fecha_descuento = pd.to_datetime(fecha_alta)  # Por defecto la misma que la alta
            
            # Mostrar valores finales que se usarán en la consulta
            if pag_info:
                self.log(f"VALORES FINALES PARA INFONAVIT NSS {num_afil}:")
                self.log(f"PAG_INFO: {pag_info}")
                self.log(f"TIP_DSC: {tip_dsc} (tipo: {type(tip_dsc)})")
                self.log(f"VAL_DSC: {val_dsc}")
            
            # Volver a usar consultas parametrizadas que son más seguras
            sql = """
            INSERT INTO Asegura (
                REG_PATR, NUM_AFIL, CURP, RFC_CURP, NOM_ASEG, 
                SAL_IMSS, FEC_ALT, TIP_TRA, SEM_JORD, PAG_INFO,
                TIP_DSC, VAL_DSC, CVE_UBC, TMP_NOM, 
                FEC_DSC, TRA_PENIV, ESTADO, CVE_MUN
            ) VALUES (
                ?, ?, ?, ?, ?, 
                ?, ?, ?, ?, ?, 
                ?, ?, ?, ?, 
                ?, ?, ?, ?
            )
            """
            
            # Preparar parámetros (esto evita problemas de formato y SQL injection)
            parametros = [
                reg_pat, num_afil, curp, rfc, nom_aseg,
                salario, fecha_alta, tipo_trabajador, tipo_jornada, pag_info,
                tip_dsc, val_dsc, cve_ubc, tmp_nom,
                fecha_descuento.strftime("%Y-%m-%d"), 0, 'S', cve_mun
            ]
            
            # Ejecutar la consulta con los parámetros
            cursor = self.conector.conn.cursor()
            cursor.execute(sql, parametros)
            self.conector.conn.commit()
            
            self.log(f"Registro insertado en Asegura: {num_afil}", success=True)
            return True
            
        except Exception as e:
            self.log(f"Error al insertar en Asegura: {str(e)}", error=True)
            
            # Información detallada para depuración
            try:
                error_msg = f"Detalles del error en Asegura para NSS {num_afil}:\n"
                error_msg += f"REG_PATR: {reg_pat}\n"
                error_msg += f"TIP_DSC: {tip_dsc} (tipo: {type(tip_dsc)})\n"
                error_msg += f"VAL_DSC: {val_dsc} (tipo: {type(val_dsc)})\n"
                error_msg += f"PAG_INFO: {pag_info} (tipo: {type(pag_info)})\n"
                self.log(error_msg, error=True)
            except:
                self.log("No se pudieron obtener detalles adicionales del error", error=True)
                
            import traceback
            traceback.print_exc()
            return False
    
    def insertar_afiliacion(self, datos):
        """Inserta un registro en la tabla Afiliacion"""
        try:
            # Extraer datos del registro
            reg_pat = datos['Registro Patronal']
            
            # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
            num_afil = str(datos['Número de Seguridad Social']).zfill(11)
            
            # Código postal
            cpp_trab = datos['Código Postal'] if pd.notna(datos['Código Postal']) else "45040"
            
            # Obtener fecha de nacimiento de CURP
            fecha_nacimiento = self.obtener_fecha_nacimiento_de_curp(datos['CURP'])
            if fecha_nacimiento is None:
                fecha_nacimiento = pd.Timestamp('1980-01-01')  # Valor por defecto
            
            # Lugar de nacimiento
            lug_nac = datos['Lugar de nacimiento'] if pd.notna(datos['Lugar de nacimiento']) else "JALISCO"
            
            # Obtener entidad por código postal
            ent_trab = self.obtener_entidad_por_cp(datos['Código Postal'])
            
            # UMF
            umf_trab = datos['UMF'] if pd.notna(datos['UMF']) else "000"
            
            # Ocupación/Puesto
            ocupa = datos['Puesto'] if pd.notna(datos['Puesto']) else "EMPLEADO"
            
            # Sexo
            sexo = datos['Sexo'] if pd.notna(datos['Sexo']) else "M"
            
            # Tipo de salario
            tipo_salario = self.mapeo_tipo_salario.get(datos['Tipo de salario'], 0)
            
            # Obtener código de municipio
            cve_mun = self.obtener_clave_municipio_por_cp(datos['Código Postal'])
            
            # Construir la consulta SQL
            sql = f"""
            INSERT INTO Afiliacion (
                REG_PATR, NUM_AFIL, CPP_TRAB, FEC_NAC, LUG_NAC, 
                ENT_TRAB, UMF_TRAB, OCUPA, SEXO, TIP_SAL, CVE_MUN
            ) VALUES (
                '{reg_pat}', '{num_afil}', '{cpp_trab}', '{fecha_nacimiento.strftime("%Y-%m-%d")}', '{lug_nac}', 
                '{ent_trab}', '{umf_trab}', '{ocupa}', '{sexo}', {tipo_salario}, '{cve_mun}'
            )
            """
            
            # Ejecutar la consulta
            result = self.conector.conn.cursor().execute(sql)
            self.conector.conn.commit()
            
            self.log(f"Registro insertado en Afiliacion: {num_afil}", success=True)
            return True
            
        except Exception as e:
            self.log(f"Error al insertar en Afiliacion: {str(e)}", error=True)
            import traceback
            traceback.print_exc()
            return False
    
    def insertar_trabs(self, datos):
        """Inserta un registro en la tabla Trabs"""
        try:
            # Extraer datos del registro
            reg_pat = datos['Registro Patronal']
            
            # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
            num_afil = str(datos['Número de Seguridad Social']).zfill(11)
            
            # Verificar si ya existe en la tabla Trabs
            query = f"SELECT COUNT(*) AS Total FROM Trabs WHERE IP = '{reg_pat}' AND Numero_Afiliacion = '{num_afil}'"
            resultado = self.conector.ejecutar_consulta(query)
            
            if resultado and resultado[0]['Total'] > 0:
                self.log(f"El trabajador ya existe en la tabla Trabs: {num_afil}", warning=True)
                return False
            
            # Construir la consulta SQL
            sql = f"""
            INSERT INTO Trabs (IP, Numero_Afiliacion)
            VALUES ('{reg_pat}', '{num_afil}')
            """
            
            # Ejecutar la consulta
            result = self.conector.conn.cursor().execute(sql)
            self.conector.conn.commit()
            
            self.log(f"Registro insertado en Trabs: {num_afil}", success=True)
            return True
            
        except Exception as e:
            self.log(f"Error al insertar en Trabs: {str(e)}", error=True)
            import traceback
            traceback.print_exc()
            return False
    
    def insertar_movtos(self, datos):
        """Inserta un registro en la tabla Movtos para el alta del asegurado"""
        try:
            # Extraer datos del registro
            reg_pat = datos['Registro Patronal']
            
            # Asegurar que el NSS tenga 11 dígitos con ceros a la izquierda
            num_afil = str(datos['Número de Seguridad Social']).zfill(11)
            
            # Tipo de movimiento: 01 (Alta)
            tip_movs = "01"
            
            # Fecha de inicio (misma que fecha de alta)
            fecha_alta = pd.to_datetime(datos['Fecha de alta']).strftime("%Y-%m-%d")
            
            # Salario diario integrado
            salario = float(datos['Salario diario integrado']) if pd.notna(datos['Salario diario integrado']) else 0.0
            
            # Valores fijos según la equivalencia proporcionada
            cve_movs = "A"  # Clave de movimiento para alta
            edo_mov = 1     # Estado del movimiento
            art_33 = "N"    # Artículo 33
            val_des = 0     # Valor de descuento
            
            # Construir la consulta SQL
            sql = f"""
            INSERT INTO Movtos (
                REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, 
                SAL_MOVT, CVE_MOVS, EDO_MOV, ART_33, Val_Des
            ) VALUES (
                '{reg_pat}', '{num_afil}', '{tip_movs}', '{fecha_alta}', 
                {salario}, '{cve_movs}', {edo_mov}, '{art_33}', {val_des}
            )
            """
            
            # Ejecutar la consulta
            result = self.conector.conn.cursor().execute(sql)
            self.conector.conn.commit()
            
            self.log(f"Registro insertado en Movtos: {num_afil} (Alta)", success=True)
            
            # Verificar si el asegurado tiene crédito Infonavit
            tiene_infonavit = False
            num_credito = None
            valor_descuento = None
            tipo_descuento = None
            
            if 'Numero Credito Infonavit' in datos and pd.notna(datos['Numero Credito Infonavit']) and str(datos['Numero Credito Infonavit']).strip():
                tiene_infonavit = True
                num_credito = str(datos['Numero Credito Infonavit']).strip()
                
                # Obtener valor del descuento
                if 'Valor Descuento' in datos and pd.notna(datos['Valor Descuento']):
                    valor_descuento = float(datos['Valor Descuento'])
                else:
                    valor_descuento = 0.0
                
                # Obtener tipo de descuento como VALOR NUMÉRICO
                tipo_descuento_codigo = 2  # Valor por defecto (Cuota Fija = 2)
                
                if 'Tipo Descuento' in datos and pd.notna(datos['Tipo Descuento']):
                    tipo_descuento_original = datos['Tipo Descuento']
                    
                    # Si es un número, usar directamente
                    if isinstance(tipo_descuento_original, (int, float)):
                        tipo_descuento_codigo = int(tipo_descuento_original)
                    # Si es string que contiene un número, convertir
                    elif isinstance(tipo_descuento_original, str) and tipo_descuento_original.strip().isdigit():
                        tipo_descuento_codigo = int(tipo_descuento_original)
                    # Si es un texto descriptivo, convertir a su código numérico
                    elif isinstance(tipo_descuento_original, str):
                        tipo_lower = tipo_descuento_original.lower().strip()
                        if 'porcentaje' in tipo_lower:
                            tipo_descuento_codigo = 1
                        elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
                            tipo_descuento_codigo = 2
                        elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
                            tipo_descuento_codigo = 3
                
                # SIEMPRE usar el código numérico, nunca el texto en la consulta SQL
                self.log(f"Tipo de descuento usado para NSS {num_afil} en Movtos: {tipo_descuento_codigo} (numérico)")
                
                # Convertir el código numérico al texto descriptivo para Movtos
                tipo_descuento_texto = {
                    1: "Porcentaje",
                    2: "Cuota Fija",
                    3: "Factor de Descuento"
                }.get(tipo_descuento_codigo, "Cuota Fija")  # Valor por defecto si no coincide
                
                self.log(f"Tipo de descuento (texto) usado para NSS {num_afil} en Movtos: '{tipo_descuento_texto}'")
                
                # Insertar registro de Inicio de Crédito Infonavit (tipo 15) con valor de TEXTO
                sql_infonavit = f"""
                INSERT INTO Movtos (
                    REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, 
                    SAL_MOVT, CVE_MOVS, EDO_MOV, ART_33, Num_Cre, Val_Des, Tip_Des, Tab_Dism
                ) VALUES (
                    '{reg_pat}', '{num_afil}', '15', '{fecha_alta}', 
                    {salario}, 'D', 0, '{art_33}', '{num_credito}', {valor_descuento}, '{tipo_descuento_texto}', 0
                )
                """
                
                try:
                    # Ejecutar la consulta
                    result_infonavit = self.conector.conn.cursor().execute(sql_infonavit)
                    self.conector.conn.commit()
                    self.log(f"Registro insertado en Movtos: {num_afil} (Inicio Crédito Infonavit)", success=True)
                except Exception as e:
                    self.log(f"Error al insertar registro Infonavit en Movtos: {str(e)}", error=True)
                    return False
            
            return True
            
        except Exception as e:
            self.log(f"Error al insertar en Movtos: {str(e)}", error=True)
            import traceback
            traceback.print_exc()
            return False

def main():
    """Función principal"""
    root = tk.Tk()
    app = CargaMasivaAseguradosApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 