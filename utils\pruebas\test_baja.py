from conectores.conector_sua import ConectorSUA
import pandas as pd
from datetime import datetime

def probar_baja():
    """Prueba la funcionalidad de baja del asegurado"""
    try:
        print("Conectando a la base de datos...")
        conector = ConectorSUA(password='S5@N52V49')
        if not conector.conectar():
            print("Error al conectar a la base de datos")
            return False
            
        # 1. Verificar que el asegurado existe y su estado actual
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        # Consultar estado actual
        print(f"\nConsultando empleado: {reg_patr} - {num_afil}")
        query_asegurado = f"""
        SELECT REG_PATR, NUM_AFIL, NOM_ASEG, FEC_ALT, FEC_BAJ, 
               TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc, Num_Cre
        FROM Asegura 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        """
        asegurado = conector.ejecutar_consulta(query_asegurado)
        
        if not asegurado:
            print(f"No se encontró el asegurado {num_afil} en el registro patronal {reg_patr}")
            return False
            
        print("Datos actuales del asegurado:")
        print(pd.DataFrame(asegurado))
        
        # 2. Consultar movimientos actuales del asegurado
        query_movtos = f"""
        SELECT REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CVE_MOVS, TIP_INC, 
               Num_Cre, Val_Des, Tip_Des 
        FROM Movtos
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        ORDER BY FEC_INIC DESC
        """
        movtos = conector.ejecutar_consulta(query_movtos)
        
        print("\nMovimientos actuales del asegurado:")
        print(pd.DataFrame(movtos))
        
        # 3. Contar bajas previas para verificar después
        query_bajas = f"""
        SELECT COUNT(*) as total_bajas
        FROM Movtos
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' AND TIP_MOVS = '02'
        """
        bajas_previas = conector.ejecutar_consulta(query_bajas)
        total_bajas = bajas_previas[0]['total_bajas'] if bajas_previas else 0
        print(f"\nTotal de bajas previas: {total_bajas}")
        
        # 4. Procesamos la baja
        fecha_baja = datetime.now().strftime("%Y-%m-%d")
        print(f"\nProcesando baja con fecha: {fecha_baja}")
        
        datos_baja = {
            'REG_PATR': reg_patr,
            'NUM_AFIL': num_afil,
            'FEC_INIC': fecha_baja
        }
        
        # Ejecutar la baja
        resultado = conector.procesar_baja(datos_baja)
        print(f"Resultado de la baja: {'Éxito' if resultado else 'Error'}")
        
        if resultado:
            # 5. Verificar cambios en Asegura
            asegurado_post = conector.ejecutar_consulta(query_asegurado)
            print("\nDatos del asegurado después de la baja:")
            print(pd.DataFrame(asegurado_post))
            
            # 6. Verificar movimientos generados
            movtos_post = conector.ejecutar_consulta(query_movtos)
            print("\nMovimientos después de la baja:")
            print(pd.DataFrame(movtos_post))
            
            # 7. Verificar que el contador de bajas aumentó
            bajas_nuevas = conector.ejecutar_consulta(query_bajas)
            total_bajas_nuevas = bajas_nuevas[0]['total_bajas'] if bajas_nuevas else 0
            print(f"\nTotal de bajas después del proceso: {total_bajas_nuevas}")
            
            if total_bajas_nuevas > total_bajas:
                print("✅ Se incrementó correctamente el contador de bajas")
            else:
                print("❌ No se incrementó el contador de bajas")
        
        # Desconectar
        conector.desconectar()
        return resultado
            
    except Exception as e:
        print(f"Error durante la prueba: {e}")
        return False

if __name__ == "__main__":
    probar_baja() 