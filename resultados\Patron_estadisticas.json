{"filas_muestra": 3, "columnas": 24, "tipos_datos": {"REG_PAT": "object", "RFC_PAT": "object", "NOM_PAT": "object", "ACT_PAT": "object", "DOM_PAT": "object", "MUN_PAT": "object", "CPP_PAT": "object", "ENT_PAT": "object", "TEL_PAT": "object", "REM_PAT": "bool", "ZON_PAT": "object", "DEL_PAT": "object", "CAR_ENT": "object", "NUM_DEL": "int64", "CAR_DEL": "object", "NUM_SUB": "int64", "CAR_SUB": "object", "TIP_CON": "float64", "CON_VEN": "object", "INI_AFIL": "object", "Pat_Rep": "object", "Clase": "object", "Fraccion": "object", "STyPS": "object"}, "valores_nulos": {"REG_PAT": 0, "RFC_PAT": 0, "NOM_PAT": 0, "ACT_PAT": 0, "DOM_PAT": 0, "MUN_PAT": 0, "CPP_PAT": 0, "ENT_PAT": 0, "TEL_PAT": 0, "REM_PAT": 0, "ZON_PAT": 0, "DEL_PAT": 0, "CAR_ENT": 0, "NUM_DEL": 0, "CAR_DEL": 0, "NUM_SUB": 0, "CAR_SUB": 0, "TIP_CON": 0, "CON_VEN": 3, "INI_AFIL": 0, "Pat_Rep": 0, "Clase": 0, "Fraccion": 0, "STyPS": 0}, "valores_unicos": {"REG_PAT": 3, "RFC_PAT": 3, "NOM_PAT": 3, "ACT_PAT": 3, "DOM_PAT": 3, "MUN_PAT": 2, "CPP_PAT": 2, "ENT_PAT": 2, "TEL_PAT": 3, "REM_PAT": 1, "ZON_PAT": 1, "DEL_PAT": 2, "CAR_ENT": 2, "NUM_DEL": 1, "CAR_DEL": 2, "NUM_SUB": 2, "CAR_SUB": 2, "TIP_CON": 1, "CON_VEN": 0, "INI_AFIL": 2, "Pat_Rep": 3, "Clase": 1, "Fraccion": 3, "STyPS": 1}}