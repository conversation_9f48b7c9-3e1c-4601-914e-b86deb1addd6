import requests
import json
import os
from datetime import datetime

# URL base de la API
BASE_URL = "http://localhost:8000/api/v1"

def test_generar_plantilla():
    """Prueba el endpoint de generación de plantilla"""
    print("\n=== Probando generación de plantilla ===")
    response = requests.get(f"{BASE_URL}/patrones/plantilla")
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Archivo generado: {data['archivo']}")
        print(f"Mensaje: {data['mensaje']}")
        return data['archivo']
    else:
        print("Error al generar plantilla:", response.text)
        return None

def test_carga_masiva(archivo_excel):
    """Prueba el endpoint de carga masiva"""
    print("\n=== Probando carga masiva ===")
    
    if not os.path.exists(archivo_excel):
        print(f"Error: No se encuentra el archivo {archivo_excel}")
        return
    
    with open(archivo_excel, 'rb') as f:
        files = {'file': (os.path.basename(archivo_excel), f)}
        response = requests.post(f"{BASE_URL}/patrones/carga-masiva", files=files)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("\nResumen de la carga:")
        print(f"Total registros: {data['total_registros']}")
        print(f"Exitosos: {data['exitosos']}")
        print(f"Fallidos: {data['fallidos']}")
        
        if 'errores' in data:
            print("\nErrores encontrados:")
            for error in data['errores']:
                print(f"- {error}")
    else:
        print("Error en la carga:", response.text)

def test_obtener_patron(registro_patronal):
    """Prueba el endpoint de obtención de patrón específico"""
    print(f"\n=== Probando obtención de patrón {registro_patronal} ===")
    response = requests.get(f"{BASE_URL}/patrones/{registro_patronal}")
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        patron = response.json()
        print("\nDatos del patrón:")
        print(json.dumps(patron, indent=2))
    else:
        print("Error al obtener patrón:", response.text)

def test_listar_patrones():
    """Prueba el endpoint de listado de patrones"""
    print("\n=== Probando listado de patrones ===")
    response = requests.get(f"{BASE_URL}/patrones/")
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        patrones = response.json()
        print(f"\nTotal de patrones: {len(patrones)}")
        if patrones:
            print("\nPrimeros 3 patrones:")
            for patron in patrones[:3]:
                print(json.dumps(patron, indent=2))
    else:
        print("Error al listar patrones:", response.text)

if __name__ == "__main__":
    # 1. Generar plantilla
    archivo_plantilla = test_generar_plantilla()
    if archivo_plantilla:
        print(f"\nPlantilla generada: {archivo_plantilla}")
        
        # 2. Probar carga masiva con la plantilla generada
        test_carga_masiva(archivo_plantilla)
        
        # 3. Probar obtención de un patrón específico
        test_obtener_patron("R1265455102")  # Usar un registro patronal de prueba
        
        # 4. Probar listado de patrones
        test_listar_patrones() 