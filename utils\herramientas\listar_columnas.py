import pyodbc

def main():
    try:
        # Establecer conexión
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa a la base de datos")
        
        # Obtener esquema de columnas usando ADOX
        schema = []
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Asegura WHERE 1=0")
        
        # Imprimir nombres de columnas
        print("\nColumnas de la tabla Asegura:")
        for i, column in enumerate(cursor.description):
            column_name = column[0]
            schema.append(column_name)
            print(f"{i+1}. {column_name}")
        
        print(f"\nTotal de columnas: {len(schema)}")
        
        # Consulta para verificar si existe TAB_DISM en alguna tabla
        print("\nBuscando campos TAB_DISM en otras tablas...")
        cursor.execute("SELECT TOP 1 * FROM Movtos WHERE 1=0")
        movtos_columns = [column[0] for column in cursor.description]
        
        if 'TAB_DISM' in movtos_columns:
            print("✅ El campo TAB_DISM existe en la tabla Movtos")
            idx = movtos_columns.index('TAB_DISM')
            column_info = cursor.description[idx]
            print(f"   Tipo: {column_info[1]}, Tamaño: {column_info[2]}")
        else:
            print("❌ El campo TAB_DISM no existe en la tabla Movtos")
        
        print("\nLista de columnas de la tabla Movtos:")
        for i, column_name in enumerate(movtos_columns):
            print(f"{i+1}. {column_name}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 