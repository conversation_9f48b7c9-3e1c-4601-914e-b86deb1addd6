"""
Script para verificar que los campos FEC_DSC y Fec_FinDsc se están actualizando correctamente
en la tabla Asegura después de procesar suspensiones y reinicios de crédito.
"""

from conectores.conector_sua import ConectorSUA

def main():
    # Conectar a la base de datos
    conector = ConectorSUA(password="S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    # Consultar datos de asegurado específico
    reg_patr = "E5352621109"
    num_afil = "04088625456"  # Este es el NSS utilizado en las pruebas
    
    print(f"\nConsultando datos del asegurado {num_afil} en el registro patronal {reg_patr}...")
    
    # Ejecutar consulta SQL
    query = f"""
    SELECT REG_PATR, NUM_AFIL, TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc, Num_Cre, PAG_INFO 
    FROM Asegura 
    WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
    """
    
    resultados = conector.ejecutar_consulta(query)
    
    if not resultados:
        print(f"No se encontró el asegurado {num_afil}")
    else:
        print("\nDatos del asegurado:")
        for campo, valor in resultados[0].items():
            print(f"{campo}: {valor}")
        
        # Verificar específicamente los campos de interés
        asegurado = resultados[0]
        if asegurado.get('FEC_DSC'):
            print(f"\n✅ Campo FEC_DSC está presente: {asegurado['FEC_DSC']}")
        else:
            print("\n❌ Campo FEC_DSC está vacío")
            
        if asegurado.get('Fec_FinDsc'):
            print(f"✅ Campo Fec_FinDsc está presente: {asegurado['Fec_FinDsc']}")
        else:
            print("❌ Campo Fec_FinDsc está vacío")
    
    # Consultar movimientos relacionados con crédito
    print("\nConsultando movimientos de crédito del asegurado...")
    query_movimientos = f"""
    SELECT TIP_MOVS, FEC_INIC, CVE_MOVS, Num_Cre, Tip_Des, Val_Des
    FROM Movtos 
    WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
    AND TIP_MOVS IN ('15', '16', '17')
    ORDER BY FEC_INIC DESC
    """
    
    movimientos = conector.ejecutar_consulta(query_movimientos)
    
    if not movimientos:
        print("No se encontraron movimientos de crédito")
    else:
        print("\nMovimientos de crédito:")
        for i, mov in enumerate(movimientos, 1):
            tipo = "Inicio" if mov['TIP_MOVS'] == '15' else "Suspensión" if mov['TIP_MOVS'] == '16' else "Reinicio"
            print(f"{i}. {tipo} de crédito - Fecha: {mov['FEC_INIC']}, Crédito: {mov['Num_Cre']}")
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 