# Carga Masiva de Patrones y Primas RT

Este documento explica el funcionamiento del sistema de carga masiva de patrones y primas RT, incluyendo detalles sobre las relaciones entre tablas, el manejo de valores por defecto y el formato de datos requerido.

## Mapeo de Campos

La carga masiva utiliza un archivo Excel con los siguientes campos que se mapean a la base de datos SUA:

| Campo Excel | Campo SUA | Tabla | Descripción |
|-------------|-----------|-------|-------------|
| Registro Patronal | REG_PAT | Patron | Identificador único del patrón (11 caracteres) |
| RFC | RFC_PAT | Patron | RFC del patrón |
| Razon Social | NOM_PAT | Patron | Nombre o razón social |
| Actividad Económica | ACT_PAT | Patron | Actividad económica del patrón |
| Domicilio | DOM_PAT | Patron | Domicilio fiscal |
| Municipio | MUN_PAT | Patron | Municipio |
| Código Postal | CPP_PAT | Patron | Código postal |
| Entidad Federativa | ENT_PAT | Patron | ID de entidad federativa (relación con tabla Estados) |
| Teléfono | TEL_PAT | Patron | Número telefónico |
| Rembolso de Subsidios | REM_PAT | Patron | Flag para reembolso (0/1) |
| Zona Salario | ZON_PAT | Patron | Zona salarial |
| Subdelegacion | DEL_PAT | Patron | ID de subdelegación (relación con tabla Subdelega) |
| Representante Legal | Pat_Rep | Patron | Nombre del representante legal |
| Clase | Clase | Patron | Clase de riesgo |
| Fracción | Fraccion | Patron | Fracción de riesgo |
| STyPS | STyPS | Patron | Número de registro ante STyPS |
| Fecha Prima de RT | PER_PRI | Prima_RT | Período de prima RT (fecha) |
| Prima RT | TASA_RT | Prima_RT | Tasa de riesgo de trabajo |

## Relaciones entre Tablas

### Manejo de Entidades Federativas y Subdelegaciones

La carga masiva procesa las relaciones entre tablas de la siguiente manera:

1. **Entidad Federativa (ENT_PAT)**:
   - Se busca el ID de la entidad federativa en la tabla `Estados` utilizando la descripción proporcionada
   - Método: `self.buscar_id_entidad(entidad_nombre)`
   - El valor encontrado se usa para el campo `ENT_PAT`

2. **Subdelegación (DEL_PAT)**:
   - Se busca el ID de la subdelegación en la tabla `Subdelega` utilizando la descripción proporcionada
   - Método: `self.buscar_id_subdelegacion(subdelegacion_nombre)`
   - El valor encontrado se usa para el campo `DEL_PAT`

3. **Número de Subdelegación (NUM_SUB)**:
   - Se obtiene automáticamente de la propiedad `posicion` del resultado de la búsqueda de subdelegación

## Manejo de Valores por Defecto

Para garantizar la continuidad del proceso de carga masiva, el sistema utiliza valores por defecto cuando no encuentra coincidencias exactas:

1. **Entidad Federativa por defecto**:
   - ID: 20 (correspondiente a Jalisco)
   - Se utiliza cuando no se encuentra la entidad en la tabla `Estados`
   
2. **Subdelegación por defecto**:
   - ID: 2153
   - Posición: 3
   - Se utiliza cuando no se encuentra la subdelegación en la tabla `Subdelega`

Esto permite que el proceso continúe aún cuando haya variaciones en los nombres o cuando la base de datos no tenga un registro exacto de la entidad o subdelegación proporcionada.

## Formato Especial para INI_AFIL

El campo `INI_AFIL` (fecha de afiliación inicial) se genera automáticamente a partir de la fecha de prima RT en formato "AAAAMM":

```python
# Formatear fecha para INI_AFIL (AAAAMM)
fecha_prima = datos.get('Fecha Prima de RT')
if pd.notna(fecha_prima):
    if isinstance(fecha_prima, str):
        fecha_prima = pd.to_datetime(fecha_prima)
    # Formato AAAAMM
    fecha_afil = fecha_prima.strftime("%Y%m")
```

## Script de Prueba con Valores Aleatorios

El script `probar_carga_patrones.py` incluye funcionalidad para generar registros patronales aleatorios, evitando problemas de duplicación:

```python
def generar_registro_patronal_aleatorio():
    """Genera un registro patronal aleatorio de 11 caracteres"""
    letras = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    numeros = '0123456789'
    
    # Formato: LAANNNNNNNN (L=letra, A=alfanumérico, N=número)
    registro = random.choice(letras)
    registro += random.choice(letras + numeros)
    registro += random.choice(letras + numeros)
    
    # Añadir 8 dígitos numéricos
    for _ in range(8):
        registro += random.choice(numeros)
    
    return registro
```

Este enfoque garantiza que los registros generados cumplan con el formato requerido de 11 caracteres y sean únicos para propósitos de prueba.

## Consideraciones para la Carga Masiva

1. **Datos requeridos**:
   - El registro patronal (`REG_PAT`) es obligatorio y debe ser único
   - La fecha de prima RT es necesaria para el cálculo de `INI_AFIL`

2. **Tratamiento de errores**:
   - Los errores se registran en archivos log dentro de la carpeta `resultados/`
   - Los registros con errores se omiten, pero la carga continúa con el siguiente registro
   
3. **Advertencias**:
   - Cuando se utilizan valores por defecto, se genera una advertencia en el log
   - Las advertencias no detienen el proceso, pero deben revisarse posteriormente 