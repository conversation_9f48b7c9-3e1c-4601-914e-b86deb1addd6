"""
Script para examinar los registros de bajas en las tablas Movtos y Asegura.
Este script analiza cómo se manejan las bajas en el sistema SUA.
"""

import os
import sys
import pandas as pd
import pyodbc
from pathlib import Path

# Configuración
DB_PATH = r'C:\Cobranza\SUA\SUA.MDB'
DB_PASSWORD = "S5@N52V49"
RESULTS_DIR = 'resultados'

def crear_directorio_si_no_existe(directorio):
    """Crea un directorio si no existe"""
    Path(directorio).mkdir(parents=True, exist_ok=True)

def guardar_dataframe_a_csv(df, nombre_archivo, directorio="resultados"):
    """Guarda un DataFrame en un archivo CSV"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{nombre_archivo}.csv")
    df.to_csv(ruta_completa, index=False, encoding='utf-8-sig')
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def conectar_bd():
    """Establece conexión con la base de datos SUA"""
    print(f"Conectando a la base de datos: {DB_PATH}")
    try:
        connection_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={DB_PATH};PWD={DB_PASSWORD}"
        conn = pyodbc.connect(connection_str)
        print("Conexión exitosa a la base de datos.")
        return conn
    except pyodbc.Error as e:
        print(f"Error: {str(e)}")
        return None

def obtener_movimientos_tipo(conn, tipo_movimiento):
    """Obtiene los movimientos de un tipo específico"""
    cursor = conn.cursor()
    query = f"""
    SELECT 
        REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CVE_MOVS, EDO_MOV
    FROM 
        Movtos 
    WHERE 
        TIP_MOVS = '{tipo_movimiento}'
    """
    
    cursor.execute(query)
    columns = ['REG_PATR', 'NUM_AFIL', 'TIP_MOVS', 'FEC_INIC', 'CVE_MOVS', 'EDO_MOV']
    rows = cursor.fetchall()
    
    if not rows:
        print(f"No se encontraron movimientos de tipo {tipo_movimiento}")
        return None
    
    df = pd.DataFrame.from_records(rows, columns=columns)
    return df

def examinar_asegurados_por_nss(conn, num_afil_list):
    """Examina los asegurados por NSS individualmente"""
    resultado = []
    for nss in num_afil_list:
        cursor = conn.cursor()
        query = f"SELECT REG_PATR, NUM_AFIL, ESTADO, FEC_ALT, FEC_BAJ FROM Asegura WHERE NUM_AFIL = '{nss}'"
        try:
            cursor.execute(query)
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    print(f"Asegurado: {row}")
                    resultado.append({
                        'REG_PATR': row[0],
                        'NUM_AFIL': row[1],
                        'ESTADO': row[2],
                        'FEC_ALT': row[3],
                        'FEC_BAJ': row[4]
                    })
            else:
                print(f"No se encontró el asegurado con NSS: {nss}")
        except Exception as e:
            print(f"Error al consultar asegurado {nss}: {e}")
    
    if resultado:
        return pd.DataFrame(resultado)
    return None

def obtener_estructura_tabla(conn, tabla):
    """Obtiene la estructura de columnas de una tabla"""
    cursor = conn.cursor()
    query = f"SELECT TOP 1 * FROM {tabla}"
    cursor.execute(query)
    
    columns = [column[0] for column in cursor.description]
    tipos = [column[1].__name__ for column in cursor.description]
    
    result = pd.DataFrame({
        'Columna': columns,
        'Tipo': tipos
    })
    
    return result

def analizar_relacion_movtos_asegura(conn):
    """Analiza la relación entre movimientos de baja y el estado en Asegura"""
    print("\n=== ANÁLISIS DE RELACIÓN MOVTOS-ASEGURA PARA BAJAS ===")
    
    try:
        # Obtener movimientos de baja
        cursor = conn.cursor()
        query_bajas = """
        SELECT m.REG_PATR, m.NUM_AFIL, m.TIP_MOVS, m.FEC_INIC, m.CVE_MOVS, m.EDO_MOV 
        FROM Movtos m
        WHERE m.TIP_MOVS = '02'
        """
        cursor.execute(query_bajas)
        bajas = cursor.fetchall()
        
        if not bajas:
            print("No se encontraron movimientos de baja")
            return
        
        # Analizar cada baja
        for baja in bajas:
            reg_pat, num_afil, tip_movs, fec_inic, cve_movs, edo_mov = baja
            print(f"\nMovimiento Baja: REG_PATR={reg_pat}, NUM_AFIL={num_afil}, FECHA={fec_inic}")
            
            # Buscar en Asegura
            query_asegura = f"""
            SELECT ESTADO, FEC_ALT, FEC_BAJ, TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc 
            FROM Asegura 
            WHERE REG_PATR = '{reg_pat}' AND NUM_AFIL = '{num_afil}'
            """
            
            cursor.execute(query_asegura)
            asegura_row = cursor.fetchone()
            
            if asegura_row:
                estado, fec_alt, fec_baj, tip_dsc, val_dsc, fec_dsc, fec_findsc = asegura_row
                print(f"  Asegura: ESTADO={estado}, FEC_ALT={fec_alt}, FEC_BAJ={fec_baj}")
                print(f"  Info. Infonavit: TIP_DSC={tip_dsc}, VAL_DSC={val_dsc}, FEC_DSC={fec_dsc}, Fec_FinDsc={fec_findsc}")
                
                # Análisis de la relación
                if estado == 'B':
                    print("  ✓ ESTADO correctamente establecido como 'B' (Baja)")
                else:
                    print(f"  ✗ ESTADO no refleja baja: {estado}")
                
                if fec_baj:
                    if fec_baj == fec_inic:
                        print("  ✓ FEC_BAJ coincide con la fecha del movimiento de baja")
                    else:
                        print(f"  ✗ FEC_BAJ ({fec_baj}) no coincide con fecha del movimiento ({fec_inic})")
                else:
                    print("  ✗ FEC_BAJ no está establecida")

                # Verificar si había crédito Infonavit y su fecha fin
                if tip_dsc and tip_dsc.strip():
                    print(f"  ✓ Tenía crédito Infonavit tipo: {tip_dsc}")
                    
                    # Buscar movimientos de tipo 16 (Terminación crédito Infonavit)
                    query_fin_credito = f"""
                    SELECT TIP_MOVS, FEC_INIC, CVE_MOVS, EDO_MOV 
                    FROM Movtos 
                    WHERE REG_PATR = '{reg_pat}' AND NUM_AFIL = '{num_afil}' AND TIP_MOVS = '16'
                    """
                    cursor.execute(query_fin_credito)
                    fin_creditos = cursor.fetchall()
                    
                    if fin_creditos:
                        print("  ✓ Se encontraron movimientos de terminación de crédito Infonavit:")
                        for fin in fin_creditos:
                            tip_movs_fin, fec_inic_fin, cve_movs_fin, edo_mov_fin = fin
                            print(f"    - Fecha: {fec_inic_fin}, CVE_MOVS: {cve_movs_fin}, EDO_MOV: {edo_mov_fin}")
                            
                            # Verificar si la fecha fin de descuento coincide
                            if fec_findsc:
                                if fec_findsc == fec_inic_fin:
                                    print(f"    ✓ Fec_FinDsc coincide con fecha del movimiento tipo 16")
                                else:
                                    print(f"    ✗ Fec_FinDsc ({fec_findsc}) no coincide con fecha del movimiento ({fec_inic_fin})")
                    else:
                        print("  ✗ No se encontraron movimientos de terminación de crédito Infonavit")
            else:
                print("  ✗ No se encontró el registro en Asegura")
    
    except Exception as e:
        print(f"Error al analizar relación: {e}")

def analizar_relacion_creditos_bajas(conn):
    """Analiza la relación entre movimientos de baja y créditos Infonavit"""
    print("\n=== ANÁLISIS DE RELACIÓN ENTRE BAJAS Y CRÉDITOS INFONAVIT ===")
    
    try:
        # Buscar movimientos de tipo 02 (Baja) con movimientos tipo 16 (Fin crédito) relacionados
        cursor = conn.cursor()
        query = """
        SELECT 
            m1.REG_PATR, m1.NUM_AFIL, m1.FEC_INIC AS FECHA_BAJA, 
            m2.TIP_MOVS, m2.FEC_INIC AS FECHA_FIN_CREDITO
        FROM 
            Movtos m1
        INNER JOIN 
            Movtos m2 
        ON 
            m1.REG_PATR = m2.REG_PATR AND m1.NUM_AFIL = m2.NUM_AFIL
        WHERE 
            m1.TIP_MOVS = '02' AND m2.TIP_MOVS = '16'
        ORDER BY 
            m1.REG_PATR, m1.NUM_AFIL, m1.FEC_INIC
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        if not rows:
            print("No se encontraron bajas relacionadas con finalizaciones de crédito")
            return
        
        print(f"Se encontraron {len(rows)} relaciones entre bajas y finalizaciones de crédito:")
        
        # Crear un diccionario para agrupar por asegurado
        asegurados = {}
        for row in rows:
            reg_pat, num_afil, fecha_baja, tip_movs, fecha_fin_credito = row
            key = f"{reg_pat}_{num_afil}"
            
            if key not in asegurados:
                asegurados[key] = {
                    'REG_PATR': reg_pat,
                    'NUM_AFIL': num_afil,
                    'FECHA_BAJA': fecha_baja,
                    'MOVIMIENTOS_FIN_CREDITO': []
                }
            
            asegurados[key]['MOVIMIENTOS_FIN_CREDITO'].append({
                'TIP_MOVS': tip_movs,
                'FECHA_FIN_CREDITO': fecha_fin_credito
            })
        
        # Mostrar resultados agrupados
        for key, asegurado in asegurados.items():
            print(f"\nAsegurado: REG_PATR={asegurado['REG_PATR']}, NUM_AFIL={asegurado['NUM_AFIL']}")
            print(f"  Fecha de baja: {asegurado['FECHA_BAJA']}")
            
            # Obtener información de Asegura
            query_asegura = f"""
            SELECT ESTADO, FEC_BAJ, TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc  
            FROM Asegura 
            WHERE REG_PATR = '{asegurado['REG_PATR']}' AND NUM_AFIL = '{asegurado['NUM_AFIL']}'
            """
            cursor.execute(query_asegura)
            asegura_info = cursor.fetchone()
            
            if asegura_info:
                estado, fec_baj, tip_dsc, val_dsc, fec_dsc, fec_findsc = asegura_info
                print(f"  Asegura: ESTADO={estado}, FEC_BAJ={fec_baj}")
                print(f"  Info. Infonavit: TIP_DSC={tip_dsc}, VAL_DSC={val_dsc}, FEC_DSC={fec_dsc}, Fec_FinDsc={fec_findsc}")
            
            # Mostrar movimientos de fin de crédito
            print("  Movimientos de fin de crédito:")
            for mov in asegurado['MOVIMIENTOS_FIN_CREDITO']:
                print(f"    - Tipo: {mov['TIP_MOVS']}, Fecha: {mov['FECHA_FIN_CREDITO']}")
                
                # Verificar relación temporal entre baja y fin de crédito
                if asegurado['FECHA_BAJA'] == mov['FECHA_FIN_CREDITO']:
                    print("    ✓ La fecha de baja coincide con la fecha de fin de crédito")
                else:
                    print(f"    ✗ La fecha de baja ({asegurado['FECHA_BAJA']}) no coincide con la fecha de fin de crédito ({mov['FECHA_FIN_CREDITO']})")
    
    except Exception as e:
        print(f"Error al analizar relación entre bajas y créditos: {e}")

def procesar_carga_masiva_movimientos(archivo_excel, tipo_movimiento="02", conn=None, guardar_resultados=True):
    """
    Procesa una carga masiva de movimientos desde un archivo Excel.
    
    Args:
        archivo_excel (str): Ruta al archivo Excel con los datos
        tipo_movimiento (str): Tipo de movimiento a procesar (por defecto "02" para bajas)
        conn (pyodbc.Connection, optional): Conexión a la BD. Si es None, se crea una nueva
        guardar_resultados (bool): Si es True, guarda los resultados en archivos CSV
        
    Returns:
        dict: Diccionario con resultados del procesamiento
    """
    try:
        cerrar_conn = False
        if conn is None:
            conn = conectar_bd()
            cerrar_conn = True
            
        if conn is None:
            return {"error": "No se pudo conectar a la base de datos"}
            
        print(f"\n=== PROCESANDO CARGA MASIVA DE MOVIMIENTOS TIPO {tipo_movimiento} ===")
        print(f"Archivo: {archivo_excel}")
        
        # Leer el archivo Excel
        try:
            df = pd.read_excel(archivo_excel)
            print(f"Se leyeron {len(df)} registros del archivo Excel")
        except Exception as e:
            return {"error": f"Error al leer archivo Excel: {str(e)}"}
        
        # Verificar columnas obligatorias
        columnas_requeridas = [
            "REG_PATR", 
            "NUM_AFIL", 
            "NOMBRE", 
            "FECHA_MOVIMIENTO", 
            "TIPO_MOVIMIENTO"
        ]
        
        for col in columnas_requeridas:
            if col not in df.columns:
                return {"error": f"Columna requerida '{col}' no encontrada en el archivo"}
        
        # Filtrar por tipo de movimiento si se especificó uno
        if tipo_movimiento:
            df_filtrada = df[df['TIPO_MOVIMIENTO'] == tipo_movimiento].copy()
            if len(df_filtrada) == 0:
                return {"error": f"No se encontraron movimientos de tipo {tipo_movimiento} en el archivo"}
        else:
            df_filtrada = df.copy()
        
        print(f"Se procesarán {len(df_filtrada)} movimientos de tipo {tipo_movimiento}")
        
        # Preparar resultados
        resultados = {
            "total_registros": len(df_filtrada),
            "exitosos": 0,
            "fallidos": 0,
            "detalles": []
        }
        
        # Procesar cada registro
        cursor = conn.cursor()
        
        for idx, row in df_filtrada.iterrows():
            reg_patr = str(row['REG_PATR']).strip()
            num_afil = str(row['NUM_AFIL']).strip()
            fecha_mov = pd.to_datetime(row['FECHA_MOVIMIENTO'])
            
            # Preparar diccionario para guardar resultado de este registro
            resultado_registro = {
                "REG_PATR": reg_patr,
                "NUM_AFIL": num_afil,
                "NOMBRE": row['NOMBRE'] if 'NOMBRE' in row else "",
                "FECHA_MOVIMIENTO": fecha_mov.strftime('%Y-%m-%d'),
                "exito": False,
                "mensaje": ""
            }
            
            # Verificar si el trabajador existe en la tabla Asegura
            try:
                query_verif = f"""
                SELECT ESTADO, FEC_ALT, FEC_BAJ, TIP_DSC, VAL_DSC, FEC_DSC, Fec_FinDsc
                FROM Asegura 
                WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
                """
                cursor.execute(query_verif)
                asegurado = cursor.fetchone()
                
                if not asegurado:
                    resultado_registro["mensaje"] = f"Error: Trabajador no encontrado en la tabla Asegura"
                    resultados["fallidos"] += 1
                    resultados["detalles"].append(resultado_registro)
                    continue
                
                estado, fec_alt, fec_baj, tip_dsc, val_dsc, fec_dsc, fec_findsc = asegurado
                
                # Lógica específica según el tipo de movimiento
                if tipo_movimiento == "02":  # BAJA
                    # Verificar si ya está dado de baja
                    if estado == "B":
                        resultado_registro["mensaje"] = f"Error: Trabajador ya está dado de baja (ESTADO=B)"
                        resultados["fallidos"] += 1
                        resultados["detalles"].append(resultado_registro)
                        continue
                        
                    # Verificar si hay un crédito Infonavit activo
                    tiene_credito = tip_dsc and tip_dsc.strip() and val_dsc
                    
                    # Insertar movimiento de baja en la tabla Movtos
                    query_insert = f"""
                    INSERT INTO Movtos (REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CVE_MOVS, EDO_MOV)
                    VALUES ('{reg_patr}', '{num_afil}', '02', '{fecha_mov.strftime("%Y-%m-%d")}', 'H', 0)
                    """
                    
                    cursor.execute(query_insert)
                    
                    # Si tiene crédito Infonavit, también insertar movimiento fin crédito
                    if tiene_credito:
                        query_finc = f"""
                        INSERT INTO Movtos (REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CVE_MOVS, EDO_MOV)
                        VALUES ('{reg_patr}', '{num_afil}', '16', '{fecha_mov.strftime("%Y-%m-%d")}', 'G', 0)
                        """
                        cursor.execute(query_finc)
                        
                    # Actualizar tabla Asegura
                    query_update = f"""
                    UPDATE Asegura 
                    SET ESTADO = 'B', 
                        FEC_BAJ = '{fecha_mov.strftime("%Y-%m-%d")}'
                    """
                    
                    # Si tiene crédito, actualizar también fecha fin de descuento
                    if tiene_credito:
                        query_update += f", Fec_FinDsc = '{fecha_mov.strftime('%Y-%m-%d')}'"
                        
                    query_update += f" WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
                    
                    cursor.execute(query_update)
                    
                    resultado_registro["exito"] = True
                    resultado_registro["mensaje"] = f"OK. Baja registrada. "
                    if tiene_credito:
                        resultado_registro["mensaje"] += "También se registró fin de crédito Infonavit."
                    
                    resultados["exitosos"] += 1
                
                # Aquí se pueden agregar más tipos de movimientos en el futuro
                
            except Exception as e:
                resultado_registro["mensaje"] = f"Error: {str(e)}"
                resultados["fallidos"] += 1
            
            resultados["detalles"].append(resultado_registro)
        
        # Confirmar cambios
        conn.commit()
        
        # Guardar resultados en CSV si se solicitó
        if guardar_resultados:
            df_resultados = pd.DataFrame(resultados["detalles"])
            nombre_archivo = f"resultados_carga_movimientos_{tipo_movimiento}"
            guardar_dataframe_a_csv(df_resultados, nombre_archivo)
        
        # Cerrar conexión si se creó aquí
        if cerrar_conn:
            conn.close()
            print("Conexión cerrada.")
            
        return resultados
        
    except Exception as e:
        print(f"Error general en procesamiento: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def generar_plantilla_movimientos(ruta_salida=None):
    """
    Genera una plantilla Excel para carga masiva de movimientos
    
    Args:
        ruta_salida (str, optional): Ruta donde guardar el archivo. Si es None,
                                    se guarda en el directorio de resultados.
    
    Returns:
        str: Ruta del archivo generado
    """
    try:
        if ruta_salida is None:
            crear_directorio_si_no_existe(RESULTS_DIR)
            ruta_salida = os.path.join(RESULTS_DIR, "plantilla_movimientos.xlsx")
        
        # Crear DataFrame con encabezados
        encabezados = [
            "REG_PATR",      # Registro patronal
            "NUM_AFIL",      # NSS del trabajador
            "NOMBRE",        # Nombre del trabajador (referencia, no se procesa)
            "FECHA_MOVIMIENTO",  # Fecha del movimiento
            "TIPO_MOVIMIENTO",   # Código del tipo de movimiento
            "SALARIO",       # Salario (para altas o modificaciones)
            "TIPO_DESCUENTO",    # Tipo de descuento Infonavit (para movs. 15)
            "VALOR_DESCUENTO",   # Valor del descuento Infonavit
            "OBSERVACIONES"      # Campo para notas o referencias
        ]
        
        # Crear DataFrame con una fila de ejemplo
        ejemplo = {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "JUAN PEREZ RODRIGUEZ",
            "FECHA_MOVIMIENTO": pd.Timestamp("2025-05-15"),
            "TIPO_MOVIMIENTO": "02",  # 02=Baja
            "SALARIO": 0.0,           # No aplicable para bajas
            "TIPO_DESCUENTO": "",     # No aplicable para bajas
            "VALOR_DESCUENTO": None,  # No aplicable para bajas
            "OBSERVACIONES": "Baja por renuncia voluntaria"
        }
        
        df_template = pd.DataFrame([ejemplo])
        
        # Crear tabla de referencia de tipos de movimiento
        tipos_movimiento = {
            "01": "Alta",
            "02": "Baja",
            "07": "Modificación Salario",
            "15": "Inicio Crédito Infonavit",
            "16": "Fin Crédito Infonavit",
            "17": "Modificación Crédito Infonavit"
        }
        
        df_tipos = pd.DataFrame(list(tipos_movimiento.items()), 
                              columns=["CÓDIGO", "DESCRIPCIÓN"])
        
        # Guardar en Excel con dos hojas
        with pd.ExcelWriter(ruta_salida, engine='openpyxl') as writer:
            df_template.to_excel(writer, sheet_name='Datos', index=False)
            df_tipos.to_excel(writer, sheet_name='Catálogo Movimientos', index=False)
        
        print(f"Plantilla de movimientos generada en: {ruta_salida}")
        return ruta_salida
        
    except Exception as e:
        print(f"Error al generar plantilla: {str(e)}")
        return None

def main():
    """Función principal"""
    try:
        # Crear directorio de resultados si no existe
        crear_directorio_si_no_existe(RESULTS_DIR)
        
        # Conectar a la base de datos
        conn = conectar_bd()
        if not conn:
            print("No se pudo conectar a la base de datos. Terminando.")
            return
        
        # Examinar estructura de tablas
        print("\n=== ESTRUCTURA DE LA TABLA MOVTOS ===")
        estructura_movtos = obtener_estructura_tabla(conn, "Movtos")
        print(estructura_movtos)
        guardar_dataframe_a_csv(estructura_movtos, "estructura_movtos")
        
        print("\n=== ESTRUCTURA DE LA TABLA ASEGURA ===")
        estructura_asegura = obtener_estructura_tabla(conn, "Asegura")
        print(estructura_asegura)
        guardar_dataframe_a_csv(estructura_asegura, "estructura_asegura")
        
        # Obtener movimientos de tipo 02 (Bajas)
        print("\n=== MOVIMIENTOS DE BAJA (TIPO 02) ===")
        movtos_baja = obtener_movimientos_tipo(conn, "02")
        if movtos_baja is not None:
            print(movtos_baja)
            guardar_dataframe_a_csv(movtos_baja, "movtos_baja")
            
            # Obtener NSS de asegurados con bajas
            nss_list = movtos_baja['NUM_AFIL'].unique().tolist()
            
            # Consultar esos asegurados en la tabla Asegura
            print("\n=== REGISTROS EN ASEGURA DE ASEGURADOS CON BAJAS ===")
            asegurados_df = examinar_asegurados_por_nss(conn, nss_list)
            if asegurados_df is not None:
                print(asegurados_df)
                guardar_dataframe_a_csv(asegurados_df, "asegurados_con_bajas")
        
        # Análisis detallado de la relación entre movimientos y Asegura
        analizar_relacion_movtos_asegura(conn)
        
        # Análisis de la relación entre bajas y créditos Infonavit
        analizar_relacion_creditos_bajas(conn)
        
        # Generar plantilla para carga masiva de movimientos
        print("\n=== GENERANDO PLANTILLA PARA CARGA MASIVA ===")
        plantilla_path = generar_plantilla_movimientos()
        
        # Ejemplos específicos de movimientos para analizar el proceso completo
        tipos_movimiento = ["01", "02", "07", "15", "16", "17"]
        
        for tipo in tipos_movimiento:
            print(f"\n=== ANÁLISIS DE MOVIMIENTOS TIPO {tipo} ===")
            movtos = obtener_movimientos_tipo(conn, tipo)
            if movtos is not None:
                print(f"Se encontraron {len(movtos)} registros")
                print(movtos.head(2) if len(movtos) > 1 else movtos)
                guardar_dataframe_a_csv(movtos, f"movtos_tipo_{tipo}")
                
                # Obtener asegurados para este tipo de movimiento también
                if len(movtos) > 0:
                    nss_list = movtos['NUM_AFIL'].unique().tolist()
                    print(f"Consultando asegurados para movimientos tipo {tipo}...")
                    asegurados_tipo = examinar_asegurados_por_nss(conn, nss_list)
                    if asegurados_tipo is not None:
                        guardar_dataframe_a_csv(asegurados_tipo, f"asegurados_tipo_{tipo}")
        
        # Cerrar conexión
        conn.close()
        print("\nConexión cerrada. Análisis completado.")
        
    except Exception as e:
        print(f"Error general: {str(e)}")
        import traceback
        traceback.print_exc()

def ejecutar_carga_de_prueba():
    """
    Ejecuta una carga de prueba con datos simulados para validar el proceso
    """
    try:
        print("\n=== EJECUTANDO CARGA DE PRUEBA ===")
        
        # Crear un archivo Excel temporal con datos de prueba
        crear_directorio_si_no_existe(RESULTS_DIR)
        archivo_prueba = os.path.join(RESULTS_DIR, "datos_prueba_bajas.xlsx")
        
        # Datos de prueba
        datos_prueba = [
            {
                "REG_PATR": "E5352621109",
                "NUM_AFIL": "04088625456",
                "NOMBRE": "JUAN PÉREZ RODRÍGUEZ",
                "FECHA_MOVIMIENTO": pd.Timestamp("2025-06-01"),
                "TIPO_MOVIMIENTO": "02",
                "SALARIO": 0.0,
                "TIPO_DESCUENTO": "",
                "VALOR_DESCUENTO": None,
                "OBSERVACIONES": "Baja por renuncia voluntaria - PRUEBA"
            },
            {
                "REG_PATR": "E5352621109",
                "NUM_AFIL": "04196525874",  # Otro trabajador
                "NOMBRE": "MARÍA LÓPEZ HERNÁNDEZ",
                "FECHA_MOVIMIENTO": pd.Timestamp("2025-06-01"),
                "TIPO_MOVIMIENTO": "02",
                "SALARIO": 0.0,
                "TIPO_DESCUENTO": "",
                "VALOR_DESCUENTO": None,
                "OBSERVACIONES": "Baja por término de contrato - PRUEBA"
            }
        ]
        
        # Crear DataFrame y guardar en Excel
        df_prueba = pd.DataFrame(datos_prueba)
        df_prueba.to_excel(archivo_prueba, index=False)
        
        print(f"Archivo de prueba creado: {archivo_prueba}")
        
        # Procesar carga de prueba
        conn = conectar_bd()
        if not conn:
            print("No se pudo conectar a la base de datos. Terminando prueba.")
            return
        
        # Usar la función de carga masiva
        resultados = procesar_carga_masiva_movimientos(
            archivo_excel=archivo_prueba,
            tipo_movimiento="02",
            conn=conn,
            guardar_resultados=True
        )
        
        # Mostrar resultados
        print("\nRESULTADOS DE LA CARGA DE PRUEBA:")
        print(f"Total de registros: {resultados.get('total_registros', 0)}")
        print(f"Exitosos: {resultados.get('exitosos', 0)}")
        print(f"Fallidos: {resultados.get('fallidos', 0)}")
        
        # Mostrar detalles
        if 'detalles' in resultados:
            for i, detalle in enumerate(resultados['detalles']):
                print(f"\nRegistro {i+1}:")
                print(f"  REG_PATR: {detalle.get('REG_PATR')}")
                print(f"  NUM_AFIL: {detalle.get('NUM_AFIL')}")
                print(f"  NOMBRE: {detalle.get('NOMBRE')}")
                print(f"  FECHA: {detalle.get('FECHA_MOVIMIENTO')}")
                print(f"  Resultado: {'Éxito' if detalle.get('exito') else 'Fallido'}")
                print(f"  Mensaje: {detalle.get('mensaje')}")
        
        # Cerrar conexión
        conn.close()
        print("\nPrueba de carga completada.")
        
    except Exception as e:
        print(f"Error en prueba de carga: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Si se proporciona el argumento --test, ejecutar carga de prueba
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        ejecutar_carga_de_prueba()
    # Si se proporciona el argumento --plantilla, solo generar la plantilla
    elif len(sys.argv) > 1 and sys.argv[1] == "--plantilla":
        generar_plantilla_movimientos()
    # Si se proporciona una ruta a un archivo Excel, procesarlo
    elif len(sys.argv) > 1 and sys.argv[1].endswith(('.xlsx', '.xls')):
        tipo = "02"  # Por defecto, procesar bajas
        if len(sys.argv) > 2:
            tipo = sys.argv[2]
        procesar_carga_masiva_movimientos(sys.argv[1], tipo_movimiento=tipo)
    else:
        main() 