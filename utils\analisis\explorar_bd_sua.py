"""
Exploración de la base de datos SUA.MDB.
Este script se conecta a la base de datos SUA.MDB y explora las tablas Patron y Prima_RT.
"""

import os
import sys
import pandas as pd
import pyodbc
import getpass

def solicitar_credenciales():
    """Solicita al usuario la ruta y contraseña de la base de datos"""
    print("\n=== CONFIGURACIÓN DE LA BASE DE DATOS ===")
    
    # Solicitar ruta de la base de datos
    ruta_predeterminada = r'C:\Cobranza\SUA\SUA.MDB'
    ruta_input = input(f"Ruta a la base de datos SUA [Enter para usar {ruta_predeterminada}]: ")
    ruta_bd = ruta_input.strip() if ruta_input.strip() else ruta_predeterminada
    
    # Verificar si la base existe
    if not os.path.exists(ruta_bd):
        print(f"¡Advertencia! La ruta {ruta_bd} no existe.")
        opciones = input("¿Desea continuar de todos modos? (s/n): ").lower()
        if opciones != 's':
            print("Operación cancelada.")
            sys.exit(0)
    
    # Usar contraseña fija
    password = "S5@N52V49"
    print("Usando contraseña predeterminada para la base de datos.")
    
    return ruta_bd, password

def conectar_bd_sua(ruta_bd=None, password=None):
    """Establece conexión con la base de datos SUA.MDB"""
    try:
        # Si no se proporciona ruta, usar la predeterminada
        if ruta_bd is None:
            ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
        
        # Verificar si el archivo existe
        if not os.path.exists(ruta_bd):
            print(f"Error: No se encontró la base de datos en {ruta_bd}")
            return None
        
        # Establecer la conexión
        if password:
            # Si hay contraseña, incluirla en la cadena de conexión
            connection_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                f'DBQ={ruta_bd};'
                f'PWD={password};'
            )
        else:
            # Intentar conectar sin contraseña
            connection_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                f'DBQ={ruta_bd};'
                r'ReadOnly=0;Exclusive=0;'  # Modo no exclusivo, sólo lectura
            )
        
        conn = pyodbc.connect(connection_str)
        print(f"Conexión exitosa a la base de datos: {ruta_bd}")
        return conn
    
    except pyodbc.Error as e:
        # Si el error es por contraseña, dar un mensaje más específico
        if "No es una contraseña válida" in str(e):
            print("Error: La base de datos está protegida por contraseña. Por favor proporcione la contraseña correcta.")
            print(f"Detalles del error: {e}")
        else:
            print(f"Error al conectar a la base de datos: {e}")
        return None

def listar_tablas(conn):
    """Lista todas las tablas en la base de datos"""
    if not conn:
        return []
    
    cursor = conn.cursor()
    tablas = []
    
    # En MS Access, la consulta para obtener tablas es diferente
    for row in cursor.tables(tableType='TABLE'):
        tablas.append(row.table_name)
    
    return tablas

def obtener_estructura_tabla(conn, nombre_tabla):
    """Obtiene la estructura (columnas) de una tabla específica"""
    if not conn:
        return []
    
    cursor = conn.cursor()
    columnas = []
    
    try:
        # Ejecutar una consulta que no devuelve datos para obtener las columnas
        cursor.execute(f"SELECT TOP 0 * FROM [{nombre_tabla}]")
        columnas = [column[0] for column in cursor.description]
    except pyodbc.Error as e:
        print(f"Error al obtener estructura de la tabla {nombre_tabla}: {e}")
    
    return columnas

def obtener_datos_tabla(conn, nombre_tabla, limite=10):
    """Obtiene los primeros N registros de una tabla"""
    if not conn:
        return pd.DataFrame()
    
    try:
        # Usar pandas para leer directamente la tabla
        query = f"SELECT TOP {limite} * FROM [{nombre_tabla}]"
        df = pd.read_sql(query, conn)
        return df
    except Exception as e:
        print(f"Error al obtener datos de la tabla {nombre_tabla}: {e}")
        return pd.DataFrame()

def guardar_resultados_csv(df, nombre_tabla):
    """Guarda los resultados en un archivo CSV"""
    if df.empty:
        return False
    
    try:
        nombre_archivo = f"{nombre_tabla}_datos.csv"
        df.to_csv(nombre_archivo, index=False, encoding='utf-8-sig')
        print(f"Datos guardados en {nombre_archivo}")
        return True
    except Exception as e:
        print(f"Error al guardar CSV: {e}")
        return False

def reintentar_conexion():
    """Devuelve la contraseña fija para reintentar la conexión"""
    print("Reintentando con la contraseña predeterminada...")
    return "S5@N52V49"

def main():
    """Función principal"""
    print("Iniciando exploración de la base de datos SUA.MDB...")
    
    # Solicitar credenciales
    ruta_bd, password = solicitar_credenciales()
    
    # Conectar a la BD
    conn = conectar_bd_sua(ruta_bd=ruta_bd, password=password)
    
    # Reintentar si falla
    if not conn:
        retry = input("¿Desea intentar con una contraseña diferente? (s/n): ").lower()
        if retry == 's':
            password = reintentar_conexion()
            conn = conectar_bd_sua(ruta_bd=ruta_bd, password=password)
    
    # Verificar si se pudo conectar
    if not conn:
        print("No se pudo establecer conexión. Abortando.")
        sys.exit(1)
    
    # Listar todas las tablas para verificar
    tablas = listar_tablas(conn)
    print("\nTablas disponibles en la base de datos:")
    for i, tabla in enumerate(tablas, 1):
        print(f"{i}. {tabla}")
    
    # Explorar las tablas específicas
    tablas_objetivo = ['Patron', 'Prima_RT']
    
    for tabla in tablas_objetivo:
        if tabla in tablas:
            print(f"\n\n=== Explorando tabla: {tabla} ===")
            
            # Obtener estructura
            columnas = obtener_estructura_tabla(conn, tabla)
            print(f"\nEstructura de la tabla {tabla}:")
            for i, col in enumerate(columnas, 1):
                print(f"{i}. {col}")
            
            # Obtener datos
            print(f"\nPrimeros registros de la tabla {tabla}:")
            df = obtener_datos_tabla(conn, tabla)
            if not df.empty:
                print(df)
                guardar_resultados_csv(df, tabla)
            else:
                print(f"No se pudieron obtener datos de la tabla {tabla}")
        else:
            print(f"\nLa tabla {tabla} no existe en la base de datos.")
    
    # Cerrar conexión
    conn.close()
    print("\nExploración finalizada.")

if __name__ == "__main__":
    main() 