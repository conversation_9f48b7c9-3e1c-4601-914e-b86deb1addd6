from conectores.conector_sua import ConectorSUA
import traceback

def probar_tipo_18():
    try:
        # Conectar a la base de datos
        conector = ConectorSUA(ruta_bd=r'C:\Cobranza\SUA\SUA.MDB', password="S5@N52V49")
        if not conector.conectar():
            print("❌ Error al conectar a la base de datos")
            return
        print("✅ Conexión establecida correctamente")
        
        # Datos de prueba para movimiento tipo 18
        datos_mov = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456', 
            'TIP_MOVS': '18',
            'FEC_INIC': '2025-04-20',
            'Tip_Des': 'Factor de Descuento',
            'Val_Des': 6.59
        }
        
        # Procesar el movimiento directamente
        print("Intentando procesar modificación de tipo de descuento...")
        resultado = conector.procesar_modificacion_tipo_descuento(datos_mov)
        
        if resultado:
            print("✅ Movimiento procesado correctamente")
        else:
            print("❌ Error al procesar movimiento")
        
        conector.desconectar()
    
    except Exception as e:
        print(f"Error general: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    probar_tipo_18() 