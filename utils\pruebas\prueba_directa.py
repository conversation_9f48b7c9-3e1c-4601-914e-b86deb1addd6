import pyodbc
import pandas as pd
from datetime import datetime

def main():
    # Establecer conexión
    try:
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa a la base de datos")
        
        cursor = conn.cursor()
        
        # 1. Probar la consulta SELECT
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        query_select = f"""
        SELECT Num_Cre, Val_Des, Tip_Des, SAL_IMSS 
        FROM Asegura 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        """
        
        print(f"Ejecutando SELECT: {query_select}")
        cursor.execute(query_select)
        row = cursor.fetchone()
        
        if row:
            print(f"Resultado: Num_Cre={row[0]}, Val_Des={row[1]}, Tip_Des={row[2]}, SAL_IMSS={row[3]}")
            
            # 2. Probar la consulta UPDATE
            fecha_suspensión = "2025-04-01"
            fecha_suspensión_dt = pd.to_datetime(fecha_suspensión)
            fecha_suspensión_str = fecha_suspensión_dt.strftime('%m/%d/%Y')
            
            query_update = f"""
            UPDATE Asegura 
            SET Fec_FinDsc = #{fecha_suspensión_str}# 
            WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
            """
            
            print(f"Ejecutando UPDATE: {query_update}")
            cursor.execute(query_update)
            print(f"Filas afectadas: {cursor.rowcount}")
            
            # 3. Probar la consulta INSERT
            num_cre = row[0] or ''
            val_des = float(row[1]) if row[1] else 0.0
            tip_des = row[2] or ''
            sal_imss = float(row[3]) if row[3] else 350.0
            
            fecha_str = fecha_suspensión_dt.strftime('%m/%d/%Y')
            
            query_insert = f"""
            INSERT INTO Movtos (
                REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CON_SEC, 
                NUM_DIAS, SAL_MOVT, SAL_MOVT2, SAL_MOVT3, CVE_MOVS,
                EDO_MOV, SAL_ANT1, SAL_ANT2, SAL_ANT3, ART_33,
                Num_Cre, Val_Des, Tip_Des, Tab_Dism
            ) VALUES (
                '{reg_patr}', '{num_afil}', '16', #{fecha_str}#, '',
                0, {sal_imss}, 0, 0, 'G',
                0, 0, 0, 0, 'N',
                '{num_cre}', {val_des}, '{tip_des}', 0
            )
            """
            
            print(f"Ejecutando INSERT: {query_insert}")
            cursor.execute(query_insert)
            print(f"Filas afectadas: {cursor.rowcount}")
            
            # Deshacer cambios para probar nuevamente
            conn.rollback()
            
        else:
            print(f"No se encontró el asegurado {num_afil} en el registro patronal {reg_patr}")
            
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 