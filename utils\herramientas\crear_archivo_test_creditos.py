"""
Script para generar un archivo Excel de prueba con movimientos de suspensión y reinicio de créditos.
"""

import pandas as pd
from datetime import datetime, timedelta

def main():
    # Crear el DataFrame para el Excel
    df = pd.DataFrame(columns=[
        'REG_PATR',
        'NUM_AFIL',
        'NOMBRE',
        'FECHA_MOVIMIENTO',
        'TIPO_MOVIMIENTO',
        'SALARIO',
        'TIPO_DESCUENTO',
        'VALOR_DESCUENTO',
        'NUMERO_CREDITO'
    ])
    
    # Usar fechas futuras para evitar conflictos con pruebas anteriores
    fecha_base = datetime.now()
    # Usar el formato YYYY-MM-DD explícitamente
    fecha_suspension = (fecha_base + timedelta(days=15)).strftime('%Y-%m-%d')
    fecha_reinicio = (fecha_base + timedelta(days=30)).strftime('%Y-%m-%d')
    
    # Agregar un registro de suspensión de crédito (16)
    suspension = {
        'REG_PATR': 'E5352621109',
        'NUM_AFIL': '04088625456',
        'NOMBRE': 'EMPLEADO PRUEBA',
        'FECHA_MOVIMIENTO': fecha_suspension,
        'TIPO_MOVIMIENTO': '16',
        'SALARIO': '',
        'TIPO_DESCUENTO': '',
        'VALOR_DESCUENTO': '',
        'NUMERO_CREDITO': '1565544777'
    }
    
    # Agregar un registro de reinicio de crédito (17)
    reinicio = {
        'REG_PATR': 'E5352621109',
        'NUM_AFIL': '04088625456',
        'NOMBRE': 'EMPLEADO PRUEBA',
        'FECHA_MOVIMIENTO': fecha_reinicio,
        'TIPO_MOVIMIENTO': '17',
        'SALARIO': '',
        'TIPO_DESCUENTO': '',
        'VALOR_DESCUENTO': '',
        'NUMERO_CREDITO': '1565544777'
    }
    
    # Agregar los registros al DataFrame
    df = pd.concat([df, pd.DataFrame([suspension, reinicio])], ignore_index=True)
    
    # Guardar el DataFrame como Excel
    archivo_salida = 'test_bajas_reingresos.xlsx'
    df.to_excel(archivo_salida, index=False)
    
    print(f"Archivo Excel creado: {archivo_salida}")
    print(f"Registros incluidos:")
    print(f"1. Suspensión (16): Fecha={fecha_suspension}")
    print(f"2. Reinicio (17): Fecha={fecha_reinicio}")
    
    # Verificar el contenido
    df_check = pd.read_excel(archivo_salida)
    print("\nContenido del archivo creado:")
    print(df_check)

if __name__ == "__main__":
    main() 