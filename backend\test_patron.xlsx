import pandas as pd
import openpyxl
from datetime import datetime

# Crear un DataFrame con un registro de prueba
data = {
    'Registro Patronal': ['Y6473802109'],
    'RFC': ['XAXX010101000'],
    'Razon Social': ['EMPRESA DE PRUEBA SA DE CV'],
    'Actividad Económica': ['CONSTRUCCION DE EDIFICIOS'],
    'Dom<PERSON>lio': ['CALLE PRUEBA 123, COL. CENTRO'],
    'Municipio': ['ALVARO OBREGON'],
    'Código Postal': ['01000'],
    'Entidad Federativa': ['CIUDAD DE MEXICO'],
    'Teléfono': ['5555555555'],
    'Re<PERSON>lso de Subsidios': [True],
    'Zona Salario': ['A'],
    'Subdelegacion': ['ALVARO OBREGON'],
    'Fecha Prima de RT': ['01/02/2024'],
    'Factor Prima de RT': [0.5],
    'Clase': ['III'],
    'Fracción': ['123'],
    'STyPS': ['ABC123'],
    'Representante Legal': ['JUAN PEREZ LOPEZ']
}

df = pd.DataFrame(data)

# Guardar en Excel
df.to_excel('test_patron.xlsx', sheet_name='patron', index=False) 