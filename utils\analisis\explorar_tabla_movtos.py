"""
Exploración detallada de la tabla Movtos de SUA.
Este script utiliza la clase ConectorSUA para explorar en detalle
la estructura y los datos de la tabla de movimientos.
"""

import os
import sys
import pandas as pd
import json
from pathlib import Path
import pyodbc
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

# Agregar el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar el conector
from conectores.conector_sua import ConectorSUA

# Configuración
DB_PATH = r'C:\Cobranza\SUA\SUA.MDB'
RESULTS_DIR = 'resultados'

def crear_directorio_si_no_existe(directorio):
    """Crea un directorio si no existe"""
    Path(directorio).mkdir(parents=True, exist_ok=True)

def guardar_dataframe_a_csv(df, nombre_archivo, directorio="resultados"):
    """Guarda un DataFrame en un archivo CSV"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{nombre_archivo}.csv")
    df.to_csv(ruta_completa, index=False, encoding='utf-8-sig')
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def guardar_diccionario_a_json(diccionario, nombre_archivo, directorio="resultados"):
    """Guarda un diccionario en un archivo JSON"""
    crear_directorio_si_no_existe(directorio)
    ruta_completa = os.path.join(directorio, f"{nombre_archivo}.json")
    with open(ruta_completa, 'w', encoding='utf-8') as f:
        json.dump(diccionario, f, indent=4, ensure_ascii=False)
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def obtener_datos_tabla_sql(conector, tabla, limite=50):
    """Obtiene datos directamente mediante SQL"""
    print(f"\n=== DATOS DE LA TABLA {tabla.upper()} ===")
    try:
        # Construir la consulta SQL con corchetes para evitar problemas con palabras reservadas
        query = f"SELECT TOP {limite} * FROM [{tabla}]"
        
        # Ejecutar la consulta
        cursor = conector.conn.cursor()
        cursor.execute(query)
        
        # Obtener columnas
        columnas = [column[0] for column in cursor.description]
        print(f"\nColumnas de la tabla {tabla}:")
        for i, col in enumerate(columnas, 1):
            print(f"{i}. {col}")
        
        # Obtener datos
        resultados = []
        for row in cursor.fetchall():
            resultados.append(dict(zip(columnas, row)))
        
        # Convertir a DataFrame
        df = pd.DataFrame(resultados)
        
        if df.empty:
            print(f"No se encontraron datos en la tabla {tabla}")
            return None, None
        
        # Mostrar información básica
        print(f"\nSe encontraron {len(df)} registros")
        
        # Mostrar primeros registros
        print("\nPrimeros registros:")
        print(df.head().to_string())
        
        return df, columnas
        
    except Exception as e:
        print(f"Error al obtener datos de la tabla {tabla}: {e}")
        return None, None

def analizar_tipos_movimiento(df):
    """Analiza los tipos de movimiento disponibles"""
    print("\n=== ANÁLISIS DE TIPOS DE MOVIMIENTO ===")
    
    if df is None or df.empty:
        print("No hay datos para analizar tipos de movimiento")
        return {}
    
    # Buscar columna relacionada con el tipo de movimiento
    columnas_tipo = [col for col in df.columns if "tipo" in col.lower() or "tip" in col.lower()]
    
    if not columnas_tipo:
        print("No se identificaron columnas que puedan contener el tipo de movimiento")
        
        # Buscar específicamente TIP_MOVS si existe
        if 'TIP_MOVS' in df.columns:
            columnas_tipo = ['TIP_MOVS']
            print("Se utilizará la columna TIP_MOVS para el análisis.")
    
    resultados = {}
    for col_tipo in columnas_tipo:
        valores_unicos = df[col_tipo].dropna().unique()
        
        print(f"\nValores únicos en la columna {col_tipo}:")
        tipos = []
        for i, valor in enumerate(valores_unicos, 1):
            # Contar ocurrencias
            count = df[df[col_tipo] == valor].shape[0]
            print(f"{i}. {valor} ({count} ocurrencias)")
            tipos.append({"valor": valor, "ocurrencias": count})
        
        resultados[col_tipo] = tipos
    
    return resultados

def identificar_campos_clave(columnas):
    """Identifica los campos clave basados en sus nombres"""
    print("\n=== CAMPOS CLAVE IDENTIFICADOS ===")
    
    if not columnas:
        print("No hay columnas para analizar")
        return {}
    
    # Patrones comunes para identificar campos clave
    patrones = {
        'Asegurado': ['NUM_AFIL', 'AFIL', 'NSS', 'ASEGURADO'],
        'Patrón': ['REG_PATR', 'PATRON', 'EMPRESA'],
        'Fecha': ['FEC', 'FECHA', 'DATE', 'PERIODO'],
        'Tipo de Movimiento': ['TIP_MOV', 'TIPO_MOV', 'MOV'],
        'Salario': ['SAL', 'SALARIO', 'SUELDO'],
        'Incapacidad': ['INC', 'INCAP', 'INCAPACIDAD'],
        'Estado': ['EDO', 'ESTADO', 'STATUS']
    }
    
    # Identificar campos según patrones
    campos_identificados = {}
    
    for categoria, patrones_busqueda in patrones.items():
        campos_cat = []
        for col in columnas:
            if any(patron.lower() in col.lower() for patron in patrones_busqueda):
                campos_cat.append(col)
        
        if campos_cat:
            campos_identificados[categoria] = campos_cat
    
    # Mostrar resultados
    for categoria, campos in campos_identificados.items():
        print(f"\n{categoria}:")
        for campo in campos:
            print(f"  - {campo}")
    
    return campos_identificados

def analizar_tabla_movtos(conector, limite=50):
    """Analiza la tabla Movtos y guarda sus datos y estructura"""
    tabla = "Movtos"
    print(f"\n{'='*50}")
    print(f"ANALIZANDO TABLA: {tabla}")
    print(f"{'='*50}\n")
    
    # Obtener datos directamente con SQL
    df, columnas = obtener_datos_tabla_sql(conector, tabla, limite)
    
    if df is not None and columnas:
        # Guardar datos
        ruta_csv = guardar_dataframe_a_csv(df, f"{tabla}_detallado")
        
        # Identificar campos clave
        campos_clave = identificar_campos_clave(columnas)
        
        # Analizar tipos de movimiento
        tipos_movimiento = analizar_tipos_movimiento(df)
        
        # Realizar análisis adicional si hay datos
        if not df.empty:
            analisis_adicional = {}
            
            # Analizar fechas si existen campos de fecha
            campos_fecha = [col for col in df.columns if 'FECHA' in col or 'DIA' in col or 'FEC' in col]
            if campos_fecha:
                print("\nAnálisis de fechas:")
                fechas_info = {}
                for campo in campos_fecha:
                    try:
                        # Intentar convertir a datetime, ignorando errores
                        fechas = pd.to_datetime(df[campo], errors='coerce').dropna()
                        if len(fechas) > 0:
                            min_fecha = fechas.min()
                            max_fecha = fechas.max()
                            print(f"  - {campo}: Desde {min_fecha.date()} hasta {max_fecha.date()}")
                            fechas_info[campo] = {
                                "min": min_fecha.strftime('%Y-%m-%d'),
                                "max": max_fecha.strftime('%Y-%m-%d')
                            }
                        else:
                            print(f"  - {campo}: No hay fechas válidas")
                            fechas_info[campo] = "No hay fechas válidas"
                    except Exception as e:
                        print(f"  - {campo}: Error al procesar fechas: {str(e)}")
                        fechas_info[campo] = f"Error: {str(e)}"
                analisis_adicional["fechas"] = fechas_info
        
        # Verificar campos de salario
        campos_salario = [col for col in df.columns if 'SAL' in col]
        if campos_salario:
            print("\nAnálisis de salarios:")
            salarios_info = {}
            for campo in campos_salario:
                try:
                    # Convertir explícitamente a numérico, ignorando errores
                    valores = pd.to_numeric(df[campo], errors='coerce').dropna()
                    if len(valores) > 0:
                        min_val = valores.min()
                        max_val = valores.max()
                        prom_val = valores.mean()
                        print(f"  - {campo}: Min={min_val}, Max={max_val}, Prom={prom_val:.2f}")
                        salarios_info[campo] = {"min": float(min_val), "max": float(max_val), "promedio": float(prom_val)}
                    else:
                        print(f"  - {campo}: No hay datos numéricos válidos")
                        salarios_info[campo] = "No hay datos numéricos válidos"
                except Exception as e:
                    print(f"  - {campo}: Error al procesar valores: {str(e)}")
                    salarios_info[campo] = f"Error: {str(e)}"
            analisis_adicional["salarios"] = salarios_info
        
        # Guardar análisis completo
        analisis_completo = {
            "tabla": tabla,
            "columnas": columnas,
            "campos_clave": campos_clave,
            "tipos_movimiento": tipos_movimiento,
            "analisis_adicional": analisis_adicional,
            "archivos": {
                "muestra_csv": ruta_csv
            }
        }
        ruta_analisis = guardar_diccionario_a_json(analisis_completo, f"{tabla}_analisis_completo")
        
        return analisis_completo
    else:
        print(f"No se pudieron obtener datos de la tabla {tabla}")
        return None

def obtener_datos_movtos(conn):
    """Obtiene todos los datos de la tabla Movtos"""
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM Movtos")
    columns = [column[0] for column in cursor.description]
    results = cursor.fetchall()
    return pd.DataFrame.from_records(results, columns=columns)

def analizar_creditos_infonavit(df):
    """Analiza específicamente los registros relacionados con créditos Infonavit"""
    # Tipo 15 corresponde a inicio de crédito Infonavit
    creditos = df[df['TIP_MOVS'] == '15'].copy()
    
    print(f"\n{'-'*80}")
    print(f"ANÁLISIS DE CRÉDITOS INFONAVIT (TIP_MOVS = '15')")
    print(f"{'-'*80}")
    
    if creditos.empty:
        print("No se encontraron registros de créditos Infonavit en la tabla Movtos.")
        return None
    
    print(f"Se encontraron {len(creditos)} registros de créditos Infonavit.")
    
    # Análisis de tipos de descuento
    # Verificar si Tip_Des es numérico o texto
    try:
        creditos['Tip_Des'] = creditos['Tip_Des'].fillna(0)
        if creditos['Tip_Des'].dtype == 'object':
            # Si es texto, crear un mapeo
            tipo_descuento_counts = creditos['Tip_Des'].value_counts()
        else:
            # Si es numérico, convertir a entero
            creditos['Tip_Des'] = creditos['Tip_Des'].astype(int)
            tipo_descuento_counts = creditos['Tip_Des'].value_counts()
    except Exception as e:
        print(f"Error al procesar tipos de descuento: {e}")
        tipo_descuento_counts = creditos['Tip_Des'].value_counts()
    
    print("\nDistribución de tipos de descuento:")
    for tipo, count in tipo_descuento_counts.items():
        if isinstance(tipo, int) or (isinstance(tipo, str) and tipo.isdigit()):
            # Si es numérico
            tipo_int = int(tipo) if isinstance(tipo, str) else tipo
            tipo_nombre = {
                1: "Porcentaje",
                2: "Cuota Fija",
                3: "Factor de descuento (VSM)"
            }.get(tipo_int, f"Desconocido ({tipo})")
        else:
            # Si es texto, usar directamente
            tipo_nombre = tipo if tipo else "No especificado"
        
        print(f"  - {tipo_nombre}: {count} registros ({count/len(creditos)*100:.1f}%)")
    
    # Análisis de valores de descuento
    creditos['Val_Des'] = pd.to_numeric(creditos['Val_Des'], errors='coerce')
    
    print(f"\nEstadísticas de valores de descuento:")
    print(f"  - Promedio: {creditos['Val_Des'].mean():.2f}")
    print(f"  - Mediana: {creditos['Val_Des'].median():.2f}")
    print(f"  - Mínimo: {creditos['Val_Des'].min():.2f}")
    print(f"  - Máximo: {creditos['Val_Des'].max():.2f}")
    
    # Análisis de número de crédito
    print(f"\nAnálisis de números de crédito:")
    creditos_unicos = creditos['Num_Cre'].nunique()
    print(f"  - Números de crédito únicos: {creditos_unicos}")
    
    # Verificar si hay múltiples registros para el mismo asegurado
    try:
        if 'NUM_AFIL' in creditos.columns:
            asegurados_duplicados = creditos.duplicated(subset=['NUM_AFIL'], keep=False)
            if asegurados_duplicados.any():
                print(f"\nSe encontraron {asegurados_duplicados.sum()} registros duplicados para algunos asegurados.")
                print("Asegurados con múltiples registros de crédito:")
                duplicados_por_asegurado = creditos[asegurados_duplicados].groupby('NUM_AFIL').size()
                for asegurado, count in duplicados_por_asegurado.items():
                    print(f"  - Asegurado {asegurado}: {count} registros")
            else:
                print("\nNo se encontraron asegurados con múltiples registros de crédito.")
        else:
            print("\nNo se pudo analizar duplicados: la columna 'NUM_AFIL' no existe en los datos.")
    except Exception as e:
        print(f"\nError al analizar duplicados: {e}")
    
    return creditos

def graficar_creditos_infonavit(creditos, output_dir):
    """Genera gráficos para el análisis de créditos Infonavit"""
    if creditos is None or creditos.empty:
        return
    
    # Asegurar que el directorio existe
    os.makedirs(output_dir, exist_ok=True)
    
    # Convertir columnas numéricas
    creditos['Val_Des'] = pd.to_numeric(creditos['Val_Des'], errors='coerce')
    creditos['Tip_Des'] = pd.to_numeric(creditos['Tip_Des'], errors='coerce')
    
    # 1. Distribución de tipos de descuento
    plt.figure(figsize=(10, 6))
    tipo_counts = creditos['Tip_Des'].value_counts()
    labels = [f"Tipo {i}" for i in tipo_counts.index]
    sns.barplot(x=tipo_counts.index, y=tipo_counts.values)
    plt.title('Distribución de Tipos de Descuento')
    plt.xlabel('Tipo de Descuento')
    plt.ylabel('Cantidad')
    plt.xticks(range(len(labels)), labels)
    plt.savefig(os.path.join(output_dir, 'tipos_descuento_infonavit.png'))
    plt.close()
    
    # 2. Distribución de valores de descuento
    plt.figure(figsize=(10, 6))
    sns.histplot(creditos['Val_Des'].dropna(), kde=True, bins=20)
    plt.title('Distribución de Valores de Descuento')
    plt.xlabel('Valor de Descuento')
    plt.ylabel('Frecuencia')
    plt.savefig(os.path.join(output_dir, 'valores_descuento_infonavit.png'))
    plt.close()

def main():
    """Función principal para analizar la tabla Movtos"""
    try:
        # Crear directorio de resultados si no existe
        os.makedirs(RESULTS_DIR, exist_ok=True)
        
        # Conexión a la base de datos utilizando la clase ConectorSUA con la contraseña
        print(f"Conectando a la base de datos: {DB_PATH}")
        conector = ConectorSUA(ruta_bd=DB_PATH, password="S5@N52V49")
        if not conector.conectar():
            print("No se pudo conectar a la base de datos. Verifique la ruta y los permisos.")
            return
        
        # Usar la conexión del conector
        conn = conector.conn
        
        # Obtener datos
        print("Obteniendo datos de la tabla Movtos...")
        df_movtos = obtener_datos_movtos(conn)
        print(f"Se obtuvieron {len(df_movtos)} registros de la tabla Movtos.")
        
        # Mostrar columnas
        print("\nColumnas de la tabla Movtos:")
        for i, col in enumerate(df_movtos.columns):
            print(f"  {i+1:2d}. {col}")
        
        # Mostrar primeros registros
        print("\nPrimeros registros de la tabla Movtos:")
        print(df_movtos.head().to_string())
        
        # Análisis general de tipos de movimiento
        print("\nDistribución de tipos de movimiento:")
        tipo_mov_counts = df_movtos['TIP_MOVS'].value_counts()
        for tipo, count in tipo_mov_counts.items():
            tipo_nombre = {
                '01': "Alta",
                '02': "Baja",
                '07': "Modificación de salario",
                '15': "Inicio crédito Infonavit",
                '16': "Modificación crédito Infonavit",
                '17': "Suspensión crédito Infonavit",
            }.get(tipo, f"Desconocido ({tipo})")
            
            print(f"  - Tipo {tipo} ({tipo_nombre}): {count} registros")
        
        # Análisis específico de créditos Infonavit
        creditos_df = analizar_creditos_infonavit(df_movtos)
        
        # Generar gráficos
        graficar_creditos_infonavit(creditos_df, RESULTS_DIR)
        
        # Guardar resultados a CSV
        if creditos_df is not None and not creditos_df.empty:
            output_path = os.path.join(RESULTS_DIR, 'creditos_infonavit.csv')
            creditos_df.to_csv(output_path, index=False)
            print(f"\nResultados guardados en {output_path}")
        
        print("\nAnálisis completado.")
        
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        if 'conector' in locals():
            conector.desconectar()
            print("Conexión cerrada correctamente.")

if __name__ == "__main__":
    main() 