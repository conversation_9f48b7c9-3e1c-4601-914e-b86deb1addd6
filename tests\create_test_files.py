import pandas as pd
from datetime import datetime

def create_patron_test_file():
    """Crea un archivo Excel de ejemplo para la carga masiva de patrones"""
    
    # Datos de ejemplo
    data = {
        'Registro Patronal': ['B7812345101', 'C5512345102'],
        'RFC': ['XAXX010101000', 'XEXX010101000'],
        'Nombre o Razón Social': ['CONSTRUCTORA EJEMPLO SA DE CV', 'SERVICIOS PRUEBA SA DE CV'],
        'Actividad Principal': ['CONSTRUCCIÓN DE VIVIENDAS', 'SERVICIOS DE CONSULTORÍA'],
        'Domicilio': ['CALLE EJEMPLO 123, COL CENTRO', 'AV PRUEBA 456, COL REFORMA'],
        'Ciudad o Municipio': ['ALVARO OBREGON', 'TOLUCA'],
        'CP': ['01234', '50123'],
        'Estado': ['CIUDAD DE MEXICO', 'ESTADO DE MEXICO'],
        'Subdelegación': ['ALVARO OBREGON', 'TOLUCA'],
        'Fecha Prima RT': ['01/01/2024', '01/01/2024'],
        'Prima RT': [0.5, 0.75],
        'Clase de Riesgo': ['IV', 'III'],
        'Fracción': ['420', '320'],
        'Representante Legal': ['JUAN PEREZ LOPEZ', 'MARIA LOPEZ PEREZ'],
        'Registro STyPS': ['SI', 'NO']
    }
    
    # Crear el archivo Excel con múltiples hojas
    with pd.ExcelWriter('tests/formato_carga_patrones.xlsx', engine='openpyxl') as writer:
        # Hoja de instrucciones
        instrucciones = pd.DataFrame({
            'Campo': list(data.keys()),
            'Descripción': [
                'Número de registro patronal ante el IMSS (11 dígitos)',
                'RFC del patrón con homoclave (13 caracteres)',
                'Nombre completo o razón social del patrón',
                'Giro o actividad principal del negocio',
                'Dirección completa incluyendo número y colonia',
                'Nombre del municipio o alcaldía',
                'Código postal (5 dígitos)',
                'Nombre completo del estado (ver catálogo)',
                'Nombre de la subdelegación del IMSS (ver catálogo)',
                'Fecha de la prima RT (formato DD/MM/AAAA)',
                'Valor de la prima de riesgo de trabajo (ejemplo: 0.5)',
                'Clase de riesgo (I, II, III, IV o V)',
                'Fracción de la clase de riesgo (3 dígitos)',
                'Nombre completo del representante legal',
                'Indicar SI o NO si cuenta con registro STyPS'
            ],
            'Obligatorio': [
                'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 
                'Sí', 'Sí', 'Sí', 'Sí', 'Sí', 'No'
            ],
            'Ejemplo': [
                'B7812345101',
                'XAXX010101000',
                'CONSTRUCTORA EJEMPLO SA DE CV',
                'CONSTRUCCIÓN DE VIVIENDAS',
                'CALLE EJEMPLO 123, COL CENTRO',
                'ALVARO OBREGON',
                '01234',
                'CIUDAD DE MEXICO',
                'ALVARO OBREGON',
                '01/01/2024',
                '0.5',
                'IV',
                '420',
                'JUAN PEREZ LOPEZ',
                'SI'
            ]
        })
        instrucciones.to_excel(writer, sheet_name='INSTRUCCIONES', index=False)
        
        # Hoja de datos
        df_datos = pd.DataFrame(data)
        df_datos.to_excel(writer, sheet_name='PATRONES', index=False)
        
        # Catálogo de estados
        estados = pd.DataFrame({
            'Estado': [
                'AGUASCALIENTES', 'BAJA CALIFORNIA', 'BAJA CALIFORNIA SUR',
                'CAMPECHE', 'COAHUILA', 'COLIMA', 'CHIAPAS', 'CHIHUAHUA',
                'CIUDAD DE MEXICO', 'DURANGO', 'GUANAJUATO', 'GUERRERO',
                'HIDALGO', 'JALISCO', 'ESTADO DE MEXICO', 'MICHOACAN',
                'MORELOS', 'NAYARIT', 'NUEVO LEON', 'OAXACA', 'PUEBLA',
                'QUERETARO', 'QUINTANA ROO', 'SAN LUIS POTOSI', 'SINALOA',
                'SONORA', 'TABASCO', 'TAMAULIPAS', 'TLAXCALA', 'VERACRUZ',
                'YUCATAN', 'ZACATECAS'
            ]
        })
        estados.to_excel(writer, sheet_name='CAT_ESTADOS', index=False)
        
        # Catálogo de clases de riesgo
        clases = pd.DataFrame({
            'Clase': ['I', 'II', 'III', 'IV', 'V'],
            'Descripción': [
                'Riesgo Mínimo',
                'Riesgo Bajo',
                'Riesgo Medio',
                'Riesgo Alto',
                'Riesgo Máximo'
            ],
            'Prima Media': [
                '0.54355',
                '1.13065',
                '2.59840',
                '4.65325',
                '7.58875'
            ]
        })
        clases.to_excel(writer, sheet_name='CAT_CLASES', index=False)
        
        # Hoja de validaciones comunes
        validaciones = pd.DataFrame({
            'Campo': [
                'Registro Patronal',
                'RFC',
                'CP',
                'Fecha Prima RT',
                'Prima RT',
                'Clase de Riesgo',
                'Fracción'
            ],
            'Validación': [
                'Debe tener exactamente 11 dígitos',
                'Debe tener 13 caracteres para personas morales o 12 para físicas',
                'Debe tener exactamente 5 dígitos',
                'Debe estar en formato DD/MM/AAAA',
                'Debe ser un número decimal entre 0.5 y 15',
                'Debe ser una de las clases válidas (I a V)',
                'Debe ser un número de 3 dígitos'
            ],
            'Error Común': [
                'Faltan o sobran dígitos, incluir letras',
                'RFC inválido o sin homoclave',
                'CP incompleto o con letras',
                'Fecha en formato incorrecto',
                'Prima fuera de rango o con formato incorrecto',
                'Usar números en lugar de números romanos',
                'Usar menos de 3 dígitos'
            ],
            'Solución': [
                'Verificar en su registro IMSS',
                'Verificar en su Cédula Fiscal',
                'Consultar Código Postal en Correos de México',
                'Usar el formato DD/MM/AAAA (ejemplo: 01/01/2024)',
                'Verificar en su última declaración de Prima RT',
                'Usar números romanos (I, II, III, IV, V)',
                'Completar con ceros a la izquierda si es necesario (ejemplo: 001)'
            ]
        })
        validaciones.to_excel(writer, sheet_name='VALIDACIONES', index=False)

    print("\nArchivo de ejemplo creado: tests/formato_carga_patrones.xlsx")
    print("\nEl archivo contiene las siguientes hojas:")
    print("1. INSTRUCCIONES: Descripción detallada de cada campo")
    print("2. PATRONES: Plantilla con ejemplos para llenar")
    print("3. CAT_ESTADOS: Catálogo de estados válidos")
    print("4. CAT_CLASES: Catálogo de clases de riesgo con descripciones")
    print("5. VALIDACIONES: Guía de errores comunes y soluciones")

def create_asegurado_test_file():
    data = {
        'REGISTRO_PATRONAL': ['12345678901', '12345678901'],
        'NSS': ['12345678901', '12345678902'],
        'CURP': ['XAXX010101HDFXXX01', 'XAXX010101HDFXXX02'],
        'NOMBRE': ['JUAN', 'MARIA'],
        'APELLIDO_PATERNO': ['PEREZ', 'LOPEZ'],
        'APELLIDO_MATERNO': ['GARCIA', 'MARTINEZ'],
        'FECHA_NACIMIENTO': [datetime(1990, 1, 1).date(), datetime(1991, 2, 2).date()],
        'SEXO': ['H', 'M'],
        'SALARIO_DIARIO': [500.00, 600.00],
        'FECHA_ALTA': [datetime.now().date(), datetime.now().date()],
        'TIPO_TRABAJADOR': ['01', '01'],
        'TIPO_SALARIO': ['01', '01'],
        'TIPO_JORNADA': ['01', '01'],
        'TIPO_CONTRATO': ['01', '01'],
        'SINDICALIZADO': ['S', 'N'],
        'TIPO_REGIMEN': ['02', '02'],
        'PUESTO': ['OPERADOR', 'ADMINISTRATIVO'],
        'DEPARTAMENTO': ['OPERACIONES', 'ADMINISTRACION'],
        'RIESGO_PUESTO': ['3', '2'],
        'PERIODICIDAD_PAGO': ['01', '01'],
        'BANCO': ['BBVA', 'SANTANDER'],
        'CUENTA_BANCARIA': ['1234567890', '0987654321'],
        'CLAVE_BANCO': ['012', '014']
    }
    
    df = pd.DataFrame(data)
    df.to_excel('tests/asegurados_test.xlsx', index=False)
    print("Archivo de prueba para asegurados creado: tests/asegurados_test.xlsx")

def create_movimiento_test_file():
    data = {
        'REGISTRO_PATRONAL': ['12345678901', '12345678901'],
        'NSS': ['12345678901', '12345678902'],
        'TIPO_MOVIMIENTO': ['ALTA', 'BAJA'],
        'FECHA_MOVIMIENTO': [datetime.now().date(), datetime.now().date()],
        'CAUSA_BAJA': [None, 'TERMINACION DE CONTRATO'],
        'DIAS_INCAPACIDAD': [None, 5],
        'TIPO_INCAPACIDAD': [None, 'RIESGO DE TRABAJO'],
        'SECUELA': [None, 'NO'],
        'CONTROL_INCAPACIDAD': [None, '1'],
        'FECHA_INICIO_INCAPACIDAD': [None, datetime.now().date()],
        'FECHA_FIN_INCAPACIDAD': [None, datetime.now().date()],
        'PORCENTAJE_INCAPACIDAD': [None, 50.0],
        'NUMERO_CREDITO': [None, '12345'],
        'TIPO_DESCUENTO': [None, 'PRESTAMO'],
        'VALOR_DESCUENTO': [None, 1000.00]
    }
    
    df = pd.DataFrame(data)
    df.to_excel('tests/movimientos_test.xlsx', index=False)
    print("Archivo de prueba para movimientos creado: tests/movimientos_test.xlsx")

def create_obra_test_file():
    data = {
        'REGISTRO_PATRONAL': ['12345678901', '12345678901'],
        'NUMERO_OBRA': ['OBRA001', 'OBRA002'],
        'NOMBRE_OBRA': ['CONSTRUCCION CASA 1', 'CONSTRUCCION CASA 2'],
        'DIRECCION': ['CALLE OBRA 1', 'CALLE OBRA 2'],
        'ENTIDAD_FEDERATIVA': ['CIUDAD DE MEXICO', 'ESTADO DE MEXICO'],
        'MUNICIPIO': ['ALVARO OBREGON', 'TOLUCA'],
        'CODIGO_POSTAL': ['01234', '56789'],
        'FECHA_INICIO': [datetime.now().date(), datetime.now().date()],
        'FECHA_TERMINO': [datetime.now().date(), datetime.now().date()],
        'MONTO_CONTRATO': [1000000.00, 2000000.00],
        'TIPO_OBRA': ['RESIDENCIAL', 'COMERCIAL'],
        'RIESGO_OBRA': ['3', '3'],
        'NUMERO_TRABAJADORES': [10, 20],
        'DESCRIPCION_OBRA': ['CONSTRUCCION DE CASA HABITACION', 'CONSTRUCCION DE LOCAL COMERCIAL']
    }
    
    df = pd.DataFrame(data)
    df.to_excel('tests/obras_test.xlsx', index=False)
    print("Archivo de prueba para obras creado: tests/obras_test.xlsx")

if __name__ == "__main__":
    create_patron_test_file()
    create_asegurado_test_file()
    create_movimiento_test_file()
    create_obra_test_file() 