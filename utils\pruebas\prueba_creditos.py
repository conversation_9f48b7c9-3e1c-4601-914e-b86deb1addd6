"""
Script para probar el procesamiento de suspensión y reinicio de créditos
"""

from conectores.conector_sua import ConectorSUA
import traceback
import pandas as pd

def main():
    # Crear una instancia del conector
    conector = ConectorSUA(password="S5@N52V49")
    
    # Conectar a la base de datos
    if not conector.conectar():
        print("Error al conectar a la base de datos SUA")
        return
    
    # Probar suspensión de crédito
    print("\nProbando suspensión de crédito...")
    try:
        datos_suspension = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'FEC_INIC': '2025-04-01',
            'TIP_MOVS': '16'  # Suspensión de crédito
        }
        
        resultado = conector.procesar_movimiento(datos_suspension)
        if resultado:
            print("Suspensión procesada correctamente")
        else:
            print("Error al procesar suspensión")
    
    except Exception as e:
        print(f"Error al procesar suspensión: {str(e)}")
        traceback.print_exc()
    
    # Probar reinicio de crédito
    print("\nProbando reinicio de crédito...")
    try:
        datos_reinicio = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'FEC_INIC': '2025-05-01',
            'TIP_MOVS': '17'  # Reinicio de crédito
        }
        
        resultado = conector.procesar_movimiento(datos_reinicio)
        if resultado:
            print("Reinicio procesado correctamente")
        else:
            print("Error al procesar reinicio")
    
    except Exception as e:
        print(f"Error al procesar reinicio: {str(e)}")
        traceback.print_exc()
    
    # Verificar cómo se almacenó el tipo de descuento
    print("\nVerificando cómo se almacenaron los movimientos en la tabla Movtos...")
    try:
        cursor = conector.conn.cursor()
        
        # Consultar los últimos 2 movimientos para este asegurado
        query = """
        SELECT TOP 2 REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, Tip_Des, Val_Des, Num_Cre
        FROM Movtos 
        WHERE REG_PATR = 'E5352621109' AND NUM_AFIL = '04088625456' 
        AND TIP_MOVS IN ('16', '17')
        ORDER BY FEC_INIC DESC
        """
        cursor.execute(query)
        movimientos = cursor.fetchall()
        
        # Mostrar resultados
        print("\nResultados de la consulta:")
        columnas = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(movimientos, columns=columnas)
        print(df.to_string())
        
        # Mostrar el tipo de dato de Tip_Des para cada movimiento
        for i, mov in enumerate(movimientos):
            tip_des = mov[columnas.index('Tip_Des')]
            tipo_mov = mov[columnas.index('TIP_MOVS')]
            print(f"\nMovimiento {i+1} (Tipo {tipo_mov}):")
            print(f"Tip_Des: {tip_des} (Tipo: {type(tip_des).__name__})")
    
    except Exception as e:
        print(f"Error al verificar movimientos: {str(e)}")
        traceback.print_exc()
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 