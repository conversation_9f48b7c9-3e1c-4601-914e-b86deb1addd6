"""
Herramienta de carga masiva de registros patronales desde Excel.
Esta aplicación permite al usuario seleccionar un archivo Excel con los datos
de los patrones a cargar y los procesa insertándolos en la base de datos SUA.
"""

import os
import sys
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.scrolledtext import ScrolledText
import threading
import datetime
from pathlib import Path
from calendar import month_name

# Agregar directorio raíz al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar conector y modelos
from conectores.conector_sua import ConectorSUA

# Mapeo de nombres de meses en español
MESES = {
    1: "Enero", 2: "Febrero", 3: "Mar<PERSON>", 4: "Abril", 
    5: "Mayo", 6: "Junio", 7: "<PERSON>", 8: "Agosto",
    9: "Septiembre", 10: "Octubre", 11: "Noviembre", 12: "Diciembre"
}

class CargaMasivaApp:
    def __init__(self, root):
        """Inicializa la aplicación de carga masiva"""
        self.root = root
        self.root.title("Carga Masiva de Registros Patronales - SUA Tool")
        self.root.geometry("900x600")
        self.root.minsize(800, 500)
        
        # Variables
        self.archivo_excel = tk.StringVar()
        self.ruta_bd = tk.StringVar(value=r'C:\Cobranza\SUA\SUA.MDB')
        self.conector = None
        self.procesando = False
        
        # Configurar estilo
        self.configurar_estilo()
        
        # Crear interfaz
        self.crear_interfaz()
        
        # Centro de mensajes
        self.log("Bienvenido a la herramienta de carga masiva de registros patronales.")
        self.log("Por favor, seleccione un archivo Excel y configure los parámetros.")
    
    def configurar_estilo(self):
        """Configura el estilo de la aplicación"""
        self.style = ttk.Style()
        
        # Configurar estilo para botones y otros widgets
        self.style.configure("TButton", padding=6, relief="flat", background="#2196F3")
        self.style.map("TButton", 
                       foreground=[('active', 'white'), ('!disabled', 'black')],
                       background=[('active', '#1976D2'), ('!disabled', '#2196F3')])
        
        self.style.configure("Header.TLabel", font=("Segoe UI", 12, "bold"))
        self.style.configure("TFrame", background="#f5f5f5")
        self.style.configure("TLabel", background="#f5f5f5")
    
    def crear_interfaz(self):
        """Crea la interfaz gráfica"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # Sección de configuración
        ttk.Label(main_frame, text="Configuración", style="Header.TLabel").pack(pady=(0, 10), anchor="w")
        
        config_frame = ttk.Frame(main_frame)
        config_frame.pack(fill="x", pady=(0, 10))
        
        # Ruta de la base de datos
        ttk.Label(config_frame, text="Base de datos SUA:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(config_frame, textvariable=self.ruta_bd, width=60).grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", command=self.seleccionar_bd).grid(row=0, column=2, padx=5, pady=5)
        
        # Archivo Excel
        ttk.Label(config_frame, text="Archivo Excel:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(config_frame, textvariable=self.archivo_excel, width=60).grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", command=self.seleccionar_excel).grid(row=1, column=2, padx=5, pady=5)
        
        config_frame.columnconfigure(1, weight=1)
        
        # Sección de acciones
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x", pady=10)
        
        self.btn_probar = ttk.Button(action_frame, text="Probar Conexión", command=self.probar_conexion)
        self.btn_probar.pack(side="left", padx=5)
        
        self.btn_validar = ttk.Button(action_frame, text="Validar Excel", command=self.validar_excel)
        self.btn_validar.pack(side="left", padx=5)
        
        self.btn_procesar = ttk.Button(action_frame, text="Procesar Registros", command=self.iniciar_procesamiento)
        self.btn_procesar.pack(side="left", padx=5)
        
        self.btn_limpiar = ttk.Button(action_frame, text="Limpiar Log", command=self.limpiar_log)
        self.btn_limpiar.pack(side="right", padx=5)
        
        # Barra de progreso
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress.pack(fill="x", pady=10)
        
        # Área de logs
        ttk.Label(main_frame, text="Registro de actividad", style="Header.TLabel").pack(pady=(10, 5), anchor="w")
        self.log_area = ScrolledText(main_frame, height=15, wrap=tk.WORD)
        self.log_area.pack(fill="both", expand=True)
        self.log_area.config(state=tk.DISABLED)
        
        # Barra de estado
        self.status_var = tk.StringVar(value="Listo")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(side="bottom", fill="x")
    
    def seleccionar_bd(self):
        """Abre un diálogo para seleccionar la base de datos SUA"""
        ruta = filedialog.askopenfilename(
            title="Seleccionar base de datos SUA",
            filetypes=[("Archivos Access", "*.mdb"), ("Todos los archivos", "*.*")]
        )
        if ruta:
            self.ruta_bd.set(ruta)
            self.log(f"Base de datos seleccionada: {ruta}")
    
    def seleccionar_excel(self):
        """Abre un diálogo para seleccionar el archivo Excel"""
        ruta = filedialog.askopenfilename(
            title="Seleccionar archivo Excel",
            filetypes=[("Archivos Excel", "*.xlsx *.xls"), ("Todos los archivos", "*.*")]
        )
        if ruta:
            self.archivo_excel.set(ruta)
            self.log(f"Archivo Excel seleccionado: {ruta}")
    
    def probar_conexion(self):
        """Prueba la conexión a la base de datos SUA"""
        self.status_var.set("Probando conexión...")
        self.btn_probar.config(state=tk.DISABLED)
        
        def _probar():
            try:
                ruta_bd = self.ruta_bd.get()
                
                if not os.path.exists(ruta_bd):
                    self.log("Error: La ruta de la base de datos no existe.", error=True)
                    return
                
                self.conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
                
                if self.conector.conectar():
                    self.log("Conexión exitosa a la base de datos SUA.", success=True)
                    # Verificar tablas necesarias
                    tablas = ["Patron", "Prima_RT", "Estados", "Subdelega"]
                    todas_existen = True
                    for tabla in tablas:
                        if not self.conector.existe_tabla(tabla):
                            self.log(f"Advertencia: No se encontró la tabla {tabla}.", warning=True)
                            todas_existen = False
                    
                    if todas_existen:
                        self.log("Se encontraron todas las tablas necesarias.", success=True)
                    
                    self.conector.desconectar()
                else:
                    self.log("Error: No se pudo conectar a la base de datos.", error=True)
            
            except Exception as e:
                self.log(f"Error al probar la conexión: {str(e)}", error=True)
            
            finally:
                self.btn_probar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_probar)
        thread.daemon = True
        thread.start()
    
    def validar_excel(self):
        """Valida el archivo Excel seleccionado"""
        self.status_var.set("Validando Excel...")
        self.btn_validar.config(state=tk.DISABLED)
        
        def _validar():
            try:
                ruta_excel = self.archivo_excel.get()
                
                if not os.path.exists(ruta_excel):
                    self.log("Error: La ruta del archivo Excel no existe.", error=True)
                    return
                
                # Verificar si el Excel tiene la hoja 'patron'
                try:
                    xlsx = pd.ExcelFile(ruta_excel)
                    
                    if 'patron' not in xlsx.sheet_names:
                        self.log(f"Error: El archivo Excel no contiene una hoja llamada 'patron'.", error=True)
                        self.log(f"Hojas disponibles: {', '.join(xlsx.sheet_names)}")
                        return
                    
                    df = pd.read_excel(ruta_excel, sheet_name='patron')
                    self.log(f"Excel válido. Se encontraron {len(df)} registros en la hoja 'patron'.", success=True)
                    
                    # Verificar columnas requeridas
                    columnas_requeridas = [
                        "Registro Patronal", "RFC", "Razon Social", "Actividad Económica",
                        "Domicilio", "Municipio", "Código Postal", "Entidad Federativa",
                        "Teléfono", "Rembolso de Subsidios", "Zona Salario", "Subdelegacion",
                        "Fecha Prima de RT", "Representante Legal", "Clase", "Fracción", "STyPS"
                    ]
                    
                    columnas_faltantes = [col for col in columnas_requeridas if col not in df.columns]
                    
                    if columnas_faltantes:
                        self.log(f"Advertencia: Faltan las siguientes columnas en el Excel:", warning=True)
                        for col in columnas_faltantes:
                            self.log(f"  - {col}", warning=True)
                        return
                    
                    self.log("Estructura del Excel correcta. Todas las columnas requeridas están presentes.", success=True)
                    
                    # Mostrar primeros 5 registros como vista previa
                    self.log("\nVista previa de los primeros registros:")
                    vista_previa = df.head(5)
                    for i, row in vista_previa.iterrows():
                        self.log(f"Registro {i+1}: {row['Registro Patronal']} - {row['Razon Social']}")
                
                except Exception as e:
                    self.log(f"Error al leer el archivo Excel: {str(e)}", error=True)
            
            except Exception as e:
                self.log(f"Error al validar el Excel: {str(e)}", error=True)
            
            finally:
                self.btn_validar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_validar)
        thread.daemon = True
        thread.start()
    
    def iniciar_procesamiento(self):
        """Inicia el procesamiento de los registros"""
        if self.procesando:
            return
        
        self.procesando = True
        self.status_var.set("Procesando registros...")
        self.btn_procesar.config(state=tk.DISABLED)
        self.progress_var.set(0)
        
        # Desactivar otros botones durante el procesamiento
        self.btn_probar.config(state=tk.DISABLED)
        self.btn_validar.config(state=tk.DISABLED)
        
        def _procesar():
            try:
                ruta_bd = self.ruta_bd.get()
                ruta_excel = self.archivo_excel.get()
                
                if not os.path.exists(ruta_bd):
                    self.log("Error: La ruta de la base de datos no existe.", error=True)
                    return
                
                if not os.path.exists(ruta_excel):
                    self.log("Error: La ruta del archivo Excel no existe.", error=True)
                    return
                
                # Conectar a la base de datos
                self.log("Conectando a la base de datos...")
                self.conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
                
                if not self.conector.conectar():
                    self.log("Error: No se pudo conectar a la base de datos.", error=True)
                    return
                
                # Leer el Excel
                self.log("Leyendo datos del archivo Excel...")
                df = pd.read_excel(ruta_excel, sheet_name='patron')
                self.log(f"Se encontraron {len(df)} registros para procesar.")
                
                # Iniciar procesamiento
                self.log("\n=== INICIANDO PROCESAMIENTO DE REGISTROS ===\n")
                
                # Procesar registros
                registros_procesados = 0
                errores = []
                total_registros = len(df)
                
                for idx, row in df.iterrows():
                    # Actualizar progreso
                    progress = (idx + 1) / total_registros * 100
                    self.update_progress(progress)
                    self.status_var.set(f"Procesando registro {idx+1} de {total_registros}...")
                    
                    try:
                        # Verificar si el registro ya existe
                        reg_pat = str(row.get("Registro Patronal", "")).strip()
                        if not reg_pat:
                            errores.append(f"Fila {idx+2}: El Registro Patronal es obligatorio")
                            continue
                        
                        # Insertar patrón directamente
                        if self.insertar_patron_directo(row):
                            self.log(f"✓ Registro {idx+1}: Patrón {reg_pat} creado con éxito", success=True)
                            
                            # Insertar Prima RT si hay datos
                            if pd.notna(row.get("Factor Prima de RT")):
                                if self.insertar_prima_rt_directo(row):
                                    fecha_prima = row.get("Fecha Prima de RT")
                                    if pd.notna(fecha_prima):
                                        if isinstance(fecha_prima, str):
                                            fecha_prima = pd.to_datetime(fecha_prima)
                                        año = fecha_prima.year
                                        mes_num = fecha_prima.month
                                        mes_txt = MESES[mes_num]
                                        self.log(f"  ✓ Prima RT para {reg_pat} ({año}-{mes_txt}) creada con éxito")
                            
                            registros_procesados += 1
                        else:
                            # El método insertar_patron_directo ya registró el error
                            pass
                    
                    except Exception as e:
                        errores.append(f"Error en fila {idx+2}: {str(e)}")
                        self.log(f"✗ Error en registro {idx+1}: {str(e)}", error=True)
                
                # Resultados finales
                self.update_progress(100)
                self.log("\n=== RESUMEN DEL PROCESO ===")
                self.log(f"- Registros procesados con éxito: {registros_procesados}")
                self.log(f"- Errores encontrados: {len(errores)}")
                
                if errores:
                    self.log("\nDetalle de errores:", warning=True)
                    for error in errores:
                        self.log(f"  • {error}", warning=True)
                    
                    # Guardar errores en un archivo
                    directorio_salida = Path("resultados")
                    directorio_salida.mkdir(exist_ok=True)
                    
                    nombre_archivo = f"errores_carga_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    ruta_archivo = directorio_salida / nombre_archivo
                    
                    with open(ruta_archivo, 'w', encoding='utf-8') as f:
                        f.write("ERRORES EN CARGA MASIVA DE PATRONES\n")
                        f.write(f"Fecha: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Archivo: {ruta_excel}\n\n")
                        for error in errores:
                            f.write(f"{error}\n")
                    
                    self.log(f"\nSe ha guardado un reporte de errores en: {ruta_archivo}")
                
                if registros_procesados > 0:
                    self.log("\n¡Proceso completado con éxito!", success=True)
                else:
                    self.log("\nProceso completado sin registros insertados.", warning=True)
            
            except Exception as e:
                self.log(f"Error durante el procesamiento: {str(e)}", error=True)
            
            finally:
                # Cerrar conexión
                if self.conector and self.conector.conn:
                    self.conector.desconectar()
                    self.log("Conexión a la base de datos cerrada.")
                
                # Habilitar botones
                self.btn_procesar.config(state=tk.NORMAL)
                self.btn_probar.config(state=tk.NORMAL)
                self.btn_validar.config(state=tk.NORMAL)
                
                self.status_var.set("Listo")
                self.procesando = False
        
        thread = threading.Thread(target=_procesar)
        thread.daemon = True
        thread.start()
    
    def buscar_id_entidad(self, nombre_entidad):
        """Busca el ID de una entidad federativa por su nombre"""
        if not nombre_entidad or pd.isna(nombre_entidad):
            return None
        
        try:
            # Escapar posibles comillas en el nombre
            nombre_escaped = nombre_entidad.replace("'", "''")
            
            query = f"""
            SELECT IdClave, Descripcion, Posicion 
            FROM Estados 
            WHERE Descripcion LIKE '%{nombre_escaped}%'
            """
            
            resultado = self.conector.ejecutar_consulta(query)
            
            if resultado and len(resultado) > 0:
                return {
                    'id': resultado[0]['IdClave'],
                    'nombre': resultado[0]['Descripcion'],
                    'posicion': resultado[0]['Posicion']
                }
        except Exception as e:
            self.log(f"Error al buscar entidad federativa: {e}", error=True)
        
        self.log(f"Advertencia: No se encontró la entidad federativa '{nombre_entidad}'. Usando valor por defecto.", warning=True)
        # Valor por defecto para continuar el proceso
        return {
            'id': 20,  # ID por defecto (Jalisco generalmente)
            'nombre': nombre_entidad,
            'posicion': 1
        }
    
    def buscar_id_subdelegacion(self, nombre_subdelegacion):
        """Busca el ID de una subdelegación por su nombre"""
        if not nombre_subdelegacion or pd.isna(nombre_subdelegacion):
            return None
        
        try:
            # Escapar posibles comillas en el nombre
            nombre_escaped = nombre_subdelegacion.replace("'", "''")
            
            query = f"""
            SELECT IdClaveSub, Descripcion, Posicion 
            FROM Subdelega 
            WHERE Descripcion LIKE '%{nombre_escaped}%'
            """
            
            resultado = self.conector.ejecutar_consulta(query)
            
            if resultado and len(resultado) > 0:
                return {
                    'id': resultado[0]['IdClaveSub'],
                    'nombre': resultado[0]['Descripcion'],
                    'posicion': resultado[0]['Posicion']
                }
        except Exception as e:
            self.log(f"Error al buscar subdelegación: {e}", error=True)
        
        self.log(f"Advertencia: No se encontró la subdelegación '{nombre_subdelegacion}'. Usando valor por defecto.", warning=True)
        # Valores por defecto para continuar el proceso
        return {
            'id': 2153,  # ID por defecto
            'nombre': nombre_subdelegacion,
            'posicion': 3
        }
    
    def buscar_fraccion(self, fraccion_valor):
        """
        Formatea correctamente la fracción con tres dígitos para mantener los ceros a la izquierda.
        
        Esta función toma un valor de fracción (número o texto) y garantiza que sea una cadena
        de 3 dígitos con ceros a la izquierda. Esto es necesario porque el campo Fraccion 
        en la tabla Patron es de tipo texto y queremos mantener un formato consistente.
        
        Ejemplos:
        - "43" se convierte en "043"
        - "5" se convierte en "005"
        - "123" se mantiene como "123"
        """
        self.log("### UTILIZANDO VERSIÓN ACTUALIZADA DE BUSCAR_FRACCION ###", success=True)
        
        if not fraccion_valor or pd.isna(fraccion_valor):
            # Valor por defecto si no hay dato
            self.log("Fracción no especificada, usando valor por defecto '000'", warning=True)
            return "000"
        
        # Convertir a string para comparación (sin ceros a la izquierda)
        if isinstance(fraccion_valor, (int, float)):
            valor_comparacion = str(int(fraccion_valor))
        else:
            valor_comparacion = str(fraccion_valor).strip()
        
        # Formatear directamente sin buscar en la tabla
        formatted_value = valor_comparacion.zfill(3)
        self.log(f"Formateando fracción '{valor_comparacion}' a '{formatted_value}'", success=True)
        return formatted_value
    
    def log(self, mensaje, error=False, warning=False, success=False):
        """Agrega un mensaje al área de logs"""
        self.log_area.config(state=tk.NORMAL)
        
        # Agregar timestamp
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.insert(tk.END, f"[{timestamp}] ")
        
        # Formatear mensaje según tipo
        tag = None
        if error:
            tag = "error"
            self.log_area.tag_configure(tag, foreground="red")
        elif warning:
            tag = "warning"
            self.log_area.tag_configure(tag, foreground="orange")
        elif success:
            tag = "success"
            self.log_area.tag_configure(tag, foreground="green")
        
        # Insertar mensaje
        start_index = self.log_area.index(tk.END+"-1c")
        self.log_area.insert(tk.END, f"{mensaje}\n")
        
        # Aplicar tag si corresponde
        if tag:
            end_index = self.log_area.index(tk.END+"-1c")
            self.log_area.tag_add(tag, start_index, end_index)
        
        # Auto-scroll
        self.log_area.see(tk.END)
        self.log_area.config(state=tk.DISABLED)
    
    def limpiar_log(self):
        """Limpia el área de logs"""
        self.log_area.config(state=tk.NORMAL)
        self.log_area.delete(1.0, tk.END)
        self.log_area.config(state=tk.DISABLED)
        self.log("Log limpiado.")
    
    def update_progress(self, value):
        """Actualiza el valor de la barra de progreso"""
        self.progress_var.set(value)
        self.root.update_idletasks()

    def insertar_patron_directo(self, datos):
        """Inserta un patrón directamente en la base de datos"""
        try:
            # Obtener el registro patronal
            reg_pat = str(datos.get("Registro Patronal", "")).strip()
            if not reg_pat:
                self.log("Error: El Registro Patronal es obligatorio", error=True)
                return False
                
            # Verificar si ya existe el registro patronal
            cursor = self.conector.conn.cursor()
            # Consulta directa evitando parámetros
            consulta_verificacion = f"SELECT COUNT(*) FROM Patron WHERE REG_PAT = '{reg_pat}'"
            cursor.execute(consulta_verificacion)
            count = cursor.fetchone()[0]
            
            if count > 0:
                self.log(f"El registro patronal {reg_pat} ya existe en la base de datos", warning=True)
                return False
            
            # Convertir los datos booleanos a enteros (0/1) para Access
            if 'Rembolso de Subsidios' in datos:
                rembolso = 1 if datos.get('Rembolso de Subsidios') else 0
            else:
                rembolso = 0
            
            # Escapar comillas simples en los datos
            nombre = str(datos.get('Razon Social', '')).replace("'", "''")
            actividad = str(datos.get('Actividad Económica', '')).replace("'", "''")
            domicilio = str(datos.get('Domicilio', '')).replace("'", "''")
            municipio = str(datos.get('Municipio', '')).replace("'", "''")
            cp = str(datos.get('Código Postal', '')).replace("'", "''")
            telefono = str(datos.get('Teléfono', '')).replace("'", "''")
            zona = str(datos.get('Zona Salario', '')).replace("'", "''")
            entidad_nombre = str(datos.get('Entidad Federativa', '')).replace("'", "''")
            subdelegacion_nombre = str(datos.get('Subdelegacion', '')).replace("'", "''")
            rep_legal = str(datos.get('Representante Legal', '')).replace("'", "''")
            clase = str(datos.get('Clase', '')).replace("'", "''")
            
            # Buscar la fracción en la tabla TablaFraccion
            fraccion_valor = datos.get('Fracción', '')
            fraccion = self.buscar_fraccion(fraccion_valor)
            
            # Escapar comillas si las hay
            fraccion = fraccion.replace("'", "''")
            
            # Mostrar el valor exacto que se enviará a la base de datos (para depuración)
            self.log(f"DEBUG - Valor de fracción a insertar: '{fraccion}' (tipo: {type(fraccion).__name__})", warning=True)
            
            styps = str(datos.get('STyPS', '')).replace("'", "''")
            rfc = str(datos.get('RFC', '')).replace("'", "''")
            
            # Buscar ID de entidad federativa
            # ENT_PAT debe ser el IdClave de la tabla Estados SUA, relacionando la Entidad del Excel con la Descripcion de la tabla Estados
            entidad_data = self.buscar_id_entidad(entidad_nombre)
            entidad_id = entidad_data['id']
            
            # Buscar ID de subdelegación
            # DEL_PAT debe ser el IdClaveSub de la tabla Subdelega SUA, relacionando la Subdelegacion del Excel con la Descripcion de la tabla Subdelega
            subdelegacion_data = self.buscar_id_subdelegacion(subdelegacion_nombre)
            subdelegacion_id = subdelegacion_data['id']
            # NUM_SUB debe ser la posición numérica de la subdelegación en la tabla Subdelega
            subdelegacion_posicion = subdelegacion_data['posicion']
            
            # Formatear fecha para INI_AFIL (AAAAMM)
            fecha_afil = ""
            fecha_prima = datos.get('Fecha Prima de RT')
            if pd.notna(fecha_prima):
                if isinstance(fecha_prima, str):
                    fecha_prima = pd.to_datetime(fecha_prima)
                # Formato AAAAMM para INI_AFIL
                fecha_afil = fecha_prima.strftime("%Y%m")
            
            # Preparar la consulta SQL con valores directos
            # IMPORTANTE: El campo Fraccion en la tabla Patron es de tipo texto.
            # Para mantener los ceros a la izquierda (ej: "043" en lugar de "43"),
            # envolvemos la fracción en comillas simples para asegurar que se trate como texto
            fraccion_sql = f"'{fraccion}'"  # Forzar a que sea una cadena con comillas
            
            consulta_insercion = f"""
            INSERT INTO Patron (
                REG_PAT, RFC_PAT, NOM_PAT, ACT_PAT, DOM_PAT, MUN_PAT, CPP_PAT, ENT_PAT, 
                TEL_PAT, REM_PAT, ZON_PAT, DEL_PAT, CAR_ENT, NUM_DEL, CAR_DEL, NUM_SUB, 
                CAR_SUB, TIP_CON, CON_VEN, INI_AFIL, Pat_Rep, Clase, Fraccion, STyPS
            ) VALUES (
                '{reg_pat}', '{rfc}', '{nombre}', '{actividad}', '{domicilio}', '{municipio}', 
                '{cp}', {entidad_id}, '{telefono}', {rembolso}, '{zona}', {subdelegacion_id}, 
                '{entidad_nombre}', 0, '{entidad_nombre}', {subdelegacion_posicion}, 
                '{subdelegacion_nombre}', 0, NULL, '{fecha_afil}', 
                '{rep_legal}', '{clase}', {fraccion_sql}, '{styps}'
            )
            """
            self.log(f"Ejecutando inserción del patrón {reg_pat}...", success=True)
            self.log(f"Consulta SQL: {consulta_insercion}", warning=True)
            cursor.execute(consulta_insercion)
            self.conector.conn.commit()
            # Incrementar contador de patrones insertados si existe el diccionario de estadísticas
            if hasattr(self, 'estadisticas') and 'patrones_insertados' in self.estadisticas:
                self.estadisticas['patrones_insertados'] += 1
            return True
        except Exception as e:
            self.log(f"Error al insertar patrón: {e}", error=True)
            # Incrementar contador de errores en patrones si existe el diccionario de estadísticas
            if hasattr(self, 'estadisticas') and 'errores_patrones' in self.estadisticas:
                self.estadisticas['errores_patrones'] += 1
            return False

    def insertar_prima_rt_directo(self, datos, reg_pat=None):
        """Inserta una prima RT directamente en la base de datos"""
        try:
            # Si no se proporcionó el registro patronal como parámetro, intenta obtenerlo de los datos
            if not reg_pat:
                reg_pat = str(datos.get("Registro Patronal", "")).strip()
                if not reg_pat:
                    self.log("Error: El Registro Patronal es obligatorio para insertar prima RT", error=True)
                    # Incrementar contador de errores en primas RT si existe el diccionario de estadísticas
                    if hasattr(self, 'estadisticas') and 'errores_primas_rt' in self.estadisticas:
                        self.estadisticas['errores_primas_rt'] += 1
                    return False
            
            # Procesar la fecha
            fecha_prima = datos.get('Fecha Prima de RT')
            if pd.notna(fecha_prima):
                if isinstance(fecha_prima, str):
                    try:
                        fecha_prima = pd.to_datetime(fecha_prima)
                    except:
                        self.log(f"Error: Formato de fecha incorrecto para el registro patronal {reg_pat}", error=True)
                        # Incrementar contador de errores en primas RT si existe el diccionario de estadísticas
                        if hasattr(self, 'estadisticas') and 'errores_primas_rt' in self.estadisticas:
                            self.estadisticas['errores_primas_rt'] += 1
                        return False
                
                año = int(fecha_prima.year)
                mes_num = int(fecha_prima.month)
                mes_txt = MESES[mes_num]
            else:
                self.log("Error: La Fecha Prima de RT es obligatoria", error=True)
                # Incrementar contador de errores en primas RT si existe el diccionario de estadísticas
                if hasattr(self, 'estadisticas') and 'errores_primas_rt' in self.estadisticas:
                    self.estadisticas['errores_primas_rt'] += 1
                return False
            
            # Valor de la prima (debe ser un float)
            try:
                prima_rt = float(datos.get('Factor Prima de RT', 0))
            except:
                prima_rt = 0.5
                
            # Verificar si ya existe una prima para este patrón, año y mes
            cursor = self.conector.conn.cursor()
            consulta_verificacion = f"SELECT COUNT(*) FROM Prima_RT WHERE Reg_Pat = '{reg_pat}'"
            cursor.execute(consulta_verificacion)
            count = cursor.fetchone()[0]
            
            if count > 0:
                # Ya existe una prima RT con ese registro patronal, verificar año y mes
                consulta_check = f"SELECT * FROM Prima_RT WHERE Reg_Pat = '{reg_pat}'"
                cursor.execute(consulta_check)
                registros = cursor.fetchall()
                
                for registro in registros:
                    if str(registro.Ano) == str(año) and registro.Mes == mes_txt:
                        self.log(f"Ya existe una prima RT para {reg_pat} ({año}-{mes_txt})", warning=True)
                        return False
            
            # Intentar inserción directa con SQL
            try:
                # Formatear los valores para Access
                consulta = f"INSERT INTO Prima_RT (Reg_Pat, Ano, Mes, Prima_Rt, ValMes) " + \
                         f"VALUES ('{reg_pat}', {año}, '{mes_txt}', {prima_rt}, {mes_num})"
                
                cursor.execute(consulta)
                self.conector.conn.commit()
                self.log(f"Prima RT insertada para patrón {reg_pat}", success=True)
                # Incrementar contador de primas RT insertadas si existe el diccionario de estadísticas
                if hasattr(self, 'estadisticas') and 'primas_rt_insertadas' in self.estadisticas:
                    self.estadisticas['primas_rt_insertadas'] += 1
                return True
            except Exception as e:
                self.log(f"Intentando método alternativo de inserción", warning=True)
                
                # Intentar con otra aproximación
                try:
                    # Última alternativa: insertar sin la columna Mes
                    consulta_final = f"""
                    INSERT INTO Prima_RT 
                    (Reg_Pat, Ano, Prima_Rt, ValMes) 
                    VALUES 
                    ('{reg_pat}', {año}, {prima_rt}, {mes_num})
                    """
                    cursor.execute(consulta_final)
                    self.conector.conn.commit()
                    
                    # Intentar actualizar el campo Mes después
                    try:
                        update_mes = f"""
                        UPDATE Prima_RT 
                        SET Mes = '{mes_txt}' 
                        WHERE Reg_Pat = '{reg_pat}' AND Ano = {año} AND ValMes = {mes_num}
                        """
                        cursor.execute(update_mes)
                        self.conector.conn.commit()
                    except Exception as e_update:
                        self.log("No se pudo actualizar el campo Mes, pero la prima fue creada", warning=True)
                    
                    self.log(f"Prima RT insertada para patrón {reg_pat} (método alternativo)", success=True)
                    # Incrementar contador de primas RT insertadas si existe el diccionario de estadísticas
                    if hasattr(self, 'estadisticas') and 'primas_rt_insertadas' in self.estadisticas:
                        self.estadisticas['primas_rt_insertadas'] += 1
                    return True
                except Exception as e3:
                    self.log(f"Error al insertar prima RT: {e3}", error=True)
                    # Incrementar contador de errores en primas RT si existe el diccionario de estadísticas
                    if hasattr(self, 'estadisticas') and 'errores_primas_rt' in self.estadisticas:
                        self.estadisticas['errores_primas_rt'] += 1
                    return False
        except Exception as e:
            self.log(f"Error al insertar prima RT: {e}", error=True)
            # Incrementar contador de errores en primas RT si existe el diccionario de estadísticas
            if hasattr(self, 'estadisticas') and 'errores_primas_rt' in self.estadisticas:
                self.estadisticas['errores_primas_rt'] += 1
            return False

def main():
    root = tk.Tk()
    app = CargaMasivaApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 