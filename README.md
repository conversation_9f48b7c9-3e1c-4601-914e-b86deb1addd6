# ToolSUA-V4

Sistema de gestión para asegurados y pólizas.

## Requisitos

- Python 3.8+
- SQL Server
- Entorno virtual de Python (recomendado)

## Configuración

1. Clonar el repositorio
2. Crear un entorno virtual (opcional pero recomendado):
```bash
python -m venv venv
source venv/bin/activate  # En Linux/Mac
.\venv\Scripts\activate   # En Windows
```

3. Instalar dependencias:
```bash
pip install -r backend/requirements.txt
```

4. Configurar variables de entorno:
   - Crear archivo `.env` en la carpeta `backend/` con las siguientes variables:
     ```
     DB_HOST=tu_servidor
     DB_USER=tu_usuario
     DB_PASSWORD=tu_contraseña
     DB_NAME=nombre_base_datos
     DB_PORT=puerto
     ```

## Ejecución

Para iniciar el servidor de desarrollo:

```bash
cd backend
uvicorn app.main:app --reload
```

El servidor estará disponible en `http://localhost:8000`

## Documentación API

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Endpoints Principales

### Asegurados

- `POST /api/v1/carga-masiva`: Permite la carga masiva de asegurados desde un archivo Excel.
  - Requiere un archivo Excel con las columnas específicas para los asegurados.
  - Retorna un resumen de la operación con el total de registros procesados y errores si los hubiera. 