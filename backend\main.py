from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.app.api.v1.endpoints import sua, patrones
from backend.app.core.config import settings

app = FastAPI(
    title="ToolSUA API",
    description="API para la gestión de datos SUA",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En producción, especificar los orígenes permitidos
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir routers
app.include_router(sua.router, prefix=settings.API_V1_STR)
app.include_router(patrones.router, prefix=f"{settings.API_V1_STR}/patrones")

@app.get("/")
async def root():
    return {"message": "Bienvenido a la API de ToolSUA"} 