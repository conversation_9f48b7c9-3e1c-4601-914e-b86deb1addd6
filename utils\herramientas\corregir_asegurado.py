"""
Script para corregir el registro del asegurado 03178452540.
Este script inserta correctamente el registro en la tabla Asegura
utilizando el tipo de descuento como número.
"""

import os
import sys
from conectores.conector_sua import ConectorSUA

def insertar_asegurado_faltante(conector, num_afil):
    """Inserta el registro faltante en la tabla Asegura"""
    print(f"\n=== CORRIGIENDO REGISTRO PARA NSS {num_afil} ===")
    
    try:
        # Primero verificar si el registro ya existe en Asegura
        query_check = f"SELECT COUNT(*) AS Total FROM Asegura WHERE NUM_AFIL = '{num_afil}'"
        cursor = conector.conn.cursor()
        cursor.execute(query_check)
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            print(f"El registro para NSS {num_afil} ya existe en Asegura. No se requiere corrección.")
            return False
        
        # Verificar si existe en Movtos
        query_movtos = f"SELECT * FROM Movtos WHERE NUM_AFIL = '{num_afil}' AND TIP_MOVS = '15'"
        cursor.execute(query_movtos)
        movtos_row = cursor.fetchone()
        
        if not movtos_row:
            print(f"No se encontró registro tipo 15 en Movtos para NSS {num_afil}. No se puede corregir.")
            return False
        
        # Obtener datos del registro Movtos
        reg_pat = movtos_row[0]
        num_credito = movtos_row[23] if len(movtos_row) > 23 else None
        valor_descuento = movtos_row[24] if len(movtos_row) > 24 else 0.0
        tipo_descuento_texto = movtos_row[25] if len(movtos_row) > 25 else None
        
        # Mapear el tipo de descuento texto a número
        tipo_descuento_num = 0
        if tipo_descuento_texto:
            tipo_lower = tipo_descuento_texto.lower().strip()
            if 'porcentaje' in tipo_lower:
                tipo_descuento_num = 1
            elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
                tipo_descuento_num = 2
            elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
                tipo_descuento_num = 3
        
        print(f"Tipo de descuento original: '{tipo_descuento_texto}' -> convertido a número: {tipo_descuento_num}")
        
        # Consultar los datos básicos del asegurado desde otra tabla (como Afiliacion)
        query_afiliacion = f"SELECT * FROM Afiliacion WHERE NUM_AFIL = '{num_afil}'"
        cursor.execute(query_afiliacion)
        afiliacion_row = cursor.fetchone()
        
        if not afiliacion_row:
            print(f"No se encontraron datos en Afiliacion para NSS {num_afil}. No se puede corregir.")
            return False
        
        # Obtener otros datos necesarios de la tabla Movtos (tipo 01)
        query_alta = f"SELECT * FROM Movtos WHERE NUM_AFIL = '{num_afil}' AND TIP_MOVS = '01'"
        cursor.execute(query_alta)
        alta_row = cursor.fetchone()
        
        if not alta_row:
            print(f"No se encontró registro de alta (tipo 01) en Movtos para NSS {num_afil}. No se puede corregir.")
            return False
        
        # Obtener salario del registro de alta
        salario = alta_row[6] if len(alta_row) > 6 and alta_row[6] is not None else 0.0
        fecha_alta = alta_row[3] if len(alta_row) > 3 and alta_row[3] is not None else None
        
        if not fecha_alta:
            print("Fecha de alta no disponible. No se puede corregir.")
            return False
        
        # Valores por defecto para los campos obligatorios que no tenemos
        curp = "XXXX000000XXXXXX00"  # CURP por defecto
        rfc = "XXXX000000XXX"        # RFC por defecto
        nom_aseg = "APELLIDO1$APELLIDO2$NOMBRES"  # Nombre por defecto
        cve_mun = "E53"              # Código de municipio por defecto (Guadalajara)
        cve_ubc = "OBRA A"           # Ubicación por defecto
        tmp_nom = "APELLIDO1 APELLIDO2 NOMBRES"  # Nombre completo por defecto
        
        # Obtener estos valores de Afiliacion si están disponibles
        if afiliacion_row:
            cpp_trab = afiliacion_row[2] if len(afiliacion_row) > 2 and afiliacion_row[2] else "45040"  # Código postal
            cve_mun = afiliacion_row[10] if len(afiliacion_row) > 10 and afiliacion_row[10] else "E53"  # Municipio
        
        # Construir la consulta SQL asegurando que los valores numéricos sean correctos
        sql = f"""
        INSERT INTO Asegura (
            REG_PATR, NUM_AFIL, CURP, RFC_CURP, NOM_ASEG, 
            SAL_IMSS, FEC_ALT, TIP_TRA, SEM_JORD, PAG_INFO,
            TIP_DSC, VAL_DSC, CVE_UBC, TMP_NOM, 
            FEC_DSC, TRA_PENIV, ESTADO, CVE_MUN
        ) VALUES (
            '{reg_pat}', '{num_afil}', '{curp}', '{rfc}', '{nom_aseg}', 
            {salario}, '{fecha_alta}', 1, 0, '{num_credito}',
            {tipo_descuento_num}, {valor_descuento}, '{cve_ubc}', '{tmp_nom}', 
            '{fecha_alta}', 0, 'S', '{cve_mun}'
        )
        """
        
        print("\nSQL a ejecutar:")
        print(sql)
        
        # Ejecutar la inserción
        cursor.execute(sql)
        conector.conn.commit()
        
        print(f"\nRegistro insertado correctamente en Asegura para NSS {num_afil}")
        return True
        
    except Exception as e:
        print(f"Error al corregir registro: {e}")
        return False

def main():
    # Ruta a la base de datos SUA
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    
    # Conectar a la base de datos
    print(f"Conectando a la base de datos: {ruta_bd}")
    conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
    
    if not conector.conectar():
        print("ERROR: No se pudo conectar a la base de datos")
        return
    
    try:
        # Corregir el registro problemático
        nss_problematico = '03178452540'
        insertar_asegurado_faltante(conector, nss_problematico)
        
    finally:
        # Cerrar conexión
        if conector.conn:
            conector.desconectar()
            print("\nConexión a la base de datos cerrada.")

if __name__ == "__main__":
    main() 