from carga_masiva_movimientos import CargaMasivaMovimientos
import tkinter as tk
import os
from crear_archivo_test import crear_archivo_test

def probar_carga_masiva_desde_archivo():
    """Prueba la funcionalidad de carga masiva de movimientos desde un archivo Excel"""
    try:
        # Crear archivo de prueba
        ruta_archivo = crear_archivo_test()
        
        # Verificar que el archivo exista
        if not os.path.exists(ruta_archivo):
            print(f"Error: No se pudo crear el archivo de prueba en {ruta_archivo}")
            return False
        
        # Iniciar la aplicación tkinter
        root = tk.Tk()
        root.withdraw()  # Ocultar la ventana principal
        
        # Crear instancia de la clase
        app = CargaMasivaMovimientos(root)
        
        # Configurar rutas y contraseña
        app.archivo_excel.set(ruta_archivo)
        app.ruta_bd.set("C:\\Cobranza\\SUA\\SUA.MDB")
        app.password_bd.set("S5@N52V49")
        
        # Probar validación de Excel
        print("\nValidando archivo Excel...")
        if app.validar_excel():
            print("✅ Validación de Excel exitosa")
        else:
            print("❌ Error en validación de Excel")
            return False
        
        # Probar carga de movimientos
        print("\nProcesando movimientos desde archivo Excel...")
        app.cargar_movimientos()
        
        # Cerrar la aplicación
        root.destroy()
        
        print("\nPrueba completada")
        return True
    
    except Exception as e:
        print(f"Error en prueba: {e}")
        return False

if __name__ == "__main__":
    probar_carga_masiva_desde_archivo() 