import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from datetime import datetime

def crear_plantilla_uma():
    """
    Crea una plantilla Excel para la carga del catálogo de UMA.
    """
    # Crear nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "UMA"

    # Definir estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Encabezados
    headers = [
        "Fecha Inicio",
        "Valor UMA",
        "Observaciones"
    ]
    
    # Escribir encabezados
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        
    # Ajustar ancho de columnas
    ws.column_dimensions['A'].width = 15  # Fecha Inicio
    ws.column_dimensions['B'].width = 15  # Valor UMA
    ws.column_dimensions['C'].width = 30  # Observaciones

    # Agregar ejemplo
    ejemplo = [
        datetime(2024, 2, 1),  # Fecha Inicio
        103.74,  # Valor UMA
        "UMA vigente para 2024"  # Observaciones
    ]
    
    for col, valor in enumerate(ejemplo, 1):
        cell = ws.cell(row=2, column=col)
        cell.value = valor
        if col == 1:  # Para fecha
            cell.number_format = 'dd/mm/yyyy'

    # Agregar notas
    notas = [
        "1. La Fecha Inicio debe ser siempre el 01/02 del año correspondiente",
        "2. El sistema calculará automáticamente la Fecha Fin:",
        "   - Para registros normales: 31/01 del año siguiente",
        "   - Para el último registro: 31/12/2999",
        "3. El Valor UMA debe incluir 2 decimales",
        "4. Los registros deben estar ordenados por fecha de inicio",
        "5. No debe haber solapamiento de períodos"
    ]
    
    for i, nota in enumerate(notas, 1):
        ws.cell(row=4+i, column=1, value=nota)

    # Guardar archivo
    nombre_archivo = f"Plantilla_UMA_{datetime.now().strftime('%Y%m%d')}.xlsx"
    wb.save(nombre_archivo)
    return nombre_archivo

def crear_plantilla_umi():
    """Crea una plantilla Excel para el catálogo UMI"""
    # Crear un nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "UMI"
    
    # Definir estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Escribir encabezados
    headers = ["FEC_INIC", "VALOR", "DESCRIPCION"]
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Ajustar ancho de columnas
    ws.column_dimensions['A'].width = 15  # FEC_INIC
    ws.column_dimensions['B'].width = 15  # VALOR
    ws.column_dimensions['C'].width = 40  # DESCRIPCION
    
    # Agregar datos de ejemplo
    ejemplo = [
        ["01/01/2024", 100.81, "Valor de la UMI para el período"]
    ]
    
    for row_num, row_data in enumerate(ejemplo, 2):
        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num, value=value)
            if col_num == 1:  # Formato para fecha
                cell.number_format = 'DD/MM/YYYY'
            elif col_num == 2:  # Formato para valor
                cell.number_format = '#,##0.00'
    
    # Agregar notas
    notas = [
        "INSTRUCCIONES:",
        "1. Todas las fechas deben ser 01/01 del año correspondiente",
        "2. Los registros deben estar ordenados por fecha",
        "3. No debe haber solapamiento entre períodos",
        "4. La fecha fin se calculará automáticamente como 31/12 del mismo año",
        "5. Para el último registro, la fecha fin será 31/12/2999",
        "6. Los valores deben ser numéricos"
    ]
    
    for i, nota in enumerate(notas, row_num + 2):
        ws.cell(row=i, column=1, value=nota)
    
    # Guardar el archivo
    fecha_actual = datetime.now().strftime("%Y%m%d")
    nombre_archivo = f"Plantilla_UMI_{fecha_actual}.xlsx"
    wb.save(nombre_archivo)
    
    print(f"Plantillas UMA y UMI creadas")
    return nombre_archivo

def crear_plantilla_inpc():
    """Crea una plantilla Excel para el catálogo INPC"""
    # Crear un nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "INPC"
    
    # Definir estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Escribir encabezados
    headers = ["Fecha", "Valor INPC", "Valor Rec"]
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Ajustar ancho de columnas
    ws.column_dimensions['A'].width = 15  # Fecha
    ws.column_dimensions['B'].width = 15  # Valor INPC
    ws.column_dimensions['C'].width = 15  # Valor Rec
    
    # Agregar datos de ejemplo
    ejemplo = [
        ["01/01/2024", 139.161, 1.47]
    ]
    
    for row_num, row_data in enumerate(ejemplo, 2):
        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num, value=value)
            if col_num == 1:  # Formato para fecha
                cell.number_format = 'DD/MM/YYYY'
            elif col_num in [2, 3]:  # Formato para valores
                cell.number_format = '#,##0.000'
    
    # Agregar notas
    notas = [
        "INSTRUCCIONES:",
        "1. La fecha debe ser el primer día del mes",
        "2. Los valores deben ser numéricos con 2 decimales",
        "3. Los registros deben estar ordenados por fecha",
        "4. El sistema convertirá automáticamente la fecha al formato AAAAMM"
    ]
    
    for i, nota in enumerate(notas, row_num + 2):
        ws.cell(row=i, column=1, value=nota)
    
    # Guardar el archivo
    fecha_actual = datetime.now().strftime("%Y%m%d")
    nombre_archivo = f"Plantilla_INPC_{fecha_actual}.xlsx"
    wb.save(nombre_archivo)
    
    return nombre_archivo

def crear_plantilla_salario():
    """Crea una plantilla Excel para el catálogo de salarios mínimos"""
    # Crear un nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "SALARIO"
    
    # Definir estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Escribir encabezados
    headers = [
        "Fecha Inicio",
        "Salario"
    ]
    
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Ajustar ancho de columnas
    ws.column_dimensions['A'].width = 15  # Fecha Inicio
    ws.column_dimensions['B'].width = 15  # Salario
    
    # Agregar datos de ejemplo
    ejemplo = [
        ["01/01/2024", 248.93]
    ]
    
    for row_num, row_data in enumerate(ejemplo, 2):
        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num, value=value)
            if col_num == 1:  # Formato para fecha
                cell.number_format = 'DD/MM/YYYY'
            else:  # Formato para valor
                cell.number_format = '#,##0.00'
    
    # Agregar notas
    notas = [
        "INSTRUCCIONES:",
        "1. La fecha debe ser el primer día del mes",
        "2. El salario debe ser numérico con 2 decimales",
        "3. Los registros deben estar ordenados por fecha",
        "4. No debe haber solapamiento entre períodos",
        "5. La fecha fin se calculará automáticamente como 31/12 del año correspondiente",
        "6. Para el último registro, la fecha fin será 31/12/2999",
        "7. El sistema calculará automáticamente los aumentos multiplicando el salario por 1.04932",
        "8. Los valores de salario y aumentos se aplicarán a las tres zonas (A, B y C)"
    ]
    
    for i, nota in enumerate(notas, row_num + 2):
        ws.cell(row=i, column=1, value=nota)
    
    # Guardar el archivo
    fecha_actual = datetime.now().strftime("%Y%m%d")
    nombre_archivo = f"Plantilla_SALARIO_{fecha_actual}.xlsx"
    wb.save(nombre_archivo)
    
    return nombre_archivo

if __name__ == "__main__":
    archivo_uma = crear_plantilla_uma()
    archivo_umi = crear_plantilla_umi()
    archivo_inpc = crear_plantilla_inpc()
    archivo_salario = crear_plantilla_salario()
    print(f"Plantillas creadas: {archivo_uma}, {archivo_umi}, {archivo_inpc}, {archivo_salario}") 