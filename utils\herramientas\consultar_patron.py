import pyodbc

try:
    # Conexión a la base de datos
    conn = pyodbc.connect(
        r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
        r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
        r'PWD=S5@N52V49'
    )
    
    cursor = conn.cursor()
    
    # Ejecutar consulta
    cursor.execute('SELECT TOP 5 REG_PAT, Fraccion FROM Patron WHERE Fraccion IS NOT NULL')
    
    # Mostrar resultados
    rows = cursor.fetchall()
    if not rows:
        print("No se encontraron registros con Fraccion no nula")
    else:
        print("Registros encontrados:")
        for row in rows:
            print(f"REG_PAT: {row.REG_PAT}, Fraccion: {row.Fraccion}")
    
    # Consultar estructura de la tabla
    print("\nEstructura de la tabla Patron:")
    cursor.execute("SELECT * FROM Patron WHERE 1=0")
    for column in cursor.description:
        print(f"Columna: {column[0]}, Tipo: {column[1].__name__}")
    
    conn.close()
    
except Exception as e:
    print(f"Error: {e}") 