from conectores.conector_sua import ConectorSUA
from datetime import datetime

def probar_reingreso_sua():
    try:
        # Conectar a la base de datos
        print("Conectando a la base de datos SUA...")
        conector = ConectorSUA(password='S5@N52V49')
        if not conector.conectar():
            print("Error al conectar a la base de datos")
            return False
        
        # Datos del empleado a reingresar
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        # Obtener fecha actual para el reingreso (1 día después de la última baja)
        fecha_reingreso = datetime.now().strftime("%Y-%m-%d")
        
        print(f"Procesando reingreso para: {reg_patr} - {num_afil} con fecha {fecha_reingreso}")
        
        # Preparar datos del movimiento
        datos_mov = {
            'REG_PATR': reg_patr,
            'NUM_AFIL': num_afil,
            'FEC_INIC': fecha_reingreso,
            'SAL_MOVT': 450.00  # Salario actualizado
        }
        
        # Procesar el reingreso
        resultado = conector.procesar_reingreso(datos_mov)
        
        # Verificar el resultado
        if resultado:
            print("✅ Reingreso procesado exitosamente")
            
            # Ahora intentamos dar de baja al empleado
            print("\nProcesando baja después del reingreso...")
            
            # Fecha de baja posterior al reingreso
            fecha_baja = datetime.now().strftime("%Y-%m-%d")
            
            datos_baja = {
                'REG_PATR': reg_patr,
                'NUM_AFIL': num_afil,
                'FEC_INIC': fecha_baja
            }
            
            resultado_baja = conector.procesar_baja(datos_baja)
            
            if resultado_baja:
                print("✅ Baja después de reingreso procesada exitosamente")
            else:
                print("❌ Error al procesar la baja después del reingreso")
        else:
            print("❌ Error al procesar el reingreso")
        
        # Desconectar
        conector.desconectar()
        return resultado
    
    except Exception as e:
        print(f"Error en la prueba: {str(e)}")
        return False

if __name__ == "__main__":
    probar_reingreso_sua() 