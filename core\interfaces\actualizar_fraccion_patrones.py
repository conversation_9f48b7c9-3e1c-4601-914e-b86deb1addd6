#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para actualizar la fracción de patrones en la base de datos SUA
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import datetime
import traceback
from pathlib import Path

import pandas as pd

# Asegurar que los módulos sean accesibles
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

# Importar el conector personalizado
from conectores.conector_sua import ConectorSUA

class ActualizadorFraccionApp:
    """Aplicación para actualizar la fracción de patrones en la base de datos SUA"""
    
    def __init__(self, root):
        """Inicializa la aplicación con la ventana root"""
        self.root = root
        self.root.title("Actualizador de Fracción - SUA Tool")
        self.root.geometry("800x600")
        self.root.minsize(800, 600)
        
        # Variables de control
        self.conector = None
        self.ruta_bd = tk.StringVar()
        self.ruta_excel = tk.StringVar()
        self.procesando = False
        self.status_var = tk.StringVar(value="Listo")
        self.progress_var = tk.DoubleVar(value=0)
        
        # Configurar estilo
        self.configurar_estilo()
        
        # Crear interfaz
        self.crear_interfaz()
        
        # Inicializar estadísticas
        self.estadisticas = {
            'patrones_actualizados': 0,
            'patrones_no_encontrados': 0,
            'errores_actualizacion': 0
        }
    
    def configurar_estilo(self):
        """Configura el estilo de la aplicación"""
        style = ttk.Style()
        style.configure('TButton', font=('Segoe UI', 10))
        style.configure('TLabel', font=('Segoe UI', 10))
        style.configure('Header.TLabel', font=('Segoe UI', 12, 'bold'))
        style.configure('Status.TLabel', font=('Segoe UI', 9), padding=2)
    
    def crear_interfaz(self):
        """Crea los elementos de la interfaz gráfica"""
        # Marco principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        header = ttk.Label(main_frame, text="Actualizador de Fracción para Patrones SUA", style='Header.TLabel')
        header.pack(pady=10)
        
        # Marco para selección de archivos
        file_frame = ttk.LabelFrame(main_frame, text="Selección de Archivos", padding="10")
        file_frame.pack(fill=tk.X, pady=5)
        
        # Selector de base de datos
        bd_frame = ttk.Frame(file_frame)
        bd_frame.pack(fill=tk.X, pady=5)
        ttk.Label(bd_frame, text="Base de Datos SUA:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(bd_frame, textvariable=self.ruta_bd, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(bd_frame, text="Examinar...", command=self.seleccionar_bd).pack(side=tk.LEFT, padx=5)
        
        # Selector de Excel
        excel_frame = ttk.Frame(file_frame)
        excel_frame.pack(fill=tk.X, pady=5)
        ttk.Label(excel_frame, text="Archivo Excel:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(excel_frame, textvariable=self.ruta_excel, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(excel_frame, text="Examinar...", command=self.seleccionar_excel).pack(side=tk.LEFT, padx=5)
        
        # Marco para botones de acción
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        self.btn_probar = ttk.Button(action_frame, text="Probar Conexión", command=self.probar_conexion)
        self.btn_probar.pack(side=tk.LEFT, padx=5)
        
        self.btn_validar = ttk.Button(action_frame, text="Validar Excel", command=self.validar_excel)
        self.btn_validar.pack(side=tk.LEFT, padx=5)
        
        self.btn_procesar = ttk.Button(action_frame, text="Actualizar Fracciones", command=self.iniciar_actualizacion)
        self.btn_procesar.pack(side=tk.LEFT, padx=5)
        
        self.btn_limpiar = ttk.Button(action_frame, text="Limpiar Log", command=self.limpiar_log)
        self.btn_limpiar.pack(side=tk.RIGHT, padx=5)
        
        # Área de logs
        log_frame = ttk.LabelFrame(main_frame, text="Registro de Actividad", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # ScrollBar para el log
        scrollbar = ttk.Scrollbar(log_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Área de texto para el log
        self.log_area = tk.Text(log_frame, height=15, width=80, yscrollcommand=scrollbar.set, wrap=tk.WORD)
        self.log_area.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.log_area.yview)
        
        # Configuración inicial del área de log
        self.log_area.config(state=tk.DISABLED)
        self.log("Bienvenido al Actualizador de Fracción para Patrones SUA.")
        self.log("Seleccione una base de datos SUA y un archivo Excel con los datos para actualizar.")
        
        # Barra de progreso
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(progress_frame, text="Progreso:").pack(side=tk.LEFT, padx=5)
        ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # Barra de estado
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(status_frame, textvariable=self.status_var, style='Status.TLabel').pack(side=tk.LEFT, padx=5)
    
    def seleccionar_bd(self):
        """Muestra un diálogo para seleccionar la base de datos SUA"""
        filename = filedialog.askopenfilename(filetypes=[("Base de datos Access", "*.mdb"), ("Todos los archivos", "*.*")])
        if filename:
            self.ruta_bd.set(filename)
            self.log(f"Base de datos seleccionada: {filename}")
    
    def seleccionar_excel(self):
        """Muestra un diálogo para seleccionar el archivo Excel"""
        filename = filedialog.askopenfilename(filetypes=[("Archivo Excel", "*.xlsx *.xls"), ("Todos los archivos", "*.*")])
        if filename:
            self.ruta_excel.set(filename)
            self.log(f"Archivo Excel seleccionado: {filename}")
    
    def probar_conexion(self):
        """Prueba la conexión a la base de datos"""
        self.btn_probar.config(state=tk.DISABLED)
        self.status_var.set("Probando conexión...")
        
        def _probar():
            try:
                ruta_bd = self.ruta_bd.get()
                
                if not ruta_bd:
                    self.log("Error: No se ha seleccionado una base de datos.", error=True)
                    messagebox.showerror("Error", "Por favor, seleccione una base de datos SUA.")
                    return
                
                if not os.path.exists(ruta_bd):
                    self.log("Error: La ruta de la base de datos no existe.", error=True)
                    messagebox.showerror("Error", "La ruta de la base de datos no existe.")
                    return
                
                # Conectar a la base de datos
                self.log("Conectando a la base de datos...")
                conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
                
                if not conector.conectar():
                    self.log("Error: No se pudo conectar a la base de datos.", error=True)
                    messagebox.showerror("Error", "No se pudo conectar a la base de datos.")
                    return
                
                # Verificar tabla Patron
                query = "SELECT TOP 1 * FROM Patron"
                resultado = conector.ejecutar_consulta(query)
                
                if not resultado:
                    self.log("Advertencia: No se encontraron registros en la tabla Patron.", warning=True)
                    messagebox.showwarning("Advertencia", "No hay registros en la tabla Patron.")
                else:
                    self.log("Conexión exitosa. La tabla Patron existe y es accesible.", success=True)
                    # Contar registros
                    count_query = "SELECT COUNT(*) AS Total FROM Patron"
                    count_result = conector.ejecutar_consulta(count_query)
                    if count_result:
                        total = count_result[0]['Total']
                        self.log(f"La tabla Patron contiene {total} registros.")
                
                # Cerrar conexión
                conector.desconectar()
                self.log("Prueba de conexión completada.")
                messagebox.showinfo("Conexión Exitosa", "Se ha establecido conexión con la base de datos correctamente.")
                
            except Exception as e:
                self.log(f"Error durante la prueba de conexión: {str(e)}", error=True)
                messagebox.showerror("Error", f"Ocurrió un error: {str(e)}")
            
            finally:
                self.btn_probar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_probar)
        thread.daemon = True
        thread.start()
    
    def validar_excel(self):
        """Valida el archivo Excel para asegurar que contiene los campos necesarios"""
        self.btn_validar.config(state=tk.DISABLED)
        self.status_var.set("Validando Excel...")
        
        def _validar():
            try:
                ruta_excel = self.ruta_excel.get()
                
                if not ruta_excel:
                    self.log("Error: No se ha seleccionado un archivo Excel.", error=True)
                    messagebox.showerror("Error", "Por favor, seleccione un archivo Excel.")
                    return
                
                if not os.path.exists(ruta_excel):
                    self.log("Error: La ruta del archivo Excel no existe.", error=True)
                    messagebox.showerror("Error", "La ruta del archivo Excel no existe.")
                    return
                
                # Leer el Excel
                self.log("Leyendo datos del archivo Excel...")
                try:
                    df = pd.read_excel(ruta_excel)
                except Exception as e:
                    self.log(f"Error al leer el archivo Excel: {str(e)}", error=True)
                    messagebox.showerror("Error", f"No se pudo leer el archivo Excel: {str(e)}")
                    return
                
                # Verificar campos requeridos
                campos_requeridos = ["Registro Patronal", "Fracción"]
                campos_faltantes = [campo for campo in campos_requeridos if campo not in df.columns]
                
                if campos_faltantes:
                    self.log(f"Error: Faltan campos requeridos en el Excel: {', '.join(campos_faltantes)}", error=True)
                    messagebox.showerror("Error", f"El archivo Excel no contiene todos los campos requeridos. Falta: {', '.join(campos_faltantes)}")
                    return
                
                # Verificar datos
                registros_validos = 0
                registros_invalidos = 0
                
                for idx, row in df.iterrows():
                    reg_pat = str(row.get("Registro Patronal", "")).strip()
                    fraccion = row.get("Fracción", "")
                    
                    if not reg_pat or pd.isna(reg_pat):
                        registros_invalidos += 1
                        self.log(f"Fila {idx+2}: Registro Patronal vacío o inválido", warning=True)
                        continue
                    
                    if pd.isna(fraccion):
                        registros_invalidos += 1
                        self.log(f"Fila {idx+2}: Fracción vacía o inválida para {reg_pat}", warning=True)
                        continue
                    
                    registros_validos += 1
                
                self.log(f"Validación completada. {registros_validos} registros válidos, {registros_invalidos} registros con problemas.", success=True)
                
                if registros_validos > 0:
                    messagebox.showinfo("Validación Exitosa", f"El archivo Excel es válido. Contiene {registros_validos} registros para procesar.")
                else:
                    messagebox.showwarning("Advertencia", "No se encontraron registros válidos para procesar.")
            
            except Exception as e:
                self.log(f"Error durante la validación: {str(e)}", error=True)
                messagebox.showerror("Error", f"Ocurrió un error: {str(e)}")
            
            finally:
                self.btn_validar.config(state=tk.NORMAL)
                self.status_var.set("Listo")
        
        thread = threading.Thread(target=_validar)
        thread.daemon = True
        thread.start()
    
    def iniciar_actualizacion(self):
        """Inicia el proceso de actualización de fracciones"""
        if self.procesando:
            messagebox.showwarning("En proceso", "Ya hay un proceso en ejecución.")
            return
        
        # Deshabilitar botones durante el proceso
        self.btn_procesar.config(state=tk.DISABLED)
        self.btn_probar.config(state=tk.DISABLED)
        self.btn_validar.config(state=tk.DISABLED)
        
        self.procesando = True
        self.status_var.set("Iniciando procesamiento...")
        self.update_progress(0)
        
        # Reiniciar estadísticas
        self.estadisticas = {
            'patrones_actualizados': 0,
            'patrones_no_encontrados': 0,
            'errores_actualizacion': 0
        }
        
        def _procesar():
            try:
                ruta_bd = self.ruta_bd.get()
                ruta_excel = self.ruta_excel.get()
                
                if not ruta_bd:
                    self.log("Error: No se ha seleccionado una base de datos.", error=True)
                    return
                
                if not ruta_excel:
                    self.log("Error: No se ha seleccionado un archivo Excel.", error=True)
                    return
                
                if not os.path.exists(ruta_bd):
                    self.log("Error: La ruta de la base de datos no existe.", error=True)
                    return
                
                if not os.path.exists(ruta_excel):
                    self.log("Error: La ruta del archivo Excel no existe.", error=True)
                    return
                
                # Conectar a la base de datos
                self.log("Conectando a la base de datos...")
                self.conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
                
                if not self.conector.conectar():
                    self.log("Error: No se pudo conectar a la base de datos.", error=True)
                    return
                
                # Leer el Excel
                self.log("Leyendo datos del archivo Excel...")
                df = pd.read_excel(ruta_excel)
                self.log(f"Se encontraron {len(df)} registros para procesar.")
                
                # Iniciar procesamiento
                self.log("\n=== INICIANDO ACTUALIZACIÓN DE FRACCIONES ===\n")
                
                # Procesar registros
                registros_procesados = 0
                errores = []
                total_registros = len(df)
                
                for idx, row in df.iterrows():
                    # Actualizar progreso
                    progress = (idx + 1) / total_registros * 100
                    self.update_progress(progress)
                    self.status_var.set(f"Procesando registro {idx+1} de {total_registros}...")
                    
                    try:
                        # Verificar si el registro ya existe
                        reg_pat = str(row.get("Registro Patronal", "")).strip()
                        if not reg_pat:
                            errores.append(f"Fila {idx+2}: El Registro Patronal es obligatorio")
                            continue
                        
                        # Obtener fracción
                        fraccion_valor = row.get("Fracción", "")
                        if pd.isna(fraccion_valor):
                            errores.append(f"Fila {idx+2}: La Fracción es obligatoria para {reg_pat}")
                            continue
                        
                        # Formatear fracción
                        fraccion = self.buscar_fraccion(fraccion_valor)
                        
                        # Actualizar fracción
                        if self.actualizar_fraccion_patron(reg_pat, fraccion):
                            self.log(f"✓ Registro {idx+1}: Fracción actualizada a '{fraccion}' para patrón {reg_pat}", success=True)
                            registros_procesados += 1
                        else:
                            # El método actualizar_fraccion_patron ya registró el error
                            pass
                    
                    except Exception as e:
                        errores.append(f"Error en fila {idx+2}: {str(e)}")
                        self.log(f"✗ Error en registro {idx+1}: {str(e)}", error=True)
                
                # Resultados finales
                self.update_progress(100)
                self.log("\n=== RESUMEN DEL PROCESO ===")
                self.log(f"- Patrones actualizados: {self.estadisticas['patrones_actualizados']}")
                self.log(f"- Patrones no encontrados: {self.estadisticas['patrones_no_encontrados']}")
                self.log(f"- Errores de actualización: {self.estadisticas['errores_actualizacion']}")
                
                if errores:
                    self.log("\nDetalle de errores:", warning=True)
                    for error in errores:
                        self.log(f"  • {error}", warning=True)
                    
                    # Guardar errores en un archivo
                    directorio_salida = Path("resultados")
                    directorio_salida.mkdir(exist_ok=True)
                    
                    nombre_archivo = f"errores_actualizacion_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    ruta_archivo = directorio_salida / nombre_archivo
                    
                    with open(ruta_archivo, 'w', encoding='utf-8') as f:
                        f.write("ERRORES EN ACTUALIZACIÓN DE FRACCIONES\n")
                        f.write(f"Fecha: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Archivo: {ruta_excel}\n\n")
                        for error in errores:
                            f.write(f"{error}\n")
                    
                    self.log(f"\nSe ha guardado un reporte de errores en: {ruta_archivo}")
                
                if registros_procesados > 0:
                    self.log("\n¡Proceso completado con éxito!", success=True)
                else:
                    self.log("\nProceso completado sin registros actualizados.", warning=True)
            
            except Exception as e:
                self.log(f"Error durante el procesamiento: {str(e)}", error=True)
                traceback.print_exc()
            
            finally:
                # Cerrar conexión
                if self.conector and hasattr(self.conector, 'conn') and self.conector.conn:
                    self.conector.desconectar()
                    self.log("Conexión a la base de datos cerrada.")
                
                # Habilitar botones
                self.btn_procesar.config(state=tk.NORMAL)
                self.btn_probar.config(state=tk.NORMAL)
                self.btn_validar.config(state=tk.NORMAL)
                
                self.status_var.set("Listo")
                self.procesando = False
        
        thread = threading.Thread(target=_procesar)
        thread.daemon = True
        thread.start()
    
    def buscar_fraccion(self, fraccion_valor):
        """
        Formatea correctamente la fracción con tres dígitos para mantener los ceros a la izquierda.
        
        Esta función toma un valor de fracción (número o texto) y garantiza que sea una cadena
        de 3 dígitos con ceros a la izquierda. Esto es necesario porque el campo Fraccion 
        en la tabla Patron es de tipo texto y queremos mantener un formato consistente.
        
        Ejemplos:
        - "43" se convierte en "043"
        - "5" se convierte en "005"
        - "123" se mantiene como "123"
        """
        if not fraccion_valor or pd.isna(fraccion_valor):
            # Valor por defecto si no hay dato
            self.log("Fracción no especificada, usando valor por defecto '000'", warning=True)
            return "000"
        
        # Convertir a string para comparación (sin ceros a la izquierda)
        if isinstance(fraccion_valor, (int, float)):
            valor_comparacion = str(int(fraccion_valor))
        else:
            valor_comparacion = str(fraccion_valor).strip()
        
        # Formatear directamente sin buscar en la tabla
        formatted_value = valor_comparacion.zfill(3)
        self.log(f"Formateando fracción '{valor_comparacion}' a '{formatted_value}'", success=True)
        return formatted_value
    
    def actualizar_fraccion_patron(self, reg_pat, fraccion):
        """Actualiza la fracción de un patrón en la base de datos"""
        try:
            # Verificar si existe el registro patronal
            cursor = self.conector.conn.cursor()
            consulta_verificacion = f"SELECT COUNT(*) FROM Patron WHERE REG_PAT = '{reg_pat}'"
            cursor.execute(consulta_verificacion)
            count = cursor.fetchone()[0]
            
            if count == 0:
                self.log(f"No se encontró el registro patronal {reg_pat} en la base de datos", warning=True)
                self.estadisticas['patrones_no_encontrados'] += 1
                return False
            
            # Escapar comillas si las hay
            fraccion = fraccion.replace("'", "''")
            
            # Preparar la consulta SQL de actualización
            consulta_actualizacion = f"""
            UPDATE Patron 
            SET Fraccion = '{fraccion}'
            WHERE REG_PAT = '{reg_pat}'
            """
            
            # Ejecutar la actualización
            self.log(f"Ejecutando actualización de fracción para patrón {reg_pat}...")
            cursor.execute(consulta_actualizacion)
            self.conector.conn.commit()
            
            # Verificar que la actualización fue exitosa
            consulta_verificacion = f"SELECT Fraccion FROM Patron WHERE REG_PAT = '{reg_pat}'"
            cursor.execute(consulta_verificacion)
            resultado = cursor.fetchone()
            
            if resultado and resultado[0] == fraccion:
                self.log(f"Actualización exitosa: Patrón {reg_pat} ahora tiene fracción '{fraccion}'", success=True)
                self.estadisticas['patrones_actualizados'] += 1
                return True
            else:
                self.log(f"La actualización no se reflejó correctamente para patrón {reg_pat}", warning=True)
                self.estadisticas['errores_actualizacion'] += 1
                return False
                
        except Exception as e:
            self.log(f"Error al actualizar fracción del patrón {reg_pat}: {e}", error=True)
            self.estadisticas['errores_actualizacion'] += 1
            return False
    
    def log(self, mensaje, error=False, warning=False, success=False):
        """Agrega un mensaje al área de logs"""
        self.log_area.config(state=tk.NORMAL)
        
        # Agregar timestamp
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.insert(tk.END, f"[{timestamp}] ")
        
        # Formatear mensaje según tipo
        tag = None
        if error:
            tag = "error"
            self.log_area.tag_configure(tag, foreground="red")
        elif warning:
            tag = "warning"
            self.log_area.tag_configure(tag, foreground="orange")
        elif success:
            tag = "success"
            self.log_area.tag_configure(tag, foreground="green")
        
        # Insertar mensaje
        start_index = self.log_area.index(tk.END+"-1c")
        self.log_area.insert(tk.END, f"{mensaje}\n")
        
        # Aplicar tag si corresponde
        if tag:
            end_index = self.log_area.index(tk.END+"-1c")
            self.log_area.tag_add(tag, start_index, end_index)
        
        # Auto-scroll
        self.log_area.see(tk.END)
        self.log_area.config(state=tk.DISABLED)
    
    def limpiar_log(self):
        """Limpia el área de logs"""
        self.log_area.config(state=tk.NORMAL)
        self.log_area.delete(1.0, tk.END)
        self.log_area.config(state=tk.DISABLED)
        self.log("Log limpiado.")
    
    def update_progress(self, value):
        """Actualiza el valor de la barra de progreso"""
        self.progress_var.set(value)
        self.root.update_idletasks()

def main():
    """Función principal"""
    root = tk.Tk()
    app = ActualizadorFraccionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 