import pyodbc

def main():
    try:
        # Establecer conexión
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa a la base de datos")
        
        # Consulta para obtener datos completos de un asegurado específico
        cursor = conn.cursor()
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        cursor.execute(f"SELECT * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'")
        
        row = cursor.fetchone()
        if row:
            print(f"\nDatos del asegurado {num_afil}:")
            for i, column in enumerate(cursor.description):
                print(f"{column[0]}: {row[i]}")
        else:
            print(f"No se encontró el asegurado {num_afil}")
        
        # Buscar todos los movimientos existentes
        cursor.execute(f"SELECT * FROM Movtos WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' ORDER BY FEC_INIC DESC")
        
        rows = cursor.fetchall()
        if rows:
            print(f"\nMovimientos del asegurado {num_afil}:")
            for idx, row in enumerate(rows, 1):
                tipo_mov = row.TIP_MOVS if hasattr(row, 'TIP_MOVS') else "N/A"
                fecha = row.FEC_INIC if hasattr(row, 'FEC_INIC') else "N/A"
                print(f"\nMovimiento {idx} - Tipo: {tipo_mov}, Fecha: {fecha}")
                for i, column in enumerate(cursor.description):
                    print(f"  {column[0]}: {row[i]}")
        else:
            print("No se encontraron movimientos")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 