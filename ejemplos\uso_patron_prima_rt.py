"""
Ejemplo de uso de los modelos Patron y PrimaRT.
Este script muestra cómo utilizar las clases y repositorios definidos
para acceder y manipular los datos de las tablas Patron y Prima_RT.
"""

import os
import sys
from pathlib import Path
import pandas as pd
import datetime
import getpass

# Agregar directorio raíz al path
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)

# Importar las clases necesarias
from conectores.conector_sua import ConectorSUA
from modelos.patron import Patron, PatronRepository
from modelos.prima_rt import PrimaRT, PrimaRTRepository

def crear_directorio_salida():
    """Crea un directorio para los archivos de salida"""
    output_dir = Path(script_dir) / "salida"
    output_dir.mkdir(exist_ok=True)
    return output_dir

def guardar_dataframe(df, nombre_archivo, directorio):
    """Guarda un DataFrame en un archivo CSV"""
    if df.empty:
        print(f"No hay datos para guardar en {nombre_archivo}")
        return None
    
    ruta_completa = directorio / f"{nombre_archivo}.csv"
    df.to_csv(ruta_completa, index=False, encoding='utf-8-sig')
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def solicitar_credenciales():
    """Solicita al usuario la ruta y contraseña de la base de datos"""
    print("\n=== CONFIGURACIÓN DE LA BASE DE DATOS ===")
    
    # Solicitar ruta de la base de datos
    ruta_predeterminada = r'C:\Cobranza\SUA\SUA.MDB'
    ruta_input = input(f"Ruta a la base de datos SUA [Enter para usar {ruta_predeterminada}]: ")
    ruta_bd = ruta_input.strip() if ruta_input.strip() else ruta_predeterminada
    
    # Verificar si la base existe
    if not os.path.exists(ruta_bd):
        print(f"¡Advertencia! La ruta {ruta_bd} no existe.")
        opciones = input("¿Desea continuar de todos modos? (s/n): ").lower()
        if opciones != 's':
            print("Operación cancelada.")
            sys.exit(0)
    
    # Usar contraseña fija
    password = "S5@N52V49"
    print("Usando contraseña predeterminada para la base de datos.")
    
    return ruta_bd, password

def ejemplo_patron(conector=None):
    """Ejemplo de uso del repositorio de Patron"""
    print("\n=== EJEMPLO DE USO DEL REPOSITORIO DE PATRON ===\n")
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return None, None
    
    # Crear repositorio con el conector
    patron_repo = PatronRepository(conector)
    
    # Obtener todos los patrones (limitado a 10)
    print("\nObteniendo lista de patrones:")
    patrones = patron_repo.obtener_todos(limite=10)
    print(f"Se encontraron {len(patrones)} patrones")
    
    if patrones:
        # Mostrar detalles del primer patrón
        print("\nDetalles del primer patrón:")
        primer_patron = patrones[0]
        for campo, valor in primer_patron.to_dict().items():
            print(f"  {campo}: {valor}")
        
        # Buscar patrones por criterio
        # Nota: Ajustar el campo según la estructura real
        if hasattr(primer_patron, 'nombre'):
            nombre_busqueda = primer_patron.nombre[:5]  # Usar primeras 5 letras como ejemplo
            print(f"\nBuscando patrones con nombre que contiene '{nombre_busqueda}':")
            patrones_buscados = patron_repo.buscar(nombre=nombre_busqueda)
            print(f"Se encontraron {len(patrones_buscados)} patrones coincidentes")
        
        # Contar total de patrones
        total_patrones = patron_repo.contar()
        print(f"\nTotal de patrones en la base de datos: {total_patrones}")
        
        # Convertir a DataFrame para análisis
        df_patrones = pd.DataFrame([p.to_dict() for p in patrones])
        
        # Guardar resultados
        output_dir = crear_directorio_salida()
        guardar_dataframe(df_patrones, "patrones_muestra", output_dir)
        
        return primer_patron.registro_patronal if hasattr(primer_patron, 'registro_patronal') else None, conector
    
    return None, conector

def ejemplo_prima_rt(registro_patronal=None, conector=None):
    """Ejemplo de uso del repositorio de Prima_RT"""
    print("\n=== EJEMPLO DE USO DEL REPOSITORIO DE PRIMA_RT ===\n")
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return
    
    # Crear repositorio
    prima_rt_repo = PrimaRTRepository(conector)
    
    # Obtener todas las primas (limitado a 10)
    print("\nObteniendo lista de primas RT:")
    primas = prima_rt_repo.obtener_todos(limite=10)
    print(f"Se encontraron {len(primas)} primas RT")
    
    if not primas:
        print("No se encontraron primas RT para analizar")
        return
    
    # Si no se proporciona un registro patronal, usar el del primer registro
    if not registro_patronal and hasattr(primas[0], 'registro_patronal'):
        registro_patronal = primas[0].registro_patronal
        
    if registro_patronal:
        # Obtener primas de un patrón específico
        print(f"\nObtener primas RT para el registro patronal '{registro_patronal}':")
        primas_patron = prima_rt_repo.obtener_por_registro_patronal(registro_patronal)
        print(f"Se encontraron {len(primas_patron)} primas RT asociadas a este registro patronal")
        
        # Ordenar por fecha
        if primas_patron and hasattr(primas_patron[0], 'fecha_modificacion'):
            primas_patron.sort(key=lambda p: p.fecha_modificacion)
            print("\nPrimas RT ordenadas por fecha:")
            for prima in primas_patron[:5]:  # Mostrar solo las primeras 5
                print(f"  {prima.fecha_modificacion}: {prima.prima_rt}")
        
        # Convertir a DataFrame y guardar
        if primas_patron:
            df_primas_patron = pd.DataFrame([p.to_dict() for p in primas_patron])
            output_dir = crear_directorio_salida()
            guardar_dataframe(df_primas_patron, f"primas_rt_{registro_patronal}", output_dir)
    
    # Convertir a DataFrame para análisis adicional
    df_primas = pd.DataFrame([p.to_dict() for p in primas])
    
    # Guardar resultados
    output_dir = crear_directorio_salida()
    guardar_dataframe(df_primas, "primas_rt_muestra", output_dir)

def ejemplo_analisis_combinado(registro_patronal=None, conector=None):
    """Ejemplo de análisis combinado de Patron y Prima_RT"""
    print("\n=== EJEMPLO DE ANÁLISIS COMBINADO ===\n")
    
    if not registro_patronal:
        print("No se proporcionó registro patronal para el análisis combinado")
        return
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return
    
    # Crear repositorios
    patron_repo = PatronRepository(conector)
    prima_rt_repo = PrimaRTRepository(conector)
    
    # Obtener datos del patrón
    patron = patron_repo.obtener_por_registro_patronal(registro_patronal)
    if not patron:
        print(f"No se encontró el patrón con registro patronal {registro_patronal}")
        return
    
    # Obtener historial de primas
    historial_primas = prima_rt_repo.obtener_historial(registro_patronal)
    if historial_primas.empty:
        print(f"No se encontró historial de primas para el registro patronal {registro_patronal}")
        return
    
    # Realizar análisis combinado
    print(f"\nAnálisis de primas RT para el patrón: {patron.nombre if hasattr(patron, 'nombre') else registro_patronal}")
    
    # Ejemplo: Calcular cambios en la prima RT
    if 'prima_rt' in historial_primas.columns and len(historial_primas) > 1:
        historial_primas['cambio'] = historial_primas['prima_rt'].diff()
        print("\nCambios en la prima RT a lo largo del tiempo:")
        print(historial_primas[['fecha_inicio', 'prima_rt', 'cambio']].tail().to_string(index=False))
        
        # Análisis estadístico
        print("\nEstadísticas descriptivas de la prima RT:")
        print(historial_primas['prima_rt'].describe())
    
    # Guardar resultados
    output_dir = crear_directorio_salida()
    guardar_dataframe(historial_primas, f"analisis_patron_{registro_patronal}", output_dir)

def main():
    """Función principal del ejemplo"""
    print("EJEMPLO DE USO DE LOS MODELOS PATRON Y PRIMA_RT\n")
    
    # Solicitar credenciales una sola vez
    ruta_bd, password = solicitar_credenciales()
    conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
    
    # Verificar conexión
    if not conector.conectar():
        print("Reintentando con la contraseña predeterminada...")
        conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
        if not conector.conectar():
            print("No se pudo establecer conexión con la base de datos. Abortando.")
            sys.exit(1)
    
    try:
        # Ejecutar ejemplo de Patron
        registro_patronal, conector = ejemplo_patron(conector)
        
        # Ejecutar ejemplo de Prima_RT
        ejemplo_prima_rt(registro_patronal, conector)
        
        # Ejecutar análisis combinado
        if registro_patronal:
            ejemplo_analisis_combinado(registro_patronal, conector)
        
    except Exception as e:
        print(f"Error durante la ejecución del ejemplo: {e}")
    
    finally:
        # Cerrar conexión
        if conector and conector.conn:
            conector.desconectar()
    
    print("\nEjemplo finalizado.")

if __name__ == "__main__":
    main() 