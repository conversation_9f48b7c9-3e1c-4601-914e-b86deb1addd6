# Documentación Completa de ToolSUA-V2

## 1. Introducción
ToolSUA-V2 es una herramienta especializada para el manejo y análisis de datos del Sistema Único de Autodeterminación (SUA) del IMSS. Permite la manipulación eficiente de datos, generación de reportes y análisis de información contenida en la base de datos SUA.

### 1.1 Objetivo
Proporcionar una solución integral para la gestión de datos SUA, facilitando la carga masiva de información, análisis de datos y generación de reportes para patrones y trabajadores.

### 1.2 Visión del Proyecto
- Convertirse en la herramienta estándar para la gestión de datos SUA en el sector construcción
- Automatizar y optimizar los procesos de carga y análisis de datos
- Reducir errores y tiempos de procesamiento
- Facilitar el cumplimiento de obligaciones ante el IMSS
- Proporcionar insights valiosos para la toma de decisiones

### 1.3 Modelo de Negocio
- **Mercado Objetivo**: Empresas constructoras y desarrolladoras que requieren gestionar datos SUA
- **Propuesta de Valor**: 
  - Reducción de costos operativos
  - Minimización de errores
  - Optimización de tiempos
  - Cumplimiento normativo
- **Canales**: 
  - Implementación directa
  - Soporte técnico especializado
  - Capacitación continua
- **Métricas Clave**:
  - Tiempo de procesamiento
  - Tasa de errores
  - Satisfacción del cliente
  - ROI de implementación

### 1.4 Valor Principal
- Eficiencia en la gestión de datos SUA
- Reducción de errores en la carga de información
- Automatización de procesos repetitivos
- Análisis avanzado de datos
- Integración con sistemas existentes
- Soporte para múltiples formatos de datos

## 2. Estructura del Proyecto

### 2.1 Core
- **Models**: Modelos de datos y entidades del sistema
  - `Asegurado`: Modelo unificado para gestión de trabajadores/asegurados
    - Modo básico: Campos esenciales para operaciones simples
    - Modo completo: Todos los campos necesarios para SUA
    - Validación automática de campos requeridos
    - Conversión a formato SUA
  - `Patron`: Modelo para gestión de patrones
  - `Movimiento`: Modelo para movimientos IMSS
  - `Incidencia`: Modelo para incidencias y ausencias
  - `Credito`: Modelo para créditos Infonavit

- **Conectores**: Integración con bases de datos y sistemas
  - `ConectorSUA`: Clase principal para interactuar con SUA.MDB
    - Gestión de conexión y desconexión
    - Operaciones CRUD para asegurados
    - Procesamiento de movimientos IMSS
    - Manejo de créditos Infonavit
    - Validación y transformación de datos

- **Interfaces**: Scripts de carga y manipulación
  - `CargaMasivaAsegurados`: Interfaz para carga masiva desde Excel
    - Validación de datos de entrada
    - Procesamiento por lotes
    - Manejo de errores y logging
    - Generación de reportes de resultados

### 2.2 Utils
- **Herramientas de Validación**:
  - Validadores de NSS
  - Validadores de CURP
  - Validadores de RFC
  - Validadores de códigos postales
  - Validadores de fechas

- **Transformadores de Datos**:
  - Conversión de formatos de fecha
  - Normalización de nombres
  - Formateo de salarios
  - Conversión de tipos de movimientos

- **Generadores de Reportes**:
  - Reportes de altas y bajas
  - Reportes de movimientos
  - Reportes de incidencias
  - Reportes de créditos Infonavit

- **Herramientas de Análisis**:
  - Análisis de movimientos
  - Estadísticas de personal
  - Análisis de salarios
  - Reportes personalizados

## 3. Guías de Uso

### 3.1 Carga Masiva
- [Guía de Carga Masiva](README_carga_masiva.md)
- Procesos de carga para obras, movimientos, asegurados y patrones
- Validación de datos
- Manejo de errores
- Formatos soportados
- Ejemplos de uso

### 3.2 Movimientos
- [Guía de Movimientos](README_movimientos.md)
- Gestión de movimientos SUA
- Procesos de alta, baja y modificación
- Validación de movimientos
- Integración con sistemas de nómina
- Reportes de movimientos

### 3.3 Desarrollo
- [Guía de Desarrollo](README-desarrollo.md)
- Estructura del código
- Patrones de diseño
- Buenas prácticas
- Guía de contribución
- Estándares de código

## 4. Características Técnicas

### 4.1 Requisitos del Sistema
- **Requisitos Mínimos**:
  - Python 3.8 o superior
  - 4GB RAM mínimo
  - 10GB espacio en disco
  - Windows 10/11 o Linux
- **Dependencias**:
  - Listadas en requirements.txt
  - Actualizaciones automáticas
  - Compatibilidad versionada
- **Conectividad**:
  - Acceso a base de datos SUA
  - Conexión a internet para actualizaciones
  - Permisos de red configurados
- **Seguridad**:
  - Permisos de escritura en directorios de trabajo
  - Acceso controlado a recursos
  - Encriptación de datos sensibles

### 4.2 Arquitectura Técnica
- **Diseño**:
  - Arquitectura modular y escalable
  - Patrón MVC (Modelo-Vista-Controlador)
  - Separación clara de responsabilidades
- **Componentes**:
  - Core: Lógica principal del sistema
  - Interfaces: CLI y API
  - Conectores: Integración con sistemas externos
  - Utils: Herramientas de soporte
- **Flujo de Datos**:
  - Entrada de datos estandarizada
  - Procesamiento en capas
  - Validación en tiempo real
  - Salida formateada
- **Características**:
  - Interfaz de línea de comandos
  - Integración con sistemas existentes
  - Soporte para múltiples formatos
  - Sistema de logging y monitoreo
  - Manejo de errores robusto

## 5. Mantenimiento y Soporte

### 5.1 Actualizaciones
- Proceso de actualización automatizado
- Control de versiones con Git
- Documentación de cambios en [resumen_cambios.md](resumen_cambios.md)
- Plan de mantenimiento preventivo
- Actualizaciones de seguridad

### 5.2 Soporte
- Canal de comunicación: Issues en GitHub
- Proceso de reporte de errores estandarizado
- Tiempos de respuesta definidos
- Documentación de soluciones comunes
- Base de conocimientos

## 6. Seguridad

### 6.1 Protección de Datos
- Manejo seguro de información sensible
- Encriptación de datos en tránsito y en reposo
- Control de acceso basado en roles
- Auditoría de accesos
- Copias de seguridad automatizadas

### 6.2 Buenas Prácticas
- Validación estricta de datos
- Manejo robusto de errores
- Registro detallado de actividades
- Pruebas de seguridad periódicas
- Actualizaciones de seguridad

## 7. Contribución

### 7.1 Guía para Contribuidores
- Proceso de contribución documentado
- Estándares de código definidos
- Revisión de código obligatoria
- Tests requeridos para nuevas funcionalidades
- Documentación actualizada

### 7.2 Desarrollo de Nuevas Características
- Proceso de propuesta formalizado
- Evaluación de viabilidad técnica
- Planificación de implementación
- Pruebas y validación
- Documentación y entrenamiento

## 2. Avances Recientes

### 2.1 Carga Masiva de Patrones
Se ha implementado y mejorado la funcionalidad de carga masiva de patrones, incluyendo:

#### 2.1.1 Manejo de Primas RT
- Implementación de validación de tipos de datos para la inserción de primas RT
- Formato estandarizado para los meses (primera letra mayúscula, resto minúsculas)
- Validaciones de datos:
  - Registro patronal no vacío
  - Año entre 1900 y 2100
  - Mes válido (1-12)
  - Prima RT no negativa
- Uso de parámetros preparados para prevenir inyección SQL
- Manejo de errores mejorado con mensajes descriptivos

#### 2.1.2 Estructura de Datos
La tabla Prima_RT contiene los siguientes campos:
- `Reg_Pat`: Registro patronal (texto)
- `Ano`: Año (numérico)
- `Mes`: Mes (texto en español, formato capitalizado)
- `Prima_Rt`: Factor de prima (decimal)
- `ValMes`: Número de mes (1-12)

#### 2.1.3 Mejoras en la Robustez
- Validación de datos antes de la inserción
- Verificación de duplicados
- Manejo de transacciones con rollback en caso de error
- Logging detallado para depuración 