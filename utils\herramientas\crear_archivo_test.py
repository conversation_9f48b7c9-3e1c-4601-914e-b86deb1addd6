import pandas as pd
import os
from datetime import datetime, timedelta

def crear_archivo_test():
    """Crea un archivo Excel de prueba con diferentes escenarios para la carga masiva de movimientos."""
    
    # Fecha base para los movimientos
    fecha_base = datetime.now()
    
    # Crear directorio si no existe
    if not os.path.exists('pruebas'):
        os.makedirs('pruebas')
    
    # Lista de movimientos de prueba
    movimientos = [
        # Escenario 1: Un reingreso válido para un empleado que estaba dado de baja
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": fecha_base.strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "08",  # Reingreso
            "SALARIO": 450.00,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Prueba de reingreso"
        },
        
        # Escenario 2: Modificación salarial para un empleado activo
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": (fecha_base + timedelta(days=1)).strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "07",  # Modificación salarial
            "SALARIO": 500.00,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Aumento de salario"
        },
        
        # Escenario 3: Baja válida para un empleado activo
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": (fecha_base + timedelta(days=2)).strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "02",  # Baja
            "SALARIO": None,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Baja normal"
        },
        
        # Escenario 4: Intento de baja para un empleado que ya está dado de baja (esta debería fallar)
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": (fecha_base + timedelta(days=3)).strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "02",  # Baja (debería fallar)
            "SALARIO": None,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Esta baja debería fallar porque el empleado ya está dado de baja"
        },
        
        # Escenario 5: Reingreso después de la baja
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": (fecha_base + timedelta(days=4)).strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "08",  # Reingreso
            "SALARIO": 550.00,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Reingreso después de baja"
        },
        
        # Escenario 6: Baja válida después del reingreso
        {
            "REG_PATR": "E5352621109",
            "NUM_AFIL": "04088625456",
            "NOMBRE": "ESTE EMPLEADO ESCRITO",
            "FECHA_MOVIMIENTO": (fecha_base + timedelta(days=5)).strftime("%Y-%m-%d"),
            "TIPO_MOVIMIENTO": "02",  # Baja
            "SALARIO": None,
            "TIPO_DESCUENTO": None,
            "VALOR_DESCUENTO": None,
            "NUMERO_CREDITO": None,
            "OBSERVACIONES": "Baja después de reingreso"
        }
    ]
    
    # Crear DataFrame
    df = pd.DataFrame(movimientos)
    
    # Guardar como Excel
    ruta_archivo = os.path.join('pruebas', 'test_bajas_reingresos.xlsx')
    df.to_excel(ruta_archivo, index=False)
    
    print(f"Archivo de prueba creado en: {os.path.abspath(ruta_archivo)}")
    return ruta_archivo

if __name__ == "__main__":
    crear_archivo_test() 