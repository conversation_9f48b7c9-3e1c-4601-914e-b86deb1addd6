"""
Script para probar el procesamiento de suspensión y reinicio de créditos con fechas más recientes
"""

from conectores.conector_sua import ConectorSUA
import traceback
import pandas as pd
from datetime import datetime, timedelta

def main():
    # Crear una instancia del conector
    conector = ConectorSUA(password="S5@N52V49")
    
    # Conectar a la base de datos
    if not conector.conectar():
        print("Error al conectar a la base de datos SUA")
        return
    
    # Usar fechas más recientes
    fecha_base = datetime.now()
    fecha_suspension = (fecha_base + timedelta(days=10)).strftime('%Y-%m-%d')
    fecha_reinicio = (fecha_base + timedelta(days=20)).strftime('%Y-%m-%d')
    
    print(f"Fechas de prueba: Suspensión={fecha_suspension}, Reinicio={fecha_reinicio}")
    
    # Probar suspensión de crédito
    print("\nProbando suspensión de crédito...")
    try:
        datos_suspension = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'FEC_INIC': fecha_suspension,
            'TIP_MOVS': '16'  # Suspensión de crédito
        }
        
        resultado = conector.procesar_movimiento(datos_suspension)
        if resultado:
            print("Suspensión procesada correctamente")
        else:
            print("Error al procesar suspensión")
    
    except Exception as e:
        print(f"Error al procesar suspensión: {str(e)}")
        traceback.print_exc()
    
    # Verificar la actualización en Asegura después de la suspensión
    print("\nVerificando datos en Asegura después de la suspensión...")
    try:
        cursor = conector.conn.cursor()
        query = f"SELECT FEC_DSC FROM Asegura WHERE REG_PATR = 'E5352621109' AND NUM_AFIL = '04088625456'"
        cursor.execute(query)
        fec_dsc = cursor.fetchone()[0]
        print(f"FEC_DSC después de suspensión: {fec_dsc}")
    except Exception as e:
        print(f"Error al verificar datos: {str(e)}")
    
    # Probar reinicio de crédito
    print("\nProbando reinicio de crédito...")
    try:
        datos_reinicio = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'FEC_INIC': fecha_reinicio,
            'TIP_MOVS': '17'  # Reinicio de crédito
        }
        
        resultado = conector.procesar_movimiento(datos_reinicio)
        if resultado:
            print("Reinicio procesado correctamente")
        else:
            print("Error al procesar reinicio")
    
    except Exception as e:
        print(f"Error al procesar reinicio: {str(e)}")
        traceback.print_exc()
    
    # Verificar la actualización en Asegura después del reinicio
    print("\nVerificando datos en Asegura después del reinicio...")
    try:
        cursor = conector.conn.cursor()
        query = f"SELECT FEC_DSC FROM Asegura WHERE REG_PATR = 'E5352621109' AND NUM_AFIL = '04088625456'"
        cursor.execute(query)
        fec_dsc = cursor.fetchone()[0]
        if fec_dsc is not None:
            print(f"✅ FEC_DSC después de reinicio (se mantiene): {fec_dsc}")
        else:
            print("❌ ERROR: FEC_DSC es NULL después del reinicio")
    except Exception as e:
        print(f"Error al verificar datos: {str(e)}")
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 