"""
Script para probar los movimientos de modificación de crédito Infonavit
"""

from conectores.conector_sua import ConectorSUA
from datetime import datetime, timedelta

def probar_movimientos():
    try:
        # Inicializar conexión con contraseña
        sua = ConectorSUA(password="S5@N52V49")
        if not sua.conectar():
            print("❌ Error al conectar a la base de datos")
            return False
            
        # Fecha base para pruebas (hoy)
        fecha_base = datetime.now()
        
        # Datos base para pruebas
        datos_base = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
        }
        
        # Probar movimiento tipo 18 (Modificación tipo descuento)
        datos_mov_18 = datos_base.copy()
        datos_mov_18.update({
            'TIP_MOVS': '18',
            'FEC_INIC': (fecha_base + timedelta(days=1)).strftime('%Y-%m-%d'),
            'Tip_Des': 'Cuota Fija',
            'Val_Des': 1250.50
        })
        print("\n🔍 Probando movimiento tipo 18 (Modificación tipo descuento)...")
        resultado_18 = sua.procesar_movimiento(datos_mov_18)
        print(f"Resultado tipo 18: {'✅ OK' if resultado_18 else '❌ Error'}")
        
        # Probar movimiento tipo 19 (Modificación valor descuento)
        datos_mov_19 = datos_base.copy()
        datos_mov_19.update({
            'TIP_MOVS': '19',
            'FEC_INIC': (fecha_base + timedelta(days=2)).strftime('%Y-%m-%d'),
            'Val_Des': 1500.75
        })
        print("\n🔍 Probando movimiento tipo 19 (Modificación valor descuento)...")
        resultado_19 = sua.procesar_movimiento(datos_mov_19)
        print(f"Resultado tipo 19: {'✅ OK' if resultado_19 else '❌ Error'}")
        
        # Probar movimiento tipo 20 (Modificación número crédito)
        datos_mov_20 = datos_base.copy()
        datos_mov_20.update({
            'TIP_MOVS': '20',
            'FEC_INIC': (fecha_base + timedelta(days=3)).strftime('%Y-%m-%d'),
            'Num_Cre': '9876543210'
        })
        print("\n🔍 Probando movimiento tipo 20 (Modificación número crédito)...")
        resultado_20 = sua.procesar_movimiento(datos_mov_20)
        print(f"Resultado tipo 20: {'✅ OK' if resultado_20 else '❌ Error'}")
        
        # Desconectar
        sua.desconectar()
        
        return all([resultado_18, resultado_19, resultado_20])
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 Iniciando pruebas de movimientos...")
    resultado = probar_movimientos()
    print(f"\nResultado final: {'✅ Todas las pruebas exitosas' if resultado else '❌ Hubo errores'}") 