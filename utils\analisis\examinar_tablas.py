import pyodbc
import pandas as pd

def conectar_bd():
    """Establece conexión con la base de datos SUA"""
    try:
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"Error al conectar: {str(e)}")
        return None

def mostrar_estructura_tabla(conn, nombre_tabla):
    """Muestra la estructura y algunos datos de la tabla"""
    try:
        cursor = conn.cursor()
        print(f"\n{'='*80}")
        print(f"ESTRUCTURA DE LA TABLA {nombre_tabla}")
        print(f"{'='*80}")
        
        # Mostrar estructura
        cursor.execute(f"SELECT TOP 1 * FROM {nombre_tabla}")
        print("\nColumnas:")
        for col in cursor.description:
            nombre = col[0]
            tipo = col[1].__name__
            tamano = col[3]
            nulos = "Permite NULL" if col[6] else "No NULL"
            print(f"- {nombre:<15} | Tipo: {tipo:<10} | Tamaño: {tamano:<4} | {nulos}")
        
        # Mostrar algunos datos
        print("\nDatos de ejemplo:")
        cursor.execute(f"SELECT TOP 5 * FROM {nombre_tabla} ORDER BY Fec_Ini DESC")
        for row in cursor.fetchall():
            print(row)
                
    except Exception as e:
        print(f"Error al examinar tabla {nombre_tabla}: {str(e)}")

def main():
    conn = conectar_bd()
    if conn:
        try:
            # Mostrar estructura de UMA
            mostrar_estructura_tabla(conn, "UMA")
            
        finally:
            conn.close()

if __name__ == "__main__":
    main() 