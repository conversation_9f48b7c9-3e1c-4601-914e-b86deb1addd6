"""
Ejemplo de uso de los modelos Asegurado y Movimiento.
Este script muestra cómo utilizar las clases y repositorios definidos
para acceder y manipular los datos de las tablas Asegurado y Movimiento.
"""

import os
import sys
from pathlib import Path
import pandas as pd
import datetime
import getpass

# Agregar directorio raíz al path
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)

# Importar las clases necesarias
from conectores.conector_sua import ConectorSUA
from modelos.asegurado import Asegurado, AseguradoRepository
from modelos.movimiento import Movimiento, MovimientoRepository

def crear_directorio_salida():
    """Crea un directorio para los archivos de salida"""
    output_dir = Path(script_dir) / "salida"
    output_dir.mkdir(exist_ok=True)
    return output_dir

def guardar_dataframe(df, nombre_archivo, directorio):
    """Guarda un DataFrame en un archivo CSV"""
    if df.empty:
        print(f"No hay datos para guardar en {nombre_archivo}")
        return None
    
    ruta_completa = directorio / f"{nombre_archivo}.csv"
    df.to_csv(ruta_completa, index=False, encoding='utf-8-sig')
    print(f"Archivo guardado: {ruta_completa}")
    return ruta_completa

def solicitar_credenciales():
    """Solicita al usuario la ruta y contraseña de la base de datos"""
    print("\n=== CONFIGURACIÓN DE LA BASE DE DATOS ===")
    
    # Solicitar ruta de la base de datos
    ruta_predeterminada = r'C:\Cobranza\SUA\SUA.MDB'
    ruta_input = input(f"Ruta a la base de datos SUA [Enter para usar {ruta_predeterminada}]: ")
    ruta_bd = ruta_input.strip() if ruta_input.strip() else ruta_predeterminada
    
    # Verificar si la base existe
    if not os.path.exists(ruta_bd):
        print(f"¡Advertencia! La ruta {ruta_bd} no existe.")
        opciones = input("¿Desea continuar de todos modos? (s/n): ").lower()
        if opciones != 's':
            print("Operación cancelada.")
            sys.exit(0)
    
    # Usar contraseña fija
    password = "S5@N52V49"
    print("Usando contraseña predeterminada para la base de datos.")
    
    return ruta_bd, password

def ejemplo_asegurado(conector=None):
    """Ejemplo de uso del repositorio de Asegurado"""
    print("\n=== EJEMPLO DE USO DEL REPOSITORIO DE ASEGURADO ===\n")
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return None, None
    
    # Crear repositorio con el conector
    asegurado_repo = AseguradoRepository(conector)
    
    # Obtener todos los asegurados (limitado a 10)
    print("\nObteniendo lista de asegurados:")
    asegurados = asegurado_repo.obtener_todos(limite=10)
    print(f"Se encontraron {len(asegurados)} asegurados")
    
    if asegurados:
        # Mostrar detalles del primer asegurado
        print("\nDetalles del primer asegurado:")
        primer_asegurado = asegurados[0]
        for campo, valor in primer_asegurado.to_dict().items():
            print(f"  {campo}: {valor}")
        
        # Buscar asegurados por criterio
        if hasattr(primer_asegurado, 'nombre'):
            nombre_busqueda = primer_asegurado.nombre[:5]  # Usar primeras 5 letras como ejemplo
            print(f"\nBuscando asegurados con nombre que contiene '{nombre_busqueda}':")
            asegurados_buscados = asegurado_repo.buscar(nombre=nombre_busqueda)
            print(f"Se encontraron {len(asegurados_buscados)} asegurados coincidentes")
        
        # Contar total de asegurados
        total_asegurados = asegurado_repo.contar()
        print(f"\nTotal de asegurados en la base de datos: {total_asegurados}")
        
        # Convertir a DataFrame para análisis
        df_asegurados = pd.DataFrame([a.to_dict() for a in asegurados])
        
        # Guardar resultados
        output_dir = crear_directorio_salida()
        guardar_dataframe(df_asegurados, "asegurados_muestra", output_dir)
        
        # Retornar el NSS para uso en otros ejemplos
        return primer_asegurado.nss if hasattr(primer_asegurado, 'nss') else None, conector
    
    return None, conector

def ejemplo_movimiento(nss=None, conector=None):
    """Ejemplo de uso del repositorio de Movimiento"""
    print("\n=== EJEMPLO DE USO DEL REPOSITORIO DE MOVIMIENTO ===\n")
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return
    
    # Crear repositorio
    movimiento_repo = MovimientoRepository(conector)
    
    # Obtener todos los movimientos (limitado a 10)
    print("\nObteniendo lista de movimientos:")
    movimientos = movimiento_repo.obtener_todos(limite=10)
    print(f"Se encontraron {len(movimientos)} movimientos")
    
    if not movimientos:
        print("No se encontraron movimientos para analizar")
        return
    
    # Si no se proporciona un NSS, usar el del primer registro
    if not nss and hasattr(movimientos[0], 'nss'):
        nss = movimientos[0].nss
        
    if nss:
        # Obtener movimientos de un asegurado específico
        print(f"\nObtener movimientos para el NSS '{nss}':")
        movimientos_asegurado = movimiento_repo.obtener_por_nss(nss)
        print(f"Se encontraron {len(movimientos_asegurado)} movimientos asociados a este NSS")
        
        # Ordenar por fecha
        if movimientos_asegurado and hasattr(movimientos_asegurado[0], 'fecha_movimiento'):
            movimientos_asegurado.sort(key=lambda m: m.fecha_movimiento)
            print("\nMovimientos ordenados por fecha:")
            for movimiento in movimientos_asegurado[:5]:  # Mostrar solo los primeros 5
                print(f"  {movimiento.fecha_movimiento}: {movimiento.tipo_movimiento}")
        
        # Convertir a DataFrame y guardar
        if movimientos_asegurado:
            df_movimientos_asegurado = pd.DataFrame([m.to_dict() for m in movimientos_asegurado])
            output_dir = crear_directorio_salida()
            guardar_dataframe(df_movimientos_asegurado, f"movimientos_{nss}", output_dir)
    
    # Convertir a DataFrame para análisis adicional
    df_movimientos = pd.DataFrame([m.to_dict() for m in movimientos])
    
    # Guardar resultados
    output_dir = crear_directorio_salida()
    guardar_dataframe(df_movimientos, "movimientos_muestra", output_dir)

def ejemplo_analisis_combinado(nss=None, conector=None):
    """Ejemplo de análisis combinado de Asegurado y Movimiento"""
    print("\n=== EJEMPLO DE ANÁLISIS COMBINADO ===\n")
    
    if not nss:
        print("No se proporcionó NSS para el análisis combinado")
        return
    
    # Crear conector si no se proporciona
    if conector is None:
        # Solicitar credenciales
        ruta_bd, password = solicitar_credenciales()
        conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
        
        # Verificar conexión
        if not conector.conectar():
            print("Reintentando con la contraseña predeterminada...")
            conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
            if not conector.conectar():
                print("No se pudo establecer conexión con la base de datos. Abortando.")
                return
    
    # Crear repositorios
    asegurado_repo = AseguradoRepository(conector)
    movimiento_repo = MovimientoRepository(conector)
    
    # Obtener datos del asegurado
    asegurado = asegurado_repo.obtener_por_nss(nss)
    if not asegurado:
        print(f"No se encontró asegurado con NSS {nss}")
        return
    
    # Obtener historial de movimientos
    movimientos = movimiento_repo.obtener_por_nss(nss)
    if not movimientos:
        print(f"No se encontraron movimientos para el NSS {nss}")
        return
    
    # Convertir a DataFrame para análisis
    df_movimientos = pd.DataFrame([m.to_dict() for m in movimientos])
    
    # Realizar análisis combinado
    print(f"\nAnálisis de movimientos para el asegurado: {asegurado.nombre if hasattr(asegurado, 'nombre') else nss}")
    
    # Ejemplo: Análisis de tipos de movimiento
    if 'tipo_movimiento' in df_movimientos.columns:
        print("\nDistribución de tipos de movimiento:")
        tipos_count = df_movimientos['tipo_movimiento'].value_counts()
        for tipo, count in tipos_count.items():
            print(f"  {tipo}: {count}")
    
    # Ejemplo: Analizar salarios base de cotización a lo largo del tiempo
    if 'fecha_movimiento' in df_movimientos.columns and 'salario_base' in df_movimientos.columns:
        print("\nEvolución del salario base de cotización:")
        df_movimientos = df_movimientos.sort_values('fecha_movimiento')
        df_salarios = df_movimientos[['fecha_movimiento', 'salario_base']].dropna()
        
        if not df_salarios.empty:
            print(df_salarios.to_string(index=False))
    
    # Guardar resultados
    output_dir = crear_directorio_salida()
    guardar_dataframe(df_movimientos, f"analisis_asegurado_{nss}", output_dir)

def main():
    """Función principal del ejemplo"""
    print("EJEMPLO DE USO DE LOS MODELOS ASEGURADO Y MOVIMIENTO\n")
    
    # Solicitar credenciales una sola vez
    ruta_bd, password = solicitar_credenciales()
    conector = ConectorSUA(ruta_bd=ruta_bd, password=password)
    
    # Verificar conexión
    if not conector.conectar():
        print("Reintentando con la contraseña predeterminada...")
        conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
        if not conector.conectar():
            print("No se pudo establecer conexión con la base de datos. Abortando.")
            sys.exit(1)
    
    try:
        # Ejecutar ejemplo de Asegurado
        nss, conector = ejemplo_asegurado(conector)
        
        # Ejecutar ejemplo de Movimiento
        ejemplo_movimiento(nss, conector)
        
        # Ejecutar análisis combinado
        if nss:
            ejemplo_analisis_combinado(nss, conector)
        
    except Exception as e:
        print(f"Error durante la ejecución del ejemplo: {e}")
    
    finally:
        # Cerrar conexión
        if conector and conector.conn:
            conector.desconectar()
    
    print("\nEjemplo finalizado.")

if __name__ == "__main__":
    main() 