import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import pyodbc
from datetime import datetime
import os
import logging
from conectores.conector_sua import ConectorSUA

class CargaMasivaObrasApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Carga Masiva de Obras")
        self.root.geometry("800x600")
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('carga_obras.log'),
                logging.StreamHandler()
            ]
        )
        
        # Crear conector SUA
        self.conector = ConectorSUA(password="S5@N52V49")
        if not self.conector.conectar():
            messagebox.showerror("Error", "No se pudo conectar a la base de datos SUA")
            return
        
        # Crear widgets
        self.crear_widgets()
        
    def crear_widgets(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Botón para cargar archivo
        ttk.Button(main_frame, text="Cargar Archivo Excel", command=self.cargar_archivo).grid(row=0, column=0, pady=10)
        
        # Botón para generar plantilla
        ttk.Button(main_frame, text="Generar Plantilla", command=self.generar_plantilla).grid(row=0, column=1, pady=10)
        
        # Área de texto para logs
        self.text_log = tk.Text(main_frame, height=20, width=80)
        self.text_log.grid(row=1, column=0, columnspan=2, pady=10)
        
        # Scrollbar para el área de texto
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.text_log.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.text_log['yscrollcommand'] = scrollbar.set
        
    def log(self, mensaje, error=False):
        """Registra un mensaje en el log y en el área de texto"""
        if error:
            logging.error(mensaje)
            self.text_log.insert(tk.END, f"ERROR: {mensaje}\n", "error")
        else:
            logging.info(mensaje)
            self.text_log.insert(tk.END, f"{mensaje}\n")
        self.text_log.see(tk.END)
        
    def cargar_archivo(self):
        """Carga un archivo Excel para procesar"""
        archivo = filedialog.askopenfilename(
            title="Seleccionar archivo Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        
        if not archivo:
            return
            
        try:
            # Leer archivo Excel
            df = pd.read_excel(archivo)
            
            # Validar estructura
            if not self.validar_excel(df):
                return
                
            # Procesar registros
            self.procesar_registros(df)
            
        except Exception as e:
            self.log(f"Error al procesar archivo: {str(e)}", error=True)
            
    def validar_excel(self, df):
        """Valida la estructura del archivo Excel"""
        columnas_requeridas = ['Reg_Patr', 'Num_Afil', 'NOMBRE', 'Fec_Inic', 'Fec_Fin', 'Fol_Inc']
        
        # Verificar columnas
        for col in columnas_requeridas:
            if col not in df.columns:
                self.log(f"Error: Falta la columna {col}", error=True)
                return False
                
        # Verificar tipos de datos
        try:
            df['Fec_Inic'] = pd.to_datetime(df['Fec_Inic'])
            df['Fec_Fin'] = pd.to_datetime(df['Fec_Fin'])
            
            # Verificar que fecha fin no sea menor que fecha inicio
            if any(df['Fec_Fin'] < df['Fec_Inic']):
                self.log("Error: Existen registros donde la fecha fin es menor que la fecha inicio", error=True)
                return False
                
        except Exception as e:
            self.log(f"Error en formato de fechas: {str(e)}", error=True)
            return False
            
        return True
        
    def verificar_empalme_fechas(self, num_afil, fec_inic, fec_fin, fol_inc):
        """Verifica si hay empalme de fechas para el mismo trabajador"""
        try:
            cursor = self.conector.conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) 
                FROM MovtosObra 
                WHERE NUM_AFIL = ? 
                AND Fol_Inc != ?
                AND (
                    (FEC_INIC BETWEEN ? AND ?) OR
                    (FEC_FIN BETWEEN ? AND ?) OR
                    (? BETWEEN FEC_INIC AND FEC_FIN) OR
                    (? BETWEEN FEC_INIC AND FEC_FIN)
                )
            """, (
                num_afil, fol_inc,
                fec_inic, fec_fin,
                fec_inic, fec_fin,
                fec_inic, fec_fin
            ))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            self.log(f"Error al verificar empalme de fechas: {str(e)}", error=True)
            return False

    def procesar_registros(self, df):
        """Procesa los registros del archivo Excel"""
        total = len(df)
        exitosos = 0
        errores = 0
        
        for idx, row in df.iterrows():
            try:
                num_afil = str(row['Num_Afil']).zfill(11)
                fec_inic = row['Fec_Inic'].strftime('%Y-%m-%d')
                fec_fin = row['Fec_Fin'].strftime('%Y-%m-%d')
                
                # Verificar empalme de fechas
                if self.verificar_empalme_fechas(num_afil, fec_inic, fec_fin, row['Fol_Inc']):
                    if not messagebox.askyesno("Empalme de Fechas", 
                        f"Existe un empalme de fechas para el trabajador {num_afil}. ¿Desea continuar?"):
                        continue
                
                # Verificar si ya existe registro con el mismo Fol_Inc
                if self.verificar_duplicado(row['Fol_Inc']):
                    if not messagebox.askyesno("Registro Duplicado", 
                        f"Ya existe un registro con el Fol_Inc {row['Fol_Inc']}. ¿Desea actualizarlo?"):
                        continue
                
                # Insertar o actualizar registro
                if self.insertar_registro(row):
                    exitosos += 1
                else:
                    errores += 1
                    
            except Exception as e:
                self.log(f"Error al procesar registro {idx+1}: {str(e)}", error=True)
                errores += 1
                
        self.log(f"\nProceso completado. Total: {total}, Exitosos: {exitosos}, Errores: {errores}")
        
    def verificar_duplicado(self, fol_inc):
        """Verifica si ya existe un registro con el mismo Fol_Inc"""
        try:
            cursor = self.conector.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM MovtosObra WHERE Fol_Inc = ?", (fol_inc,))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            self.log(f"Error al verificar duplicado: {str(e)}", error=True)
            return False
            
    def insertar_registro(self, row):
        """Inserta o actualiza un registro en la tabla MovtosObra"""
        try:
            cursor = self.conector.conn.cursor()
            
            # Preparar datos
            datos = {
                'REG_PATR': row['Reg_Patr'],
                'NUM_AFIL': str(row['Num_Afil']).zfill(11),
                'TIP_MOVS': '30',  # Siempre es 30 para obras
                'FEC_INIC': row['Fec_Inic'].strftime('%Y-%m-%d'),
                'FEC_FIN': row['Fec_Fin'].strftime('%Y-%m-%d'),
                'FOL_INC': row['Fol_Inc']
            }
            
            # Verificar si ya existe un registro con el mismo Fol_Inc
            cursor.execute("SELECT COUNT(*) FROM MovtosObra WHERE Fol_Inc = ?", (datos['FOL_INC'],))
            existe = cursor.fetchone()[0] > 0
            
            if existe:
                # Actualizar registro existente
                cursor.execute("""
                    UPDATE MovtosObra 
                    SET REG_PATR = ?, 
                        NUM_AFIL = ?, 
                        TIP_MOVS = ?, 
                        FEC_INIC = ?, 
                        FEC_FIN = ?
                    WHERE Fol_Inc = ?
                """, (
                    datos['REG_PATR'],
                    datos['NUM_AFIL'],
                    datos['TIP_MOVS'],
                    datos['FEC_INIC'],
                    datos['FEC_FIN'],
                    datos['FOL_INC']
                ))
                self.log(f"Registro actualizado exitosamente: {datos['FOL_INC']}")
            else:
                # Insertar nuevo registro
                cursor.execute("""
                    INSERT INTO MovtosObra (
                        REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, FEC_FIN, FOL_INC
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    datos['REG_PATR'],
                    datos['NUM_AFIL'],
                    datos['TIP_MOVS'],
                    datos['FEC_INIC'],
                    datos['FEC_FIN'],
                    datos['FOL_INC']
                ))
                self.log(f"Registro insertado exitosamente: {datos['FOL_INC']}")
            
            self.conector.conn.commit()
            return True
            
        except Exception as e:
            self.log(f"Error al procesar registro: {str(e)}", error=True)
            return False
            
    def generar_plantilla(self):
        """Genera una plantilla Excel con la estructura requerida"""
        try:
            # Crear DataFrame con estructura
            df = pd.DataFrame(columns=[
                'Reg_Patr',
                'Num_Afil',
                'NOMBRE',
                'Fec_Inic',
                'Fec_Fin',
                'Fol_Inc'
            ])
            
            # Agregar ejemplo
            df.loc[0] = [
                'E5352621109',
                '04088625456',
                'ESTE EMPLEADO ESCRITO',
                '01/01/2025',
                '31/05/2025',
                'C0032445'
            ]
            
            # Guardar archivo
            archivo = filedialog.asksaveasfilename(
                title="Guardar plantilla",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if archivo:
                df.to_excel(archivo, index=False)
                self.log(f"Plantilla generada exitosamente: {archivo}")
                
        except Exception as e:
            self.log(f"Error al generar plantilla: {str(e)}", error=True)

if __name__ == "__main__":
    root = tk.Tk()
    app = CargaMasivaObrasApp(root)
    root.mainloop() 