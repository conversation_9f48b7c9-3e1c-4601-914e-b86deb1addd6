import pyodbc

def main():
    try:
        # Establecer conexión
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            r'DBQ=C:\Cobranza\SUA\SUA.MDB;'
            r'PWD=S5@N52V49;'
        )
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa a la base de datos")
        
        # Consulta simple
        cursor = conn.cursor()
        
        # Probar SELECT con parámetros
        reg_patr = "E5352621109"
        num_afil = "04088625456"
        
        query = "SELECT * FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
        cursor.execute(query, (reg_patr, num_afil))
        
        row = cursor.fetchone()
        if row:
            print("Asegurado encontrado")
            for i, column in enumerate(cursor.description):
                print(f"{column[0]}: {row[i]}")
        else:
            print("Asegurado no encontrado")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 