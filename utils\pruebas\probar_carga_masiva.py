from carga_masiva_movimientos import CargaMasivaMovimientos
import tkinter as tk
import pandas as pd
import os
from datetime import datetime

def crear_datos_prueba():
    """Crea un archivo Excel de prueba con algunos movimientos"""
    try:
        # Crear directorio de prueba si no existe
        os.makedirs('pruebas', exist_ok=True)
        
        # Datos de prueba para baja
        datos = [
            {
                "REG_PATR": "E5352621109",
                "NUM_AFIL": "04088625456",
                "NOMBRE": "ESTE EMPLEADO ESCRITO",
                "FECHA_MOVIMIENTO": datetime.now().strftime("%Y-%m-%d"),
                "TIPO_MOVIMIENTO": "02",  # Baja
                "SALARIO": None,
                "TIPO_DESCUENTO": None,
                "VALOR_DESCUENTO": None,
                "NUMERO_CREDITO": None,
                "OBSERVACIONES": "Prueba de baja automática"
            },
        ]
        
        # Crear DataFrame y guardar en Excel
        df = pd.DataFrame(datos)
        archivo_excel = os.path.join('pruebas', 'movimientos_prueba.xlsx')
        df.to_excel(archivo_excel, index=False)
        
        print(f"Archivo de prueba creado en: {os.path.abspath(archivo_excel)}")
        return archivo_excel
    
    except Exception as e:
        print(f"Error al crear archivo de prueba: {e}")
        return None

def probar_carga_masiva():
    """Prueba la funcionalidad de carga masiva de movimientos"""
    try:
        # Crear archivo de prueba
        archivo_excel = crear_datos_prueba()
        if not archivo_excel:
            print("No se pudo crear el archivo de prueba")
            return False
        
        # Iniciar la aplicación tkinter
        root = tk.Tk()
        root.withdraw()  # Ocultar la ventana principal
        
        # Crear instancia de la clase
        app = CargaMasivaMovimientos(root)
        
        # Configurar rutas y contraseña
        app.archivo_excel.set(archivo_excel)
        app.ruta_bd.set("C:\\Cobranza\\SUA\\SUA.MDB")
        app.password_bd.set("S5@N52V49")
        
        # Probar validación de Excel
        print("\nProbando validación de Excel...")
        if app.validar_excel():
            print("✅ Validación de Excel exitosa")
        else:
            print("❌ Error en validación de Excel")
            return False
        
        # Probar carga de movimientos
        print("\nProbando carga de movimientos...")
        app.cargar_movimientos()
        
        # Cerrar la aplicación
        root.destroy()
        
        print("\nPrueba completada")
        return True
    
    except Exception as e:
        print(f"Error en prueba: {e}")
        return False

if __name__ == "__main__":
    probar_carga_masiva() 