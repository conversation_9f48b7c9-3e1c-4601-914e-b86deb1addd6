"""
Script para probar la carga masiva de patrones de forma automatizada.
Este script ejecuta una prueba de inserción de patrones sin interfaz gráfica.
"""

import os
import sys
import pandas as pd
import datetime
from pathlib import Path
import random
import string
import time

# Importar el conector y constantes necesarias
from conectores.conector_sua import ConectorSUA
from carga_masiva_patrones import MESES

def crear_excel_prueba(ruta_salida):
    """Crea un archivo Excel de prueba con datos de patrones"""
    # Generar registros patronales aleatorios (para evitar duplicados)
    import random
    import string
    import time
    
    # Función para generar un registro patronal aleatorio
    def generar_registro_patronal():
        letra = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
        numeros = ''.join(random.choices(string.digits, k=10))
        return f"{letra}{numeros}"
    
    # Usar timestamp para asegurar que sean únicos
    timestamp = int(time.time())
    reg1 = generar_registro_patronal() + str(timestamp)[-3:]
    reg2 = generar_registro_patronal() + str(timestamp)[-3:]
    reg3 = generar_registro_patronal() + str(timestamp)[-3:]
    
    # Asegurarse de que tengan sólo 11 caracteres (formato requerido)
    reg1 = reg1[:11]
    reg2 = reg2[:11]
    reg3 = reg3[:11]
    
    # Imprimir registros generados para referencia
    print(f"Registros patronales generados: {reg1}, {reg2}, {reg3}")
    
    # Crear un DataFrame con datos de prueba
    datos = [
        {
            "Registro Patronal": reg1,
            "RFC": "TEST010101ABC",
            "Razon Social": "EMPRESA DE PRUEBA S.A. DE C.V.",
            "Actividad Económica": "SERVICIOS DE PRUEBA",
            "Domicilio": "CALLE PRUEBA #123",
            "Municipio": "ZAPOPAN",
            "Código Postal": "45040",
            "Entidad Federativa": "JALISCO",
            "Teléfono": "3312345678",
            "Rembolso de Subsidios": False,
            "Zona Salario": "A",
            "Subdelegacion": "ZAPOPAN",
            "Fecha Prima de RT": pd.to_datetime("2025-01-01"),
            "Representante Legal": "JUAN PÉREZ LÓPEZ",
            "Clase": "I",
            "Fracción": "43",
            "STyPS": "No",
            "Factor Prima de RT": 0.5
        },
        # Registro con patrón diferente
        {
            "Registro Patronal": reg2, 
            "RFC": "TEST020202XYZ",
            "Razon Social": "OTRA EMPRESA DE PRUEBA S.A. DE C.V.",
            "Actividad Económica": "SERVICIOS DE CONSULTORÍA",
            "Domicilio": "CALLE EJEMPLO #456",
            "Municipio": "GUADALAJARA",
            "Código Postal": "44100",
            "Entidad Federativa": "JALISCO",
            "Teléfono": "3399887766",
            "Rembolso de Subsidios": True,
            "Zona Salario": "A",
            "Subdelegacion": "JUAREZ",
            "Fecha Prima de RT": pd.to_datetime("2025-02-01"),
            "Representante Legal": "MARÍA RODRÍGUEZ SÁNCHEZ",
            "Clase": "II",
            "Fracción": "56",
            "STyPS": "No",
            "Factor Prima de RT": 0.75
        },
        # Otro registro diferente
        {
            "Registro Patronal": reg3,
            "RFC": "TEST030303ABC",
            "Razon Social": "TERCERA EMPRESA DE PRUEBA S.A. DE C.V.",
            "Actividad Económica": "COMERCIO",
            "Domicilio": "AVENIDA PRUEBA #789",
            "Municipio": "TLAQUEPAQUE",
            "Código Postal": "45570",
            "Entidad Federativa": "JALISCO",
            "Teléfono": "3322114455",
            "Rembolso de Subsidios": False,
            "Zona Salario": "A",
            "Subdelegacion": "TLAQUEPAQUE",
            "Fecha Prima de RT": pd.to_datetime("2025-03-01"),
            "Representante Legal": "ROBERTO GÓMEZ PEREZ",
            "Clase": "III",
            "Fracción": "20",
            "STyPS": "Sí",
            "Factor Prima de RT": 1.2
        }
    ]
    
    # Crear DataFrame
    df = pd.DataFrame(datos)
    
    # Guardar en Excel
    df.to_excel(ruta_salida, sheet_name='patron', index=False)
    print(f"Archivo de prueba creado: {ruta_salida}")
    return ruta_salida

def buscar_id_entidad(conector, nombre_entidad):
    """Busca el ID de una entidad federativa por su nombre"""
    if not nombre_entidad or pd.isna(nombre_entidad):
        return None
    
    try:
        # Escapar posibles comillas en el nombre
        nombre_escaped = nombre_entidad.replace("'", "''")
        
        query = f"""
        SELECT IdClave, Descripcion, Posicion 
        FROM Estados 
        WHERE Descripcion LIKE '%{nombre_escaped}%'
        """
        
        cursor = conector.conn.cursor()
        cursor.execute(query)
        resultado = cursor.fetchone()
        
        if resultado:
            return {
                'id': resultado.IdClave,
                'nombre': resultado.Descripcion,
                'posicion': resultado.Posicion
            }
    except Exception as e:
        print(f"Error al buscar entidad federativa: {e}")
    
    print(f"Advertencia: No se encontró la entidad federativa '{nombre_entidad}'. Usando valor por defecto.")
    # Valor por defecto para pruebas
    return {
        'id': 20,  # ID por defecto para pruebas (Jalisco)
        'nombre': nombre_entidad,
        'posicion': 1
    }

def buscar_id_subdelegacion(conector, nombre_subdelegacion):
    """Busca el ID de una subdelegación por su nombre"""
    if not nombre_subdelegacion or pd.isna(nombre_subdelegacion):
        return None
    
    try:
        # Escapar posibles comillas en el nombre
        nombre_escaped = nombre_subdelegacion.replace("'", "''")
        
        query = f"""
        SELECT IdClaveSub, Descripcion, Posicion 
        FROM Subdelega 
        WHERE Descripcion LIKE '%{nombre_escaped}%'
        """
        
        cursor = conector.conn.cursor()
        cursor.execute(query)
        resultado = cursor.fetchone()
        
        if resultado:
            return {
                'id': resultado.IdClaveSub,
                'nombre': resultado.Descripcion,
                'posicion': resultado.Posicion
            }
    except Exception as e:
        print(f"Error al buscar subdelegación: {e}")
    
    print(f"Advertencia: No se encontró la subdelegación '{nombre_subdelegacion}'. Usando valor por defecto.")
    # Valores por defecto para pruebas
    return {
        'id': 2153,  # ID por defecto para pruebas
        'nombre': nombre_subdelegacion,
        'posicion': 3
    }

def buscar_fraccion(conector, fraccion_valor):
    """
    Formatea correctamente la fracción con tres dígitos para mantener los ceros a la izquierda.
    
    Esta función toma un valor de fracción (número o texto) y garantiza que sea una cadena
    de 3 dígitos con ceros a la izquierda. Esto es necesario porque el campo Fraccion 
    en la tabla Patron es de tipo texto y queremos mantener un formato consistente.
    
    Ejemplos:
    - "43" se convierte en "043"
    - "5" se convierte en "005"
    - "123" se mantiene como "123"
    """
    if not fraccion_valor or pd.isna(fraccion_valor):
        # Valor por defecto si no hay dato
        print("Fracción no especificada, usando valor por defecto '000'")
        return "000"
    
    # Convertir a string para comparación (sin ceros a la izquierda)
    if isinstance(fraccion_valor, (int, float)):
        valor_comparacion = str(int(fraccion_valor))
    else:
        valor_comparacion = str(fraccion_valor).strip()
    
    # Formatear directamente sin buscar en la tabla
    formatted_value = valor_comparacion.zfill(3)
    print(f"Formateando fracción '{valor_comparacion}' a '{formatted_value}'")
    return formatted_value

def insertar_patron_directo(conector, datos, log_function=print):
    """Inserta un patrón directamente en la base de datos"""
    try:
        # Obtener el registro patronal
        reg_pat = str(datos.get("Registro Patronal", "")).strip()
        if not reg_pat:
            log_function("Error: El Registro Patronal es obligatorio")
            return False
            
        # Verificar si ya existe el registro patronal
        cursor = conector.conn.cursor()
        # Consulta directa evitando parámetros
        consulta_verificacion = f"SELECT COUNT(*) FROM Patron WHERE REG_PAT = '{reg_pat}'"
        cursor.execute(consulta_verificacion)
        count = cursor.fetchone()[0]
        
        if count > 0:
            log_function(f"El registro patronal {reg_pat} ya existe en la base de datos")
            return False
        
        # Convertir los datos booleanos a enteros (0/1) para Access
        if 'Rembolso de Subsidios' in datos:
            rembolso = 1 if datos.get('Rembolso de Subsidios') else 0
        else:
            rembolso = 0
        
        # Escapar comillas simples
        nombre = str(datos.get('Razon Social', '')).replace("'", "''")
        actividad = str(datos.get('Actividad Económica', '')).replace("'", "''")
        domicilio = str(datos.get('Domicilio', '')).replace("'", "''")
        municipio = str(datos.get('Municipio', '')).replace("'", "''")
        cp = str(datos.get('Código Postal', '')).replace("'", "''")
        telefono = str(datos.get('Teléfono', '')).replace("'", "''")
        zona = str(datos.get('Zona Salario', '')).replace("'", "''")
        entidad_nombre = str(datos.get('Entidad Federativa', '')).replace("'", "''")
        subdelegacion_nombre = str(datos.get('Subdelegacion', '')).replace("'", "''")
        rep_legal = str(datos.get('Representante Legal', '')).replace("'", "''")
        clase = str(datos.get('Clase', '')).replace("'", "''")
        
        # Buscar la fracción en la tabla TablaFraccion
        fraccion_valor = datos.get('Fracción', '')
        fraccion = buscar_fraccion(conector, fraccion_valor)
        
        # Escapar comillas si las hay
        fraccion = fraccion.replace("'", "''")
        
        # Mostrar el valor exacto que se enviará a la base de datos (para depuración)
        print(f"DEBUG - Valor de fracción a insertar: '{fraccion}' (tipo: {type(fraccion).__name__})")
        
        styps = str(datos.get('STyPS', '')).replace("'", "''")
        rfc = str(datos.get('RFC', '')).replace("'", "''")
        
        # Buscar ID de entidad federativa
        # ENT_PAT debe ser el IdClave de la tabla Estados SUA, relacionando la Entidad del Excel con la Descripcion de la tabla Estados
        entidad_data = buscar_id_entidad(conector, entidad_nombre)
        entidad_id = entidad_data['id']
        
        # Buscar ID de subdelegación
        # DEL_PAT debe ser el IdClaveSub de la tabla Subdelega SUA, relacionando la Subdelegacion del Excel con la Descripcion de la tabla Subdelega
        subdelegacion_data = buscar_id_subdelegacion(conector, subdelegacion_nombre)
        subdelegacion_id = subdelegacion_data['id']
        # NUM_SUB debe ser la posición numérica de la subdelegación en la tabla Subdelega
        subdelegacion_posicion = subdelegacion_data['posicion']
        
        # Formatear fecha para INI_AFIL (AAAAMM)
        fecha_afil = ""
        fecha_prima = datos.get('Fecha Prima de RT')
        if pd.notna(fecha_prima):
            if isinstance(fecha_prima, str):
                fecha_prima = pd.to_datetime(fecha_prima)
            # Formato AAAAMM para INI_AFIL
            fecha_afil = fecha_prima.strftime("%Y%m")
        
        # Preparar la consulta SQL con valores directos
        consulta_insercion = f"""
        INSERT INTO Patron (
            REG_PAT, RFC_PAT, NOM_PAT, ACT_PAT, DOM_PAT, MUN_PAT, CPP_PAT, ENT_PAT, 
            TEL_PAT, REM_PAT, ZON_PAT, DEL_PAT, CAR_ENT, NUM_DEL, CAR_DEL, NUM_SUB, 
            CAR_SUB, TIP_CON, CON_VEN, INI_AFIL, Pat_Rep, Clase, Fraccion, STyPS
        ) VALUES (
            '{reg_pat}', '{rfc}', '{nombre}', '{actividad}', '{domicilio}', '{municipio}', 
            '{cp}', {entidad_id}, '{telefono}', {rembolso}, '{zona}', {subdelegacion_id}, 
            '{entidad_nombre}', 0, '{entidad_nombre}', {subdelegacion_posicion}, 
            '{subdelegacion_nombre}', 0, NULL, '{fecha_afil}', 
            '{rep_legal}', '{clase}', CStr("{fraccion}"), '{styps}'
        )
        """
        log_function(f"Ejecutando inserción del patrón {reg_pat}...")
        cursor.execute(consulta_insercion)
        conector.conn.commit()
        return True
    except Exception as e:
        log_function(f"Error al insertar patrón: {e}")
        return False

def insertar_prima_rt_directo(conector, datos):
    """Inserta una prima RT directamente en la base de datos"""
    try:
        # Procesar la fecha
        fecha_prima = datos.get('Fecha Prima de RT')
        if pd.notna(fecha_prima):
            if isinstance(fecha_prima, str):
                fecha_prima = pd.to_datetime(fecha_prima)
            
            año = int(fecha_prima.year)
            mes_num = int(fecha_prima.month)
            mes_txt = MESES[mes_num]
        else:
            print("Error: La Fecha Prima de RT es obligatoria")
            return False
        
        # Obtener el registro patronal
        reg_pat = str(datos.get("Registro Patronal", "")).strip()
        if not reg_pat:
            print("Error: El Registro Patronal es obligatorio")
            return False
        
        # Valor de la prima (debe ser un float)
        try:
            prima_rt = float(datos.get('Factor Prima de RT', 0))
        except:
            prima_rt = 0.5
            
        # Verificar si ya existe una prima para este patrón, año y mes
        cursor = conector.conn.cursor()
        consulta_verificacion = f"SELECT COUNT(*) FROM Prima_RT WHERE Reg_Pat = '{reg_pat}'"
        cursor.execute(consulta_verificacion)
        count = cursor.fetchone()[0]
        
        if count > 0:
            # Ya existe una prima RT con ese registro patronal, verificar año y mes
            consulta_check = f"SELECT * FROM Prima_RT WHERE Reg_Pat = '{reg_pat}'"
            cursor.execute(consulta_check)
            registros = cursor.fetchall()
            
            for registro in registros:
                if str(registro.Ano) == str(año) and registro.Mes == mes_txt:
                    print(f"Ya existe una prima RT para {reg_pat} ({año}-{mes_txt})")
                    return False
        
        # Intentar inserción directa con SQL
        try:
            # Formatear los valores para Access
            consulta = f"INSERT INTO Prima_RT (Reg_Pat, Ano, Mes, Prima_Rt, ValMes) " + \
                     f"VALUES ('{reg_pat}', {año}, '{mes_txt}', {prima_rt}, {mes_num})"
            
            print(f"Ejecutando consulta: {consulta}")
            cursor.execute(consulta)
            conector.conn.commit()
            print(f"Prima RT insertada para patrón {reg_pat}")
            return True
        except Exception as e:
            print(f"Error en inserción directa: {e}")
            
            # Intentar con otra aproximación
            try:
                # Insertar usando una consulta SQL más explícita
                consulta_alt = f"""
                INSERT INTO Prima_RT 
                (Reg_Pat, Ano, Mes, Prima_Rt, ValMes) 
                SELECT 
                    '{reg_pat}' AS Reg_Pat, 
                    {año} AS Ano, 
                    '{mes_txt}' AS Mes, 
                    {prima_rt} AS Prima_Rt, 
                    {mes_num} AS ValMes
                """
                print(f"Intentando consulta alternativa: {consulta_alt}")
                cursor.execute(consulta_alt)
                conector.conn.commit()
                print(f"Prima RT insertada para patrón {reg_pat} (método alternativo)")
                return True
            except Exception as e2:
                print(f"Error en inserción alternativa: {e2}")
                
                # Última alternativa: insertar sin la columna Mes
                try:
                    consulta_final = f"""
                    INSERT INTO Prima_RT 
                    (Reg_Pat, Ano, Prima_Rt, ValMes) 
                    VALUES 
                    ('{reg_pat}', {año}, {prima_rt}, {mes_num})
                    """
                    print(f"Intentando consulta final: {consulta_final}")
                    cursor.execute(consulta_final)
                    conector.conn.commit()
                    
                    # Intentar actualizar el campo Mes después
                    try:
                        update_mes = f"""
                        UPDATE Prima_RT 
                        SET Mes = '{mes_txt}' 
                        WHERE Reg_Pat = '{reg_pat}' AND Ano = {año} AND ValMes = {mes_num}
                        """
                        cursor.execute(update_mes)
                        conector.conn.commit()
                    except:
                        print("No se pudo actualizar el campo Mes, pero la prima fue creada")
                    
                    print(f"Prima RT insertada para patrón {reg_pat} (método final)")
                    return True
                except Exception as e3:
                    print(f"Error en inserción final: {e3}")
                    return False
    except Exception as e:
        print(f"Error al insertar prima RT: {e}")
        return False

def procesar_datos(conector, ruta_excel):
    """Procesa los datos del Excel y los inserta en la base de datos"""
    print(f"\nProcesando datos desde: {ruta_excel}")
    
    # Leer Excel
    df = pd.read_excel(ruta_excel, sheet_name='patron')
    print(f"Se encontraron {len(df)} registros para procesar.")
    
    # Procesar registros
    registros_procesados = 0
    errores = []
    
    for idx, row in df.iterrows():
        print(f"\nProcesando registro {idx+1} de {len(df)}:")
        
        try:
            # Verificar registro patronal
            reg_pat = str(row.get("Registro Patronal", "")).strip()
            if not reg_pat:
                error_msg = f"Fila {idx+2}: El Registro Patronal es obligatorio"
                errores.append(error_msg)
                print(f"✗ {error_msg}")
                continue
            
            # Insertar patrón
            if insertar_patron_directo(conector, row):
                print(f"✓ Patrón {reg_pat} creado con éxito")
                
                # Insertar Prima RT si hay datos
                if pd.notna(row.get("Factor Prima de RT")):
                    if insertar_prima_rt_directo(conector, row):
                        fecha_prima = row.get("Fecha Prima de RT")
                        if pd.notna(fecha_prima):
                            if isinstance(fecha_prima, str):
                                fecha_prima = pd.to_datetime(fecha_prima)
                            año = fecha_prima.year
                            mes_num = fecha_prima.month
                            mes_txt = MESES[mes_num]
                            print(f"  ✓ Prima RT para {reg_pat} ({año}-{mes_txt}) creada con éxito")
                
                registros_procesados += 1
            else:
                # El método insertar_patron_directo ya registró el error
                errores.append(f"Fila {idx+2}: No se pudo insertar el patrón {reg_pat}")
        
        except Exception as e:
            error_msg = f"Error en fila {idx+2}: {str(e)}"
            errores.append(error_msg)
            print(f"✗ {error_msg}")
    
    # Resultados finales
    print("\n=== RESUMEN DEL PROCESO ===")
    print(f"- Registros procesados con éxito: {registros_procesados}")
    print(f"- Errores encontrados: {len(errores)}")
    
    if errores:
        print("\nDetalle de errores:")
        for error in errores:
            print(f"  • {error}")
    
    if registros_procesados > 0:
        print("\n¡Proceso completado con éxito!")
    else:
        print("\nProceso completado sin registros insertados.")

def main():
    """Función principal"""
    print("=== PRUEBA DE CARGA MASIVA DE PATRONES ===")
    
    # Definir ruta de la base de datos
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    if not os.path.exists(ruta_bd):
        ruta_bd = input("Ingrese la ruta a la base de datos SUA: ")
    
    # Crear archivo de prueba
    ruta_excel = 'carga_patrones_prueba.xlsx'
    crear_excel_prueba(ruta_excel)
    
    # Conectar a la base de datos
    print(f"\nConectando a la base de datos: {ruta_bd}")
    conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
    
    if not conector.conectar():
        print("ERROR: No se pudo conectar a la base de datos")
        sys.exit(1)
    
    # Procesar datos
    try:
        procesar_datos(conector, ruta_excel)
    finally:
        # Cerrar conexión
        if conector.conn:
            conector.desconectar()
            print("\nConexión a la base de datos cerrada.")

if __name__ == "__main__":
    main() 