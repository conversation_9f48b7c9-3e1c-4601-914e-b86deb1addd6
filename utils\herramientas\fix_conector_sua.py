"""
Script para identificar y corregir errores en el archivo conector_sua.py
"""

import re
import traceback

def fix_conector_sua():
    try:
        archivo = 'conectores/conector_sua.py'
        
        # Leer el archivo
        with open(archivo, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        # Buscar errores comunes
        
        # 1. Buscar la función procesar_modificacion_tipo_descuento
        patron = r'def procesar_modificacion_tipo_descuento\(self, datos_mov\):(.*?)def procesar_movimiento'
        match = re.search(patron, contenido, re.DOTALL)
        
        if match:
            funcion = match.group(1)
            print(f"Encontrada función procesar_modificacion_tipo_descuento")
            
            # Buscar el bloque problemático
            bloque_if = re.search(r'if isinstance\(tipo_descuento.*?(\n\s+else:.*?)\n\s+if tipo_descuento_codigo', funcion, re.DOTALL)
            
            if bloque_if:
                bloque = bloque_if.group(0)
                lineas = bloque.split('\n')
                for i, linea in enumerate(lineas):
                    print(f"{i:2d}: {linea}")
                    
                # Verificar el nivel de indentación
                espacios = []
                for linea in lineas:
                    if linea.strip():
                        espacios_iniciales = len(linea) - len(linea.lstrip())
                        espacios.append(espacios_iniciales)
                
                print(f"Niveles de indentación: {espacios}")
                
                # Corregir problema de sintaxis
                correccion_necesaria = False
                contenido_corregido = contenido
                
                # Verificar si hay un error de indentación en else:
                if len(espacios) >= 2 and espacios[0] < espacios[1]:
                    print("¡Posible error de indentación detectado!")
                    correccion_necesaria = True
                    
                    # Corregir el problema
                    nuevo_contenido = contenido.replace(
                        "            if isinstance(tipo_descuento, (int, float)) or (isinstance(tipo_descuento, str) and tipo_descuento.strip().isdigit()):",
                        "            if isinstance(tipo_descuento, (int, float)) or (isinstance(tipo_descuento, str) and tipo_descuento.strip().isdigit()):"
                    )
                    
                    if nuevo_contenido != contenido:
                        contenido_corregido = nuevo_contenido
                        print("Se corrigió un error de sintaxis")

                # Guardar cambios si es necesario
                if correccion_necesaria:
                    with open(archivo, 'w', encoding='utf-8') as f:
                        f.write(contenido_corregido)
                    print(f"Se han guardado los cambios en {archivo}")
                else:
                    print("No se detectaron errores que requieran corrección.")
            else:
                print("No se encontró el bloque if/else problemático")
        else:
            print("No se encontró la función procesar_modificacion_tipo_descuento")
            
    except Exception as e:
        print(f"Error al procesar el archivo: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    fix_conector_sua() 