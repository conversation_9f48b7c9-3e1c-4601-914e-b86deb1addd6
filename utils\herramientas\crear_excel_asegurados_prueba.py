"""
Generador de Excel de prueba para la carga masiva de asegurados.
"""

import pandas as pd
import random
import string
import datetime

def generar_curp():
    """Genera una CURP aleatoria con formato válido"""
    # 4 letras iniciales (usando solo consonantes para simplificar)
    consonantes = 'BCDFGHJKLMNPQRSTVWXYZ'
    letras = ''.join(random.choice(consonantes) for _ in range(4))
    
    # Fecha de nacimiento (formato AAMMDD)
    año = random.randint(60, 99)  # Personas entre 20 y 60 años
    mes = random.randint(1, 12)
    dia = random.randint(1, 28)  # Evitamos problemas con meses cortos
    fecha = f"{año:02d}{mes:02d}{dia:02d}"
    
    # Género (H/M) y estado
    genero = random.choice(['H', 'M'])
    estado = random.choice(['AS', 'BC', 'BS', 'CC', 'CS', 'CH', 'CL', 'CM', 'DF', 'DG', 'GT', 'GR', 'HG', 'JC', 'MC', 'MN', 'MS', 'NT', 'NL', 'OC', 'PL', 'QT', 'QR', 'SP', 'SL', 'SR', 'TC', 'TS', 'TL', 'VZ', 'YN', 'ZS'])
    
    # 3 consonantes internas
    consonantes_internas = ''.join(random.choice(consonantes) for _ in range(3))
    
    # 2 dígitos al final
    digitos = ''.join(random.choice(string.digits) for _ in range(2))
    
    return f"{letras}{fecha}{genero}{estado}{consonantes_internas}{digitos}"

def generar_rfc():
    """Genera un RFC aleatorio con formato válido"""
    # 4 letras iniciales
    letras = ''.join(random.choice(string.ascii_uppercase) for _ in range(4))
    
    # Fecha de nacimiento (formato AAMMDD)
    año = random.randint(60, 99)
    mes = random.randint(1, 12)
    dia = random.randint(1, 28)
    fecha = f"{año:02d}{mes:02d}{dia:02d}"
    
    # 3 caracteres alfanuméricos
    alfanum = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(3))
    
    return f"{letras}{fecha}{alfanum}"

def generar_nss():
    """Genera un NSS aleatorio con formato válido (11 dígitos)"""
    # Formato: 2 dígitos para el área geográfica + 2 dígitos para el año + 2 dígitos para el día + 5 dígitos para el número
    area = random.randint(1, 99)
    año = random.randint(50, 99)
    dia = random.randint(1, 366)
    numero = random.randint(0, 99999)
    
    # En algunos casos, el NSS puede comenzar con uno o varios ceros
    # Para probar esto, a veces generamos números que requieren ceros iniciales
    if random.choice([True, False]):
        area = random.randint(0, 9)  # Forzar que area sea de un solo dígito
    
    return f"{area:02d}{año:02d}{dia:03d}{numero:05d}"

def generar_datos_asegurados(num_registros=5, registros_patronales=None):
    """Genera datos aleatorios para asegurados"""
    # Si no se proporcionan registros patronales, usar uno por defecto
    if not registros_patronales:
        registros_patronales = ["R1265455102"]
    
    # Listas para apellidos y nombres comunes en español
    apellidos = [
        "GONZALEZ", "RODRIGUEZ", "LOPEZ", "MARTINEZ", "PEREZ", "GOMEZ", "SANCHEZ", 
        "FERNANDEZ", "RAMIREZ", "TORRES", "DIAZ", "HERNANDEZ", "VARGAS", "MORALES"
    ]
    
    nombres_masculinos = [
        "JUAN", "JOSE", "ANTONIO", "MANUEL", "FRANCISCO", "CARLOS", "JAVIER", "DANIEL", 
        "MIGUEL", "RAFAEL", "PEDRO", "FERNANDO", "DAVID", "LUIS", "ANGEL"
    ]
    
    nombres_femeninos = [
        "MARIA", "ANA", "LAURA", "CARMEN", "ISABEL", "SOFIA", "PATRICIA", "ELENA", 
        "ROSA", "DOLORES", "PILAR", "TERESA", "SILVIA", "MONICA", "BEATRIZ"
    ]
    
    # Tipos de trabajador, salario y jornada
    tipos_trabajador = ["Permanente", "Eventual", "Eventual Construccion"]
    tipos_salario = ["Fijo", "Variable", "Mixto"]
    tipos_jornada = ["Jornada Completa", "1 dia", "2 dias", "3 dias", "4 dias", "5 dias", "Jornada Reducida"]
    
    # Lista para almacenar los datos
    datos = []
    
    # Generar registros
    for _ in range(num_registros):
        # Determinar el género
        genero = random.choice(["M", "F"])
        
        # Seleccionar nombre según el género
        if genero == "M":
            nombre = random.choice(nombres_masculinos)
        else:
            nombre = random.choice(nombres_femeninos)
        
        # Generar dos apellidos
        primer_apellido = random.choice(apellidos)
        segundo_apellido = random.choice(apellidos)
        
        # Generar salario entre 300 y 1000
        salario = round(random.uniform(300, 1000), 2)
        
        # Fecha de alta (entre 1 y 3 meses atrás)
        dias_atras = random.randint(30, 90)
        fecha_alta = (datetime.datetime.now() - datetime.timedelta(days=dias_atras)).strftime("%d/%m/%Y")
        
        # Generar códigos
        nss = generar_nss()
        curp = generar_curp()
        rfc = generar_rfc()
        
        # Seleccionar registro patronal aleatorio
        registro_patronal = random.choice(registros_patronales)
        
        # Datos para Infonavit (algunos tendrán, otros no)
        tiene_infonavit = random.choice([True, False, False, False])  # 25% de probabilidad
        if tiene_infonavit:
            numero_credito = f"{random.randint(1000000000, 9999999999)}"
            tipo_descuento = random.choice([1, 2, 3])  # 1=Porcentaje, 2=Cuota Fija, 3=VSM
            
            if tipo_descuento == 1:  # Porcentaje (entre 5% y 30%)
                valor_descuento = round(random.uniform(5, 30), 2)
            elif tipo_descuento == 2:  # Cuota Fija (entre 500 y 2000)
                valor_descuento = round(random.uniform(500, 2000), 2)
            else:  # VSM (entre 1 y 10)
                valor_descuento = round(random.uniform(1, 10), 2)
        else:
            numero_credito = ""
            tipo_descuento = ""
            valor_descuento = ""
        
        # Crear el registro
        registro = {
            "Registro Patronal": registro_patronal,
            "Número de Seguridad Social": nss,
            "RFC": rfc,
            "CURP": curp,
            "Primer apellido": primer_apellido,
            "Segundo apellido": segundo_apellido,
            "Nombre(s)": nombre,
            "Tipo de trabajador": random.choice(tipos_trabajador),
            "Tipo de salario": random.choice(tipos_salario),
            "Tipo de jornada": random.choice(tipos_jornada),
            "Fecha de alta": fecha_alta,
            "Salario diario integrado": salario,
            "Ubicación": "OBRA A",
            "Puesto": "EMPLEADO",
            "UMF": f"{random.randint(0, 99):03d}",
            "Código Postal": f"{random.randint(10000, 99999)}",
            "Lugar de nacimiento": "JALISCO",
            "Sexo": genero,
            "Numero Credito Infonavit": numero_credito,
            "Tipo Descuento": tipo_descuento,
            "Valor Descuento": valor_descuento
        }
        
        datos.append(registro)
    
    return pd.DataFrame(datos)

def generar_excel_prueba(nombre_archivo="carga_asegurados_prueba.xlsx", num_registros=10):
    """Genera un archivo Excel de prueba con datos de asegurados"""
    # Definir algunos registros patronales para las pruebas
    registros_patronales = [
        "R1265455102",  # Este registro ya debe existir en la base de datos
        "E5352621109",
        "S5110723100"
    ]
    
    # Generar datos
    df = generar_datos_asegurados(num_registros, registros_patronales)
    
    # Crear el Excel con dos hojas: una para patrones y otra para asegurados
    with pd.ExcelWriter(nombre_archivo, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name="Asegurados", index=False)
    
    print(f"Archivo Excel generado: {nombre_archivo}")
    print(f"Se generaron {num_registros} registros de asegurados para {len(registros_patronales)} registros patronales.")

if __name__ == "__main__":
    generar_excel_prueba(num_registros=15) 