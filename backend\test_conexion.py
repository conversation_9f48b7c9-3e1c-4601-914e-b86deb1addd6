import requests
import json

url = "http://localhost:8000/api/v1/conexion/test-conexion"
data = {
    "ruta_sua": r"C:\Cobranza\SUA\SUA.MDB",
    "reg_pat": "R1265455102"
}

headers = {
    "Content-Type": "application/json"
}

try:
    response = requests.post(url, json=data, headers=headers)
    print(f"Status Code: {response.status_code}")
    print("\nResponse Headers:")
    print(json.dumps(dict(response.headers), indent=2))
    print("\nResponse Body:")
    print(json.dumps(response.json(), indent=2))
except Exception as e:
    print(f"Error: {str(e)}")
    if hasattr(e, 'response'):
        print(f"\nResponse Status: {e.response.status_code}")
        print(f"Response Text: {e.response.text}") 