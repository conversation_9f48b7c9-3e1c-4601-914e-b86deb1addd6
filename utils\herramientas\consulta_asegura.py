"""
Script para consultar registros de la tabla Asegura y analizar el tipo de descuento.
Este script crea un archivo temporal para verificar cómo se almacena el tipo de descuento
en la tabla Asegura y Movtos.
"""

import os
import sys
from conectores.conector_sua import ConectorSUA

def consultar_tabla(conector, tabla, num_afil):
    """Consulta registros de una tabla por NSS"""
    print(f"\n=== CONSULTA DE TABLA {tabla} PARA NSS {num_afil} ===")
    
    try:
        # Ejecutar consulta
        query = f"SELECT * FROM [{tabla}] WHERE NUM_AFIL = '{num_afil}'"
        cursor = conector.conn.cursor()
        cursor.execute(query)
        
        # Obtener columnas
        columnas = [column[0] for column in cursor.description]
        
        # Obtener filas
        rows = cursor.fetchall()
        
        if not rows:
            print(f"No se encontraron registros en {tabla} para NSS {num_afil}")
            return None
            
        print(f"Se encontraron {len(rows)} registros")
        
        # Mostrar datos relevantes
        if tabla == 'Asegura':
            for row in rows:
                print("\nDatos del registro:")
                print(f"REG_PATR: {row[0]}")
                print(f"NUM_AFIL: {row[1]}")
                print(f"PAG_INFO (Num Crédito): {row[9] if len(row) > 9 else 'N/A'}")
                print(f"TIP_DSC (Tipo Descuento): {row[10] if len(row) > 10 else 'N/A'} (tipo: {type(row[10] if len(row) > 10 else None).__name__})")
                print(f"VAL_DSC (Valor Descuento): {row[11] if len(row) > 11 else 'N/A'}")
                
        elif tabla == 'Movtos':
            for row in rows:
                print("\nDatos del registro:")
                print(f"REG_PATR: {row[0]}")
                print(f"NUM_AFIL: {row[1]}")
                print(f"TIP_MOVS: {row[2]}")
                print(f"Num_Cre (Num Crédito): {row[23] if len(row) > 23 else 'N/A'}")
                print(f"Val_Des (Valor Descuento): {row[24] if len(row) > 24 else 'N/A'}")
                print(f"Tip_Des (Tipo Descuento): {row[25] if len(row) > 25 else 'N/A'} (tipo: {type(row[25] if len(row) > 25 else None).__name__})")
        
        return rows
    
    except Exception as e:
        print(f"Error al consultar tabla {tabla}: {e}")
        return None

def main():
    # Ruta a la base de datos SUA
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    
    # Conectar a la base de datos
    print(f"Conectando a la base de datos: {ruta_bd}")
    conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
    
    if not conector.conectar():
        print("ERROR: No se pudo conectar a la base de datos")
        return
    
    try:
        # Consultar NSS problemáticos
        nssList = ['03178452540', '04088625456']
        
        for nss in nssList:
            print(f"\n{'='*50}")
            print(f"CONSULTANDO NSS: {nss}")
            print(f"{'='*50}")
            
            # Consultar Asegura
            asegura_rows = consultar_tabla(conector, 'Asegura', nss)
            
            # Consultar Movtos
            movtos_rows = consultar_tabla(conector, 'Movtos', nss)
            
            # Verificar específicamente registros de Movtos tipo 15 (Infonavit)
            try:
                query = f"SELECT * FROM [Movtos] WHERE NUM_AFIL = '{nss}' AND TIP_MOVS = '15'"
                cursor = conector.conn.cursor()
                cursor.execute(query)
                
                infonavit_rows = cursor.fetchall()
                
                if infonavit_rows:
                    print(f"\n=== REGISTROS TIPO 15 (INFONAVIT) PARA NSS {nss} ===")
                    print(f"Se encontraron {len(infonavit_rows)} registros")
                    
                    for row in infonavit_rows:
                        print(f"\nREG_PATR: {row[0]}")
                        print(f"NUM_AFIL: {row[1]}")
                        print(f"TIP_MOVS: {row[2]}")
                        print(f"Num_Cre: {row[23] if len(row) > 23 else 'N/A'}")
                        print(f"Val_Des: {row[24] if len(row) > 24 else 'N/A'}")
                        print(f"Tip_Des: {row[25] if len(row) > 25 else 'N/A'} (tipo: {type(row[25] if len(row) > 25 else None).__name__})")
                else:
                    print(f"\nNo se encontraron registros tipo 15 (Infonavit) para NSS {nss}")
            
            except Exception as e:
                print(f"Error al consultar registros tipo 15: {e}")
        
    finally:
        # Cerrar conexión
        if conector.conn:
            conector.desconectar()
            print("\nConexión a la base de datos cerrada.")

if __name__ == "__main__":
    main() 