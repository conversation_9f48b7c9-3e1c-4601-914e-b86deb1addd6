from conectores.conector_sua import ConectorSUA
import traceback

def probar_tipo_19():
    try:
        # Conectar a la base de datos
        conector = ConectorSUA(ruta_bd=r'C:\Cobranza\SUA\SUA.MDB', password="S5@N52V49")
        if not conector.conectar():
            print("❌ Error al conectar a la base de datos")
            return
        print("✅ Conexión establecida correctamente")
        
        # Datos de prueba para movimiento tipo 19
        datos_mov = {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456', 
            'TIP_MOVS': '19',
            'FEC_INIC': '2025-04-25',
            'Val_Des': 2450.50
        }
        
        # Procesar el movimiento directamente
        print("Intentando procesar modificación de valor de descuento...")
        resultado = conector.procesar_modificacion_valor_descuento(datos_mov)
        
        if resultado:
            print("✅ Movimiento procesado correctamente")
            
            # Verificar que se guardó correctamente en la base de datos
            print("\nVerificando el registro insertado:")
            resultados = conector.ejecutar_consulta(
                'SELECT TOP 1 REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, Tip_Des, Val_Des FROM Movtos WHERE TIP_MOVS=? ORDER BY FEC_INIC DESC', 
                ('19',)
            )
            if resultados:
                for registro in resultados:
                    print(registro)
            else:
                print("No se encontró el registro en la base de datos")
        else:
            print("❌ Error al procesar movimiento")
        
        conector.desconectar()
    
    except Exception as e:
        print(f"Error general: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    probar_tipo_19() 