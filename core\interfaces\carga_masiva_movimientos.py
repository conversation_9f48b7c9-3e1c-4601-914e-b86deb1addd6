"""
Herramienta para carga masiva de movimientos al SUA desde un archivo Excel.

Este script proporciona una interfaz gráfica para cargar movimientos de manera masiva
al sistema SUA. Permite seleccionar un archivo Excel con los movimientos a cargar
y configura la conexión a la base de datos SUA.

El archivo Excel debe contener las siguientes columnas:
- REG_PATR: Registro patronal
- NUM_AFIL: Número de afiliación del trabajador
- TIPO_MOVIMIENTO: Tipo de movimiento (02=Baja, 07=Modificación Salarial, 15=Crédito Infonavit, etc.)
- FECHA_MOVIMIENTO: Fecha del movimiento (DD/MM/AAAA)
- SALARIO: Salario base de cotización (para modificaciones salariales)
- TIPO_DESCUENTO: Tipo de descuento Infonavit (para créditos)
- VALOR_DESCUENTO: Valor del descuento Infonavit (para créditos)

NOTA: Las altas (01) no se procesan con esta herramienta, deben ser gestionadas 
con el módulo específico de carga de asegurados.

Características especiales:
1. Bajas (02): El sistema actualiza automáticamente:
   - La fecha de baja en Asegura
   - La fecha de fin de descuento en Asegura si tiene crédito Infonavit
   - Genera un movimiento tipo 16 para finalizar crédito si aplica
   - Lleva un conteo secuencial de bajas en TIP_INC (1 para la primera baja, 
     2 para la segunda, etc.)

2. Créditos Infonavit (15): Requieren:
   - TIPO_DESCUENTO: 1=Porcentaje, 2=Cuota Fija, 3=Factor de Descuento
   - VALOR_DESCUENTO: Valor según el tipo de descuento
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
import logging
from datetime import datetime
from conectores.conector_sua import ConectorSUA

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG,  # Cambiar a DEBUG para ver todos los mensajes
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('resultados/carga_movimientos.log', mode='w'),  # mode='w' para sobrescribir el archivo
        logging.StreamHandler()
    ]
)

class CargaMasivaMovimientos:
    def __init__(self, root):
        self.root = root
        self.root.title("Carga Masiva de Movimientos SUA")
        
        # Variables
        self.archivo_excel = tk.StringVar()
        self.ruta_bd = tk.StringVar(value="C:\\Cobranza\\SUA\\SUA.MDB")
        self.password_bd = tk.StringVar(value="S5@N52V49")
        
        # Mapeo de descripciones a códigos de movimiento
        self.mapeo_tipos_movimiento = {
            # Códigos numéricos
            '01': '01', '02': '02', '07': '07', '08': '08', '09': '09', 
            '11': '11', '12': '12', '15': '15', '16': '16', '17': '17',
            '18': '18', '19': '19', '20': '20',
            # Descripciones (sin acentos y en minúsculas para comparación)
            'alta': '01',
            'baja': '02',
            'modificacion de salario': '07', 'modificacion salario': '07', 'mod salario': '07',
            'reingreso': '08',
            'aportacion voluntaria': '09', 'aportacion': '09',
            'ausentismo': '11',
            'incapacidad': '12',
            'inicio de credito': '15', 'inicio credito': '15', 'credito infonavit': '15', 'icv': '15',
            'suspension de descuento': '16', 'suspension descuento': '16', 'fs': '16',
            'reinicio de descuento': '17', 'reinicio descuento': '17', 'rd': '17',
            'modificacion de tipo de descuento': '18', 'modificacion tipo descuento': '18', 'mtd': '18',
            'modificacion de valor de descuento': '19', 'modificacion valor descuento': '19', 'mvd': '19',
            'modificacion de numero de credito': '20', 'modificacion numero credito': '20', 'mnd': '20'
        }
        
        # Tipos de movimiento válidos (excluyendo altas)
        self.tipos_validos = ['02', '07', '08', '09', '11', '12', '15', '16', '17', '18', '19', '20']
        
        self.crear_interfaz()
        
    def crear_interfaz(self):
        """Crea la interfaz gráfica de usuario."""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Sección archivo Excel
        ttk.Label(main_frame, text="Archivo Excel:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(main_frame, textvariable=self.archivo_excel, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(main_frame, text="Examinar", command=self.seleccionar_archivo).grid(row=0, column=2)
        ttk.Button(main_frame, text="Generar Plantilla", command=self.generar_plantilla).grid(row=0, column=3)
        
        # Sección base de datos
        ttk.Label(main_frame, text="Base de datos:").grid(row=1, column=0, sticky=tk.W, pady=10)
        ttk.Entry(main_frame, textvariable=self.ruta_bd, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="Examinar", command=self.seleccionar_bd).grid(row=1, column=2)
        
        # Password BD (opcional)
        ttk.Label(main_frame, text="Password BD:").grid(row=2, column=0, sticky=tk.W)
        ttk.Entry(main_frame, textvariable=self.password_bd, show="*", width=50).grid(row=2, column=1, padx=5)
        
        # Botones de acción
        ttk.Button(main_frame, text="Probar Conexión", command=self.probar_conexion).grid(row=3, column=0, pady=10)
        ttk.Button(main_frame, text="Validar Excel", command=self.validar_excel).grid(row=3, column=1, pady=10)
        ttk.Button(main_frame, text="Cargar Movimientos", command=self.cargar_movimientos).grid(row=3, column=2, pady=10)
        ttk.Button(main_frame, text="Analizar Movimientos", command=self.analizar_movimientos).grid(row=3, column=3, pady=10)
        
    def seleccionar_archivo(self):
        """Abre diálogo para seleccionar archivo Excel."""
        filename = filedialog.askopenfilename(
            title="Seleccionar archivo Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.archivo_excel.set(filename)
            
    def seleccionar_bd(self):
        """Abre diálogo para seleccionar base de datos SUA."""
        filename = filedialog.askopenfilename(
            title="Seleccionar base de datos SUA",
            filetypes=[("Access DB", "*.mdb")]
        )
        if filename:
            self.ruta_bd.set(filename)
            
    def probar_conexion(self):
        """Prueba la conexión a la base de datos."""
        try:
            conector = ConectorSUA(self.ruta_bd.get(), self.password_bd.get())
            conector.conectar()
            messagebox.showinfo("Éxito", "Conexión exitosa a la base de datos")
            conector.desconectar()
        except Exception as e:
            messagebox.showerror("Error", f"Error al conectar: {str(e)}")
            
    def validar_excel(self):
        """Valida el formato y contenido del archivo Excel."""
        try:
            # Leer el archivo Excel
            df = pd.read_excel(self.archivo_excel.get())

            # Verificar columnas requeridas (layout uniforme)
            columnas_requeridas = [
                'REG_PATR', 'NUM_AFIL', 'NOMBRE', 'FECHA_MOVIMIENTO', 'TIPO_MOVIMIENTO',
                'SALARIO', 'TIPO_DESCUENTO', 'VALOR_DESCUENTO', 'NUMERO_CREDITO',
                'FOLIO_INC', 'RAMA_SEG', 'TIPO_SEG', 'SECUELA', 'CONT_INC',
                'NUM_DIAS', 'PORCENTAJE', 'OBSERVACIONES'
            ]
            for col in columnas_requeridas:
                if col not in df.columns:
                    messagebox.showerror("Error", f"Falta la columna requerida: {col}")
                    return False

            # Convertir tipos de movimiento a sus códigos (igual que antes)
            for idx, row in df.iterrows():
                if pd.notna(row['TIPO_MOVIMIENTO']):
                    import unicodedata
                    tipo_mov = str(row['TIPO_MOVIMIENTO']).strip().lower()
                    tipo_mov = ''.join(c for c in unicodedata.normalize('NFD', tipo_mov)
                                   if unicodedata.category(c) != 'Mn')
                    if tipo_mov.isdigit() and tipo_mov in self.mapeo_tipos_movimiento:
                        df.at[idx, 'TIPO_MOVIMIENTO'] = tipo_mov
                        continue
                    if tipo_mov in self.mapeo_tipos_movimiento:
                        df.at[idx, 'TIPO_MOVIMIENTO'] = self.mapeo_tipos_movimiento[tipo_mov]
                    else:
                        for codigo in self.tipos_validos:
                            if codigo in tipo_mov:
                                df.at[idx, 'TIPO_MOVIMIENTO'] = codigo
                                break

            # Validaciones específicas por tipo de movimiento (igual que antes, pero usando los nuevos campos)
            errores = []
            registros_con_error = []
            try:
                df['FECHA_MOVIMIENTO'] = pd.to_datetime(df['FECHA_MOVIMIENTO'], format='%d/%m/%Y', errors='coerce')
                fechas_invalidas = df['FECHA_MOVIMIENTO'].isna().sum()
                if fechas_invalidas > 0:
                    errores.append(f"Hay {fechas_invalidas} fechas con formato inválido en FECHA_MOVIMIENTO. El formato debe ser DD/MM/AAAA")
            except Exception as e:
                errores.append(f"Error al validar FECHA_MOVIMIENTO: {str(e)}")

            if 'TIPO_MOVIMIENTO' in df.columns:
                tipos_invalidos = [t for t in df['TIPO_MOVIMIENTO'].unique() if t not in self.tipos_validos]
                if tipos_invalidos:
                    errores.append(f"Tipos de movimiento no válidos: {', '.join(tipos_invalidos)}.\n" + \
                                   f"Los tipos válidos son: {', '.join(self.tipos_validos)}")

            for idx, row in df.iterrows():
                tipo_mov = str(row['TIPO_MOVIMIENTO']).strip()
                errores_registro = []
                if tipo_mov == '11':  # Ausentismo
                    if pd.isna(row.get('NUM_DIAS')):
                        errores_registro.append("Para ausencias se requiere NUM_DIAS")
                elif tipo_mov == '07':  # Modificación Salarial
                    if pd.isna(row.get('SALARIO')):
                        errores_registro.append("Para modificaciones salariales se requiere SALARIO")
                elif tipo_mov == '15':  # Inicio de Crédito Infonavit
                    if pd.isna(row.get('TIPO_DESCUENTO')):
                        errores_registro.append("Para inicio de crédito se requiere TIPO_DESCUENTO")
                    if pd.isna(row.get('VALOR_DESCUENTO')):
                        errores_registro.append("Para inicio de crédito se requiere VALOR_DESCUENTO")
                elif tipo_mov == '12':  # Incapacidad
                    if pd.isna(row.get('FOLIO_INC')):
                        errores_registro.append("Para incapacidad se requiere FOLIO_INC")
                    if pd.isna(row.get('RAMA_SEG')):
                        errores_registro.append("Para incapacidad se requiere RAMA_SEG")
                    if pd.isna(row.get('CONT_INC')):
                        errores_registro.append("Para incapacidad se requiere CONT_INC")
                    if pd.isna(row.get('NUM_DIAS')):
                        errores_registro.append("Para incapacidad se requiere NUM_DIAS")
                if errores_registro:
                    registros_con_error.append({
                        'fila': idx + 2,
                        'tipo': tipo_mov,
                        'errores': errores_registro
                    })

            if errores:
                messagebox.showerror("Errores de validación", "\n".join(errores))
                return False
            if registros_con_error:
                mensaje = "Se encontraron errores en los siguientes registros:\n\n"
                for reg in registros_con_error:
                    mensaje += f"Fila {reg['fila']} (Tipo {reg['tipo']}):\n"
                    mensaje += "\n".join([f"- {error}" for error in reg['errores']])
                    mensaje += "\n\n"
                messagebox.showerror("Errores en registros", mensaje)
                return False
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Error al validar el archivo Excel: {str(e)}")
            return False
            
    def cargar_movimientos(self):
        """Procesa el archivo Excel y carga los movimientos a la base de datos."""
        if not self.archivo_excel.get():
            messagebox.showerror("Error", "Seleccione un archivo Excel")
            return
            
        try:
            logging.debug("Iniciando carga de movimientos")
            # Leer Excel con tipos de datos explícitos para preservar los ceros iniciales
            df = pd.read_excel(
                self.archivo_excel.get(),
                dtype={
                    'REG_PATR': str,
                    'NUM_AFIL': str,
                    'TIPO_MOVIMIENTO': str,
                    'RAMA_SEG': str,
                    'TIPO_SEG': str,
                    'SECUELA': str,
                    'CONT_INC': str,
                    'FOLIO_INC': str,
                    'NUM_DIAS': 'Int64',  # Usar tipo nullable Int64 en lugar de int para permitir valores nulos
                    'PORCENTAJE': float,
                    'NUMERO_CREDITO': str  # Agregar NUMERO_CREDITO como string
                },
                parse_dates=['FECHA_MOVIMIENTO']
            )
            
            logging.debug(f"Archivo Excel leído correctamente. Registros encontrados: {len(df)}")
            logging.debug(f"Columnas encontradas: {df.columns.tolist()}")
            
            # Imprimir el primer registro para diagnóstico
            if not df.empty:
                logging.debug(f"Primer registro:\n{df.iloc[0].to_dict()}")
            
            # Conectar a BD
            conector = ConectorSUA(self.ruta_bd.get(), self.password_bd.get())
            conector.conectar()
            logging.debug("Conexión a base de datos establecida")
            
            # Procesar cada registro
            total = len(df)
            errores = []
            procesados = 0
            
            # Agrupar por tipo de movimiento para procesamiento personalizado
            for idx, row in df.iterrows():
                try:
                    tipo_movimiento = str(row['TIPO_MOVIMIENTO']).strip()
                    logging.debug(f"Procesando registro {idx + 1}/{total} - Tipo: {tipo_movimiento}")
                    
                    if tipo_movimiento == '12':  # Incapacidad
                        logging.debug(f"Preparando datos de incapacidad para {row['NUM_AFIL']}")
                        
                        # Verificar fecha
                        if pd.isna(row['FECHA_MOVIMIENTO']):
                            error_msg = f"Falta la fecha de movimiento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Agregar campos específicos para incapacidad
                        datos_mov = {
                            'REG_PATR': str(row['REG_PATR']).strip(),
                            'NUM_AFIL': str(row['NUM_AFIL']).strip(),
                            'TIP_MOVS': tipo_movimiento,
                            'FEC_INIC': row['FECHA_MOVIMIENTO'].strftime('%Y-%m-%d'),
                            'RAMA_SEGURO': str(row['RAMA_SEG']).strip() if pd.notna(row['RAMA_SEG']) else '',
                            'TIPO_RIESGO': str(row['TIPO_SEG']).strip() if pd.notna(row['TIPO_SEG']) else '',
                            'CONSECUENCIA': str(row['SECUELA']).strip() if pd.notna(row['SECUELA']) else '',
                            'CONTROL_INCAPACIDAD': str(row['CONT_INC']).strip() if pd.notna(row['CONT_INC']) else '',
                            'FOL_INC': str(row['FOLIO_INC']).strip() if pd.notna(row['FOLIO_INC']) else '',
                            'NUM_DIAS': int(row['NUM_DIAS']) if pd.notna(row['NUM_DIAS']) else 0,
                            'PORCENTAJE': float(row['PORCENTAJE']) if pd.notna(row['PORCENTAJE']) else 0.0
                        }
                        
                        logging.debug(f"Datos de incapacidad preparados: {datos_mov}")
                        
                        try:
                            if not conector.procesar_incapacidad(datos_mov):
                                raise Exception("El método procesar_incapacidad devolvió False")
                            logging.info(f"Incapacidad procesada correctamente para {datos_mov['NUM_AFIL']}")
                            procesados += 1
                        except Exception as e:
                            error_msg = f"Error al procesar incapacidad para {datos_mov['NUM_AFIL']}: {str(e)}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                    
                    elif tipo_movimiento in ['16', '17']:  # Suspensión o Reinicio de crédito
                        logging.debug(f"Preparando datos de {'suspensión' if tipo_movimiento == '16' else 'reinicio'} de crédito para {row['NUM_AFIL']}")
                        
                        # Verificar fecha
                        if pd.isna(row['FECHA_MOVIMIENTO']):
                            error_msg = f"Falta la fecha de movimiento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Preparar datos del movimiento
                        datos_mov = {
                            'REG_PATR': str(row['REG_PATR']).strip(),
                            'NUM_AFIL': str(row['NUM_AFIL']).strip(),
                            'TIP_MOVS': tipo_movimiento,
                            'FEC_INIC': row['FECHA_MOVIMIENTO'].strftime('%Y-%m-%d')
                        }
                        
                        # Agregar número de crédito si está presente
                        if 'NUMERO_CREDITO' in row and pd.notna(row['NUMERO_CREDITO']):
                            datos_mov['Num_Cre'] = str(row['NUMERO_CREDITO']).strip()
                        
                        # Agregar campos opcionales específicos para suspensión o reinicio
                        if 'TIPO_DESCUENTO' in row and pd.notna(row['TIPO_DESCUENTO']):
                            datos_mov['Tip_Des'] = row['TIPO_DESCUENTO']
                        
                        if 'VALOR_DESCUENTO' in row and pd.notna(row['VALOR_DESCUENTO']):
                            datos_mov['Val_Des'] = row['VALOR_DESCUENTO']
                        
                        logging.debug(f"Datos de {'suspensión' if tipo_movimiento == '16' else 'reinicio'} preparados: {datos_mov}")
                        
                        try:
                            if not conector.procesar_movimiento(datos_mov):
                                raise Exception(f"Error al procesar {'suspensión' if tipo_movimiento == '16' else 'reinicio'} de crédito")
                            logging.info(f"Movimiento tipo {tipo_movimiento} procesado correctamente para {datos_mov['NUM_AFIL']}")
                            procesados += 1
                        except Exception as e:
                            error_msg = f"Error en movimiento tipo {tipo_movimiento} para {datos_mov['NUM_AFIL']}: {str(e)}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                    
                    elif tipo_movimiento == '18':  # Modificación de Tipo de Descuento
                        logging.debug(f"Preparando datos de modificación de tipo de descuento para {row['NUM_AFIL']}")
                        
                        # Verificar fecha
                        if pd.isna(row['FECHA_MOVIMIENTO']):
                            error_msg = f"Falta la fecha de movimiento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Verificar que exista el tipo de descuento
                        if 'TIPO_DESCUENTO' not in row or pd.isna(row['TIPO_DESCUENTO']):
                            error_msg = f"Falta el tipo de descuento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Preparar datos del movimiento (solo campos esenciales)
                        datos_mov = {
                            'REG_PATR': str(row['REG_PATR']).strip(),
                            'NUM_AFIL': str(row['NUM_AFIL']).strip(),
                            'TIP_MOVS': tipo_movimiento,
                            'FEC_INIC': row['FECHA_MOVIMIENTO'].strftime('%Y-%m-%d'),
                            'CVE_MOVS': 'C'  # C para modificación de tipo de descuento
                        }
                        
                        # Procesar el tipo de descuento
                        tipo_descuento = row['TIPO_DESCUENTO']
                        
                        # Si es numérico (1, 2 o 3), convertir al formato texto
                        tipo_descuento_texto = None
                        if isinstance(tipo_descuento, (int, float)) or (isinstance(tipo_descuento, str) and tipo_descuento.strip().isdigit()):
                            tipo_descuento_codigo = int(float(tipo_descuento))
                            tipo_descuento_texto = {
                                1: "Porcentaje",
                                2: "Cuota Fija",
                                3: "Factor de Descuento"
                            }.get(tipo_descuento_codigo)
                        else:
                            # Es texto, normalizarlo
                            tipo_lower = tipo_descuento.lower().strip()
                            if 'porcentaje' in tipo_lower:
                                tipo_descuento_texto = "Porcentaje"
                            elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
                                tipo_descuento_texto = "Cuota Fija"
                            elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
                                tipo_descuento_texto = "Factor de Descuento"
                        
                        if not tipo_descuento_texto:
                            error_msg = f"Tipo de descuento no válido para el registro {idx + 1}: {tipo_descuento}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        datos_mov['Tip_Des'] = tipo_descuento_texto
                        
                        # Agregar valor de descuento si está presente
                        if 'VALOR_DESCUENTO' in row and pd.notna(row['VALOR_DESCUENTO']):
                            datos_mov['Val_Des'] = float(row['VALOR_DESCUENTO'])
                        
                        # Agregar número de crédito si está presente
                        if 'NUMERO_CREDITO' in row and pd.notna(row['NUMERO_CREDITO']):
                            datos_mov['Num_Cre'] = str(row['NUMERO_CREDITO']).strip()
                        
                        logging.debug(f"Datos de modificación de tipo de descuento preparados: {datos_mov}")
                        
                        try:
                            # Llamar directamente al método específico para modificación de tipo de descuento
                            if not conector.procesar_modificacion_tipo_descuento(datos_mov):
                                raise Exception("Error al procesar modificación de tipo de descuento")
                            
                            logging.info(f"Movimiento tipo 18 procesado correctamente para {datos_mov['NUM_AFIL']}")
                            procesados += 1
                        except Exception as e:
                            error_msg = f"Error en movimiento tipo 18 para {datos_mov['NUM_AFIL']}: {str(e)}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                    
                    elif tipo_movimiento == '19':  # Modificación de Valor de Descuento
                        logging.debug(f"Preparando datos de modificación de valor de descuento para {row['NUM_AFIL']}")
                        
                        # Verificar fecha
                        if pd.isna(row['FECHA_MOVIMIENTO']):
                            error_msg = f"Falta la fecha de movimiento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Verificar que exista el valor de descuento
                        if 'VALOR_DESCUENTO' not in row or pd.isna(row['VALOR_DESCUENTO']):
                            error_msg = f"Falta el valor de descuento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Preparar datos del movimiento (solo campos esenciales)
                        datos_mov = {
                            'REG_PATR': str(row['REG_PATR']).strip(),
                            'NUM_AFIL': str(row['NUM_AFIL']).strip(),
                            'TIP_MOVS': tipo_movimiento,
                            'FEC_INIC': row['FECHA_MOVIMIENTO'].strftime('%Y-%m-%d'),
                            'Val_Des': float(row['VALOR_DESCUENTO']),
                            'CVE_MOVS': 'C'  # C para modificación de valor de descuento
                        }
                        
                        # Agregar número de crédito si está presente
                        if 'NUMERO_CREDITO' in row and pd.notna(row['NUMERO_CREDITO']):
                            datos_mov['Num_Cre'] = str(row['NUMERO_CREDITO']).strip()
                        
                        logging.debug(f"Datos de modificación de valor de descuento preparados: {datos_mov}")
                        
                        try:
                            # Llamar directamente al método específico para modificación de valor de descuento
                            if not conector.procesar_modificacion_valor_descuento(datos_mov):
                                raise Exception("Error al procesar modificación de valor de descuento")
                            
                            logging.info(f"Movimiento tipo 19 procesado correctamente para {datos_mov['NUM_AFIL']}")
                            procesados += 1
                        except Exception as e:
                            error_msg = f"Error en movimiento tipo 19 para {datos_mov['NUM_AFIL']}: {str(e)}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                    
                    elif tipo_movimiento == '20':  # Modificación de Número de Crédito
                        logging.debug(f"Preparando datos de modificación de número de crédito para {row['NUM_AFIL']}")
                        
                        # Verificar fecha
                        if pd.isna(row['FECHA_MOVIMIENTO']):
                            error_msg = f"Falta la fecha de movimiento para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Verificar que exista el nuevo número de crédito
                        if 'NUMERO_CREDITO' not in row or pd.isna(row['NUMERO_CREDITO']):
                            error_msg = f"Falta el nuevo número de crédito para el registro {idx + 1}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                        
                        # Preparar datos del movimiento (solo campos esenciales)
                        datos_mov = {
                            'REG_PATR': str(row['REG_PATR']).strip(),
                            'NUM_AFIL': str(row['NUM_AFIL']).strip(),
                            'TIP_MOVS': tipo_movimiento,
                            'FEC_INIC': row['FECHA_MOVIMIENTO'].strftime('%Y-%m-%d'),
                            'Num_Cre': str(row['NUMERO_CREDITO']).strip(),
                            'CVE_MOVS': 'C'  # C para modificación de número de crédito
                        }
                        
                        logging.debug(f"Datos de modificación de número de crédito preparados: {datos_mov}")
                        
                        try:
                            # Llamar directamente al método específico para modificación de número de crédito
                            if not conector.procesar_modificacion_numero_credito(datos_mov):
                                raise Exception("Error al procesar modificación de número de crédito")
                            
                            logging.info(f"Movimiento tipo 20 procesado correctamente para {datos_mov['NUM_AFIL']}")
                            procesados += 1
                        except Exception as e:
                            error_msg = f"Error en movimiento tipo 20 para {datos_mov['NUM_AFIL']}: {str(e)}"
                            logging.error(error_msg)
                            errores.append(error_msg)
                            continue
                    
                    # ... resto del código para otros tipos de movimiento ...
                
                except Exception as e:
                    error_msg = f"Error en registro {idx + 1}: {str(e)}"
                    errores.append(error_msg)
                    logging.error(error_msg)
            
            conector.desconectar()
            
            # Mostrar resultado
            mensaje = f"Procesados {procesados} de {total} registros"
            if errores:
                mensaje += f"\n\nErrores ({len(errores)}):\n" + "\n".join(errores)
                messagebox.showwarning("Completado con errores", mensaje)
            else:
                messagebox.showinfo("Éxito", mensaje)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error al procesar archivo: {str(e)}")

    def generar_plantilla(self):
        """Genera una plantilla Excel con la estructura requerida y ejemplos."""
        try:
            # Crear DataFrame con la estructura base (layout uniforme)
            df_template = pd.DataFrame(columns=[
                'REG_PATR',
                'NUM_AFIL',
                'NOMBRE',
                'FECHA_MOVIMIENTO',
                'TIPO_MOVIMIENTO',
                'SALARIO',
                'TIPO_DESCUENTO',
                'VALOR_DESCUENTO',
                'NUMERO_CREDITO',
                'FOLIO_INC',
                'RAMA_SEG',
                'TIPO_SEG',
                'SECUELA',
                'CONT_INC',
                'NUM_DIAS',
                'PORCENTAJE',
                'OBSERVACIONES'
            ])

            # Agregar ejemplos para cada tipo de movimiento
            ejemplos = [
                # Modificación salarial
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '01/04/2025',
                    'TIPO_MOVIMIENTO': '07',
                    'SALARIO': 500.25,
                    'OBSERVACIONES': 'Modificación salarial',
                },
                # Incapacidad por riesgo de trabajo
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '01/04/2025',
                    'TIPO_MOVIMIENTO': '12',
                    'FOLIO_INC': 'FF000010',
                    'RAMA_SEG': '1 Riesgos de Trabajo',
                    'TIPO_SEG': '1 Accidente de Trabajo',
                    'SECUELA': '1 Incapacidad Temporal',
                    'CONT_INC': '2 Inicial',
                    'NUM_DIAS': 15,
                    'PORCENTAJE': '',
                    'OBSERVACIONES': 'Incapacidad por riesgo de trabajo',
                },
                # Incapacidad por enfermedad general
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '17/04/2025',
                    'TIPO_MOVIMIENTO': '12',
                    'FOLIO_INC': 'FF002000',
                    'RAMA_SEG': '2 Enfermedad General',
                    'TIPO_SEG': '',
                    'SECUELA': '',
                    'CONT_INC': '1 Unica',
                    'NUM_DIAS': 5,
                    'PORCENTAJE': '',
                    'OBSERVACIONES': 'Incapacidad por enfermedad general',
                },
                # Suspensión de crédito Infonavit
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '01/05/2025',
                    'TIPO_MOVIMIENTO': '16',
                    'NUMERO_CREDITO': '1565544777',
                    'OBSERVACIONES': 'Suspensión de crédito',
                },
                # Modificación de tipo de descuento
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '01/03/2025',
                    'TIPO_MOVIMIENTO': '18',
                    'TIPO_DESCUENTO': '3',
                    'VALOR_DESCUENTO': 6.59,
                    'NUMERO_CREDITO': '1565544777',
                    'OBSERVACIONES': 'Modificación tipo descuento',
                },
                # Modificación de valor de descuento
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '20/03/2025',
                    'TIPO_MOVIMIENTO': '19',
                    'VALOR_DESCUENTO': 8.25,
                    'NUMERO_CREDITO': '1565544777',
                    'OBSERVACIONES': 'Modificación valor descuento',
                },
                # Ausentismo
                {
                    'REG_PATR': 'E5352621109',
                    'NUM_AFIL': '04088625456',
                    'NOMBRE': 'JUAN PEREZ',
                    'FECHA_MOVIMIENTO': '10/06/2025',
                    'TIPO_MOVIMIENTO': '11',
                    'NUM_DIAS': 3,
                    'OBSERVACIONES': 'Ausentismo',
                },
            ]

            df_template = pd.concat([df_template, pd.DataFrame(ejemplos)], ignore_index=True)

            # Guardar en Excel
            ruta_salida = os.path.join('plantillas', 'plantilla_movimientos.xlsx')
            os.makedirs('plantillas', exist_ok=True)

            with pd.ExcelWriter(ruta_salida, engine='openpyxl') as writer:
                df_template.to_excel(writer, sheet_name='Movimientos', index=False)

            messagebox.showinfo("Éxito", f"Plantilla generada en: {ruta_salida}")
            return ruta_salida

        except Exception as e:
            messagebox.showerror("Error", f"Error al generar plantilla: {str(e)}")
            return None

    def analizar_movimientos(self):
        """Analiza la estructura de los movimientos en la BD para entender los campos necesarios."""
        try:
            # Establecer conexión
            conector = ConectorSUA(self.ruta_bd.get(), self.password_bd.get())
            if not conector.conectar():
                messagebox.showerror("Error", "No se pudo conectar a la base de datos")
                return
            
            # Crear ventana para mostrar resultados
            ventana_analisis = tk.Toplevel(self.root)
            ventana_analisis.title("Análisis de Movimientos")
            ventana_analisis.geometry("800x600")
            
            # Frame de opciones
            frame_opciones = ttk.Frame(ventana_analisis, padding="10")
            frame_opciones.pack(fill=tk.X)
            
            # Dropdown para seleccionar tipo
            ttk.Label(frame_opciones, text="Tipo de Movimiento:").pack(side=tk.LEFT, padx=5)
            
            tipos_movimiento = [
                "Todos los tipos",
                "01 - Alta",
                "02 - Baja",
                "07 - Modificación de Salario",
                "08 - Reingreso",
                "09 - Aportación Voluntaria",
                "11 - Ausentismo",
                "12 - Incapacidad",
                "15 - Inicio de Crédito de Vivienda (ICV)",
                "16 - Fecha de Suspensión de Descuento (FS)",
                "17 - Reinicio de Descuento (RD)",
                "18 - Modificación de Tipo de Descuento (MTD)",
                "19 - Modificación de Valor de Descuento (MVD)",
                "20 - Modificación de Número de Crédito (MND)"
            ]
            
            combo_tipo = ttk.Combobox(frame_opciones, values=tipos_movimiento, width=40)
            combo_tipo.current(0)
            combo_tipo.pack(side=tk.LEFT, padx=5)
            
            # Frame para resultados
            frame_resultados = ttk.Frame(ventana_analisis, padding="10")
            frame_resultados.pack(fill=tk.BOTH, expand=True)
            
            # Text widget para mostrar resultados
            texto_resultados = tk.Text(frame_resultados, wrap=tk.WORD, width=80, height=20)
            texto_resultados.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            
            # Scrollbar
            scrollbar = ttk.Scrollbar(frame_resultados, command=texto_resultados.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            texto_resultados.config(yscrollcommand=scrollbar.set)
            
            # Función para analizar al cambiar la selección
            def analizar_seleccion(event=None):
                seleccion = combo_tipo.get()
                tipo_mov = None
                
                if seleccion != "Todos los tipos":
                    tipo_mov = seleccion.split(" - ")[0]
                
                texto_resultados.delete(1.0, tk.END)
                texto_resultados.insert(tk.END, f"Analizando movimientos tipo: {seleccion}...\n\n")
                
                # Redireccionar stdout al widget Text
                import io
                import sys
                stdout_original = sys.stdout
                sys.stdout = io.StringIO()
                
                # Ejecutar consulta
                df = conector.consultar_ejemplos_movtos(tipo_mov)
                
                # Obtener la salida
                output = sys.stdout.getvalue()
                sys.stdout = stdout_original
                
                # Mostrar resultado
                texto_resultados.insert(tk.END, output)
                
                if not df.empty:
                    # Mostrar también la estructura recomendada
                    texto_resultados.insert(tk.END, "\n\n=== CAMPOS RECOMENDADOS PARA ESTE TIPO DE MOVIMIENTO ===\n")
                    
                    if tipo_mov == "01":  # Alta
                        texto_resultados.insert(tk.END, "NOTA: Las altas deben procesarse con la herramienta de carga de asegurados.\n")
                    
                    elif tipo_mov == "02":  # Baja
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la baja\n\n")
                        texto_resultados.insert(tk.END, "Notas especiales:\n")
                        texto_resultados.insert(tk.END, "- Se actualiza FEC_BAJ en Asegura\n")
                        texto_resultados.insert(tk.END, "- Si tiene crédito, se actualiza Fec_FinDsc en Asegura\n")
                        texto_resultados.insert(tk.END, "- Se genera automáticamente un movimiento tipo 16 si tiene crédito\n")
                        texto_resultados.insert(tk.END, "- Se actualiza TIP_INC con el contador secuencial de bajas\n")
                    
                    elif tipo_mov == "07":  # Modificación Salarial
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la modificación\n")
                        texto_resultados.insert(tk.END, "- SALARIO: Nuevo salario diario integrado\n")
                    
                    elif tipo_mov == "08":  # Reingreso
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha del reingreso\n")
                        texto_resultados.insert(tk.END, "- SALARIO: Salario diario integrado (opcional)\n\n")
                        texto_resultados.insert(tk.END, "Notas especiales:\n")
                        texto_resultados.insert(tk.END, "- Se establece FEC_BAJ a NULL en Asegura\n")
                    
                    elif tipo_mov == "09":  # Aportación Voluntaria
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la aportación\n")
                        texto_resultados.insert(tk.END, "- VALOR_APORTACION: Monto de la aportación\n")
                    
                    elif tipo_mov in ["11", "12"]:  # Ausentismo/Incapacidad
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de inicio\n")
                        texto_resultados.insert(tk.END, "- FECHA_FIN: Fecha de finalización\n")
                        texto_resultados.insert(tk.END, "- NUM_DIAS: Número de días\n")
                        if tipo_mov == "12":  # Incapacidad
                            texto_resultados.insert(tk.END, "- FOL_INC: Folio de incapacidad\n")
                            texto_resultados.insert(tk.END, "- TIPO_RIESGO: Tipo de riesgo\n")
                    
                    elif tipo_mov == "15":  # Inicio de Crédito de Vivienda
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de inicio del crédito\n")
                        texto_resultados.insert(tk.END, "- TIPO_DESCUENTO: Tipo de descuento (1=Porcentaje, 2=Cuota Fija, 3=Factor)\n")
                        texto_resultados.insert(tk.END, "- VALOR_DESCUENTO: Valor del descuento\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO: Número de crédito (opcional)\n\n")
                        texto_resultados.insert(tk.END, "Notas especiales:\n")
                        texto_resultados.insert(tk.END, "- Se actualiza TIP_DSC, VAL_DSC y FEC_DSC en Asegura\n")
                        texto_resultados.insert(tk.END, "- Se genera un número de crédito automáticamente si no se proporciona\n")
                    
                    elif tipo_mov == "16":  # Fecha de Suspensión de Descuento
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de suspensión del descuento\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO: Número de crédito (opcional)\n")
                    
                    elif tipo_mov == "17":  # Reinicio de Descuento
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de reinicio del descuento\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO: Número de crédito (opcional)\n")
                    
                    elif tipo_mov == "18":  # Modificación de Tipo de Descuento
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la modificación\n")
                        texto_resultados.insert(tk.END, "- TIPO_DESCUENTO: Nuevo tipo de descuento (1=Porcentaje, 2=Cuota Fija, 3=Factor)\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO: Número de crédito (opcional)\n")
                    
                    elif tipo_mov == "19":  # Modificación de Valor de Descuento
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la modificación\n")
                        texto_resultados.insert(tk.END, "- VALOR_DESCUENTO: Nuevo valor del descuento\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO: Número de crédito (opcional)\n")
                    
                    elif tipo_mov == "20":  # Modificación de Número de Crédito
                        texto_resultados.insert(tk.END, "Campos obligatorios:\n")
                        texto_resultados.insert(tk.END, "- REG_PATR: Registro patronal\n")
                        texto_resultados.insert(tk.END, "- NUM_AFIL: Número de seguridad social\n")
                        texto_resultados.insert(tk.END, "- FECHA_MOVIMIENTO: Fecha de la modificación\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO_NUEVO: Nuevo número de crédito\n")
                        texto_resultados.insert(tk.END, "- NUMERO_CREDITO_ANTERIOR: Número de crédito anterior (opcional)\n")
            
            # Botón para analizar
            ttk.Button(frame_opciones, text="Analizar", command=analizar_seleccion).pack(side=tk.LEFT, padx=10)
            
            # Analizar inicialmente todos los tipos
            analizar_seleccion()
            
            # Enlazar cambio en combobox
            combo_tipo.bind("<<ComboboxSelected>>", analizar_seleccion)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error al analizar movimientos: {str(e)}")

    def dividir_incapacidad_por_mes(self, datos_mov):
        """Divide una incapacidad en registros mensuales si es necesario"""
        fecha_inicio = pd.to_datetime(datos_mov['FEC_INIC'])
        total_dias = int(datos_mov['NUM_DIAS'])
        
        registros = []
        fecha_actual = fecha_inicio
        dias_restantes = total_dias
        contador = 1
        
        while dias_restantes > 0:
            # Obtener último día del mes actual
            ultimo_dia_mes = fecha_actual.replace(day=1) + pd.offsets.MonthEnd(0)
            # Calcular días hasta fin de mes
            dias_este_mes = min(
                dias_restantes,
                (ultimo_dia_mes - fecha_actual).days + 1
            )
            
            # Formar CON_SEC según el patrón observado
            if contador == 1:
                con_sec = f"1{dias_este_mes:02d}"  # Primera parte usa 1 como prefijo
            else:
                con_sec = f"2{dias_este_mes:02d}"  # Partes subsecuentes usan 2 como prefijo
            
            registros.append({
                'fecha_inicio': fecha_actual,
                'num_dias': dias_este_mes,
                'con_sec': con_sec
            })
            
            dias_restantes -= dias_este_mes
            fecha_actual = ultimo_dia_mes + pd.Timedelta(days=1)
            contador += 1
        
        return registros

    def procesar_incapacidad(self, datos_mov):
        """Procesa un movimiento de incapacidad (tipo 12)"""
        try:
            logging.info(f"Iniciando procesamiento de incapacidad para {datos_mov['NUM_AFIL']}")
            
            # Validar campos requeridos básicos
            campos_requeridos = [
                'REG_PATR', 'NUM_AFIL', 'TIP_MOVS', 'FEC_INIC', 
                'RAMA_SEGURO', 'CONTROL_INCAPACIDAD', 'FOL_INC', 'NUM_DIAS'
            ]
            for campo in campos_requeridos:
                if campo not in datos_mov or not datos_mov[campo]:
                    raise ValueError(f"Falta el campo requerido: {campo}")

            # Validar rama de seguro y sus campos específicos
            rama_seguro = datos_mov['RAMA_SEGURO'].split()[0]  # Obtener solo el número
            logging.info(f"Rama de seguro: {rama_seguro}")
            
            if rama_seguro == '1':  # Riesgos de Trabajo
                if 'TIPO_RIESGO' not in datos_mov or not datos_mov['TIPO_RIESGO']:
                    raise ValueError("Para Riesgos de Trabajo se requiere TIPO_RIESGO")
                if 'CONSECUENCIA' not in datos_mov or not datos_mov['CONSECUENCIA']:
                    raise ValueError("Para Riesgos de Trabajo se requiere CONSECUENCIA")
                
                # Validar porcentaje para valuaciones
                control = datos_mov['CONTROL_INCAPACIDAD'].split()[0]
                if control == '5':  # Valuación
                    if 'PORCENTAJE' not in datos_mov or not datos_mov['PORCENTAJE']:
                        raise ValueError("Para valuaciones se requiere PORCENTAJE")

            # Validar combinaciones permitidas
            self.validar_combinacion_incapacidad(datos_mov)

            # Validar que no haya empalme de fechas
            fecha_inicio = pd.to_datetime(datos_mov['FEC_INIC'])
            num_dias = int(datos_mov['NUM_DIAS'])
            
            cursor = self.conn.cursor()
            query = """
            SELECT FEC_INIC, NUM_DIAS 
            FROM Movtos 
            WHERE REG_PATR = ? 
            AND NUM_AFIL = ? 
            AND TIP_MOVS = '12'
            AND (
                (FEC_INIC <= ? AND DATEADD(day, NUM_DIAS-1, FEC_INIC) >= ?)
                OR (? <= DATEADD(day, NUM_DIAS-1, FEC_INIC) AND ? >= FEC_INIC)
            )
            """
            fecha_fin = fecha_inicio + pd.Timedelta(days=num_dias-1)
            cursor.execute(query, (
                datos_mov['REG_PATR'], 
                datos_mov['NUM_AFIL'], 
                fecha_fin.strftime('%Y-%m-%d'),
                fecha_inicio.strftime('%Y-%m-%d'),
                fecha_inicio.strftime('%Y-%m-%d'),
                fecha_fin.strftime('%Y-%m-%d')
            ))
            
            if cursor.fetchone():
                raise ValueError("Ya existe una incapacidad que se empalma con las fechas proporcionadas")

            # Dividir por meses si es necesario
            registros = self.dividir_incapacidad_por_mes(datos_mov)
            logging.info(f"Registros divididos por mes: {len(registros)}")
            
            # Insertar registros
            for reg in registros:
                logging.info(f"Procesando registro con CON_SEC: {reg['con_sec']}")
                
                # Preparar datos para Movtos
                datos_movto = {
                    'REG_PATR': datos_mov['REG_PATR'],
                    'NUM_AFIL': datos_mov['NUM_AFIL'],
                    'TIP_MOVS': '12',
                    'FEC_INIC': reg['fecha_inicio'].strftime('%Y-%m-%d'),
                    'NUM_DIAS': reg['num_dias'],
                    'FOL_INC': datos_mov['FOL_INC'],
                    'CVE_MOVS': 'C',  # C para incapacidad
                    'EDO_MOV': 0,
                    'CON_SEC': reg['con_sec'],
                    'TIP_RIE': rama_seguro,
                    'SAL_MOVT': 0,
                    'SAL_MOVT2': 0,
                    'SAL_MOVT3': 0,
                    'ART_33': 'N'
                }
                
                # Insertar en Movtos
                query_movtos = """
                INSERT INTO Movtos (
                    REG_PATR, NUM_AFIL, TIP_MOVS, FEC_INIC, CON_SEC, 
                    NUM_DIAS, SAL_MOVT, SAL_MOVT2, FOL_INC, CVE_MOVS, 
                    SAL_MOVT3, EDO_MOV, ART_33, TIP_RIE
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                try:
                    cursor.execute(query_movtos, (
                        datos_movto['REG_PATR'],
                        datos_movto['NUM_AFIL'],
                        datos_movto['TIP_MOVS'],
                        datos_movto['FEC_INIC'],
                        datos_movto['CON_SEC'],
                        datos_movto['NUM_DIAS'],
                        datos_movto['SAL_MOVT'],
                        datos_movto['SAL_MOVT2'],
                        datos_movto['FOL_INC'],
                        datos_movto['CVE_MOVS'],
                        datos_movto['SAL_MOVT3'],
                        datos_movto['EDO_MOV'],
                        datos_movto['ART_33'],
                        datos_movto['TIP_RIE']
                    ))
                    logging.info(f"Registro insertado en Movtos")
                except Exception as e:
                    logging.error(f"Error al insertar en Movtos: {str(e)}")
                    raise

                # Si es el primer registro, insertar en Incapacidades
                if reg == registros[0]:
                    logging.info("Preparando inserción en Incapacidades")
                    # Determinar Grupo_Inc basado en la rama de seguro y tipo de riesgo
                    grupo_inc = '0000001'  # Valor por defecto
                    if rama_seguro == '1':  # Riesgos de Trabajo
                        tipo_riesgo = datos_mov.get('TIPO_RIESGO', '').split()[0]
                        if tipo_riesgo == '2':  # Accidente de Trayecto
                            grupo_inc = '0000002'
                    
                    logging.info(f"Grupo_Inc determinado: {grupo_inc}")
                    
                    # Preparar datos para Incapacidades
                    query_incapacidades = """
                    INSERT INTO Incapacidades (
                        Reg_Pat, Num_Afi, Fec_Acc, Fol_Inc, Grupo_Inc,
                        Rec_Rev, Consecuencia, Tip_Rie, Ram_Seg, Secuela,
                        Con_Inc, Dia_Sub, Por_Inc, Ind_Def, Fec_Ter
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    consecuencia = datos_mov.get('CONSECUENCIA', '').split()[0] if 'CONSECUENCIA' in datos_mov else ''
                    porcentaje = float(datos_mov.get('PORCENTAJE', 0)) if 'PORCENTAJE' in datos_mov else 0.0
                    control = datos_mov['CONTROL_INCAPACIDAD'].split()[0]
                    
                    try:
                        cursor.execute(query_incapacidades, (
                            datos_mov['REG_PATR'],
                            datos_mov['NUM_AFIL'],
                            fecha_inicio.strftime('%Y-%m-%d'),
                            datos_mov['FOL_INC'],
                            grupo_inc,
                            '',  # rec_rev
                            consecuencia,
                            datos_mov.get('TIPO_RIESGO', ''),  # Mantener el texto completo del tipo de riesgo
                            datos_mov['RAMA_SEGURO'],  # Mantener el texto completo de la rama
                            consecuencia if rama_seguro == '1' else '',  # Secuela solo para Riesgos de Trabajo
                            control,
                            num_dias,
                            porcentaje,
                            'No',
                            fecha_fin.strftime('%Y-%m-%d')
                        ))
                        logging.info("Registro insertado en Incapacidades")
                    except Exception as e:
                        logging.error(f"Error al insertar en Incapacidades: {str(e)}")
                        raise

            self.conn.commit()
            logging.info("Transacción completada exitosamente")
            return True

        except Exception as e:
            logging.error(f"Error al procesar incapacidad: {str(e)}")
            if 'cursor' in locals():
                self.conn.rollback()
            raise

    def validar_combinacion_incapacidad(self, datos_mov):
        """Valida que la combinación de rama, tipo, consecuencia y control sea válida"""
        rama = datos_mov['RAMA_SEGURO'].split()[0]
        control = datos_mov['CONTROL_INCAPACIDAD'].split()[0]
        
        if rama == '1':  # Riesgos de Trabajo
            tipo = datos_mov['TIPO_RIESGO'].split()[0]
            consecuencia = datos_mov['CONSECUENCIA'].split()[0]
            
            # Validar tipo de riesgo
            if tipo not in ['1', '2', '3']:
                raise ValueError("Tipo de riesgo inválido para Riesgos de Trabajo")
            
            # Validar consecuencia
            if consecuencia not in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']:
                raise ValueError("Consecuencia inválida para Riesgos de Trabajo")
            
            # Validar control según consecuencia
            controles_validos = {
                '0': ['0'],
                '1': ['1', '2', '3', '4'],
                '2': ['5'],
                '3': ['5'],
                '4': ['6'],
                '5': ['1', '2', '3', '4'],
                '6': ['5'],
                '7': ['5'],
                '8': ['1', '2', '3', '4'],
                '9': ['5']
            }
            
            if control not in controles_validos[consecuencia]:
                raise ValueError(f"Control de incapacidad inválido para la consecuencia {consecuencia}")
                
        elif rama == '2':  # Enfermedad General
            if control not in ['1', '2', '3', '4']:
                raise ValueError("Control de incapacidad inválido para Enfermedad General")
                
        elif rama == '3':  # Maternidad
            if control not in ['7', '8', '9']:
                raise ValueError("Control de incapacidad inválido para Maternidad")
                
        elif rama == '4':  # Licencia 140 Bis
            if control != '0':
                raise ValueError("Control de incapacidad inválido para Licencia 140 Bis")
        else:
            raise ValueError("Rama de seguro inválida")

if __name__ == "__main__":
    root = tk.Tk()
    app = CargaMasivaMovimientos(root)
    root.mainloop() 