"""
Script para probar las validaciones de suspensión y reinicio de crédito.
"""

from conectores.conector_sua import ConectorSUA
import datetime

def main():
    # Conectar a la base de datos con contraseña
    conector = ConectorSUA(password="S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    # Datos de prueba - Asegurado sin crédito activo
    reg_patr = "E5352621109"
    num_afil = "01234567890"  # NSS que no existe o no tiene crédito
    
    print("\nProbando suspensión sin inicio de crédito previo...")
    datos_suspension = {
        'REG_PATR': reg_patr,
        'NUM_AFIL': num_afil,
        'FEC_INIC': datetime.datetime.now().strftime('%Y-%m-%d'),
        'TIP_MOVS': '16'  # Suspensión de crédito
    }
    
    # Esta operación debería fallar porque no hay un inicio de crédito previo
    resultado = conector.procesar_suspension_credito(datos_suspension)
    print(f"Resultado esperado (False): {resultado}")
    
    print("\nProbando reinicio sin suspensión previa...")
    datos_reinicio = {
        'REG_PATR': reg_patr,
        'NUM_AFIL': num_afil,
        'FEC_INIC': datetime.datetime.now().strftime('%Y-%m-%d'),
        'TIP_MOVS': '17'  # Reinicio de crédito
    }
    
    # Esta operación debería fallar porque no hay una suspensión previa
    resultado = conector.procesar_reinicio_credito(datos_reinicio)
    print(f"Resultado esperado (False): {resultado}")
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 