import pyodbc
import os

def examinar_tabla_uma():
    try:
        # Configurar la conexión
        ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
        password = "S5@N52V49"
        ruta_normalizada = os.path.normpath(ruta_bd)
        
        # Cadena de conexión
        connection_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={ruta_normalizada};'
            f'PWD={password};'
        )
        
        # Conectar a la base de datos
        conn = pyodbc.connect(connection_str)
        cursor = conn.cursor()
        
        # Obtener información de la tabla
        print("\n--- Información de la tabla UMA ---")
        
        # 1. Obtener columnas
        cursor.execute("SELECT * FROM UMA WHERE 1=0")  # Esto nos dará la estructura sin datos
        columns = [column[0] for column in cursor.description]
        print("\nColumnas:")
        for col in columns:
            print(f"- {col}")
            
        # 2. Obtener algunos datos de ejemplo
        print("\nDatos de ejemplo:")
        cursor.execute("SELECT TOP 5 * FROM UMA")
        for row in cursor.fetchall():
            print(row)
            
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    examinar_tabla_uma() 