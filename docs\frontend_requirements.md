# Requerimientos del Frontend

## 1. Componentes Necesarios

### a) Configuración Inicial (ConfiguracionSUA.tsx)
```typescript
interface ConfiguracionSUAForm {
  rutaSua: string;
  regPat: string;
}

interface EstadoConexion {
  conectado: boolean;
  mensaje: string;
  detalles: {
    problemas: string[];
    ejemplo_reg_pat?: string;
    tablas: string[];
    reg_pat_valido?: boolean;
  };
  timestamp: string;
  reg_pat: string | null;
  version_sua: string | null;
}
```
- Formulario para configuración inicial
- Selector de archivo .mdb (con validación de extensión)
- Campo para registro patronal con autocompletado del formato correcto (R + 10 dígitos)
- Botón para probar conexión
- Indicador de estado de la conexión
- Panel de resultados de prueba de conexión mostrando:
  - Estado de conexión
  - Tablas disponibles
  - Validez del registro patronal
  - Errores encontrados (si los hay)

### b) Layout Principal (Layout.tsx)
- Barra de navegación
- Sidebar con menú de opciones
- Área principal de contenido
- Footer con información de la aplicación

### c) Panel de Control (Dashboard.tsx)
- Resumen de configuración actual
- Estado de la conexión
- Accesos rápidos a funciones principales

## 2. Endpoints a Consumir

```typescript
// Configuración y prueba de conexión SUA
POST /api/v1/conexion/test-conexion
Body: {
  ruta_sua: string;  // Ruta al archivo .mdb
  reg_pat: string;   // Formato: R + 10 dígitos
}
Response: {
  estado: {
    conectado: boolean;
    mensaje: string;
    detalles: {
      problemas: string[];
      ejemplo_reg_pat?: string;
      tablas: string[];
      reg_pat_valido?: boolean;
    };
    timestamp: string;
    reg_pat: string | null;
    version_sua: string | null;
  };
  metadata?: Record<string, any>;
}

// Resto de endpoints...
```

## 3. Manejo de Estado

```typescript
interface SUAConfig {
  rutaConfigured: boolean;
  rutaSua: string;
  regPat: string;
  lastConnection: Date;
  estadoConexion: EstadoConexion;
}
```
- Usar localStorage para persistir configuración
- Estado global (Context o Redux) para configuración activa
- Mantener estado de última conexión exitosa

## 4. Validaciones del Cliente

```typescript
const validaciones = {
  rutaSua: (ruta: string) => {
    if (!ruta) return "La ruta es requerida";
    if (!ruta.toLowerCase().endsWith('.mdb')) 
      return "Debe ser un archivo .mdb";
    return "";
  },
  regPat: (regPat: string) => {
    if (!regPat) return "El registro patronal es requerido";
    const regPatFormat = /^R\d{10}$/;
    if (!regPatFormat.test(regPat))
      return "Formato inválido. Debe ser R seguido de 10 dígitos";
    return "";
  }
};
```

## 5. Interfaz de Usuario

```css
/* Estilos Principales */
:root {
  --primary-color: #1976d2;
  --secondary-color: #424242;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
}
```

- Diseño responsivo
- Tema claro/oscuro
- Feedback visual para acciones
- Mensajes de error/éxito claros

## 6. Flujo de Usuario

```mermaid
graph TD
  A[Inicio] --> B{¿Configurado?}
  B -->|No| C[Formulario Configuración]
  B -->|Sí| D[Dashboard]
  C --> E[Validar Conexión]
  E -->|Error| C
  E -->|Éxito| D
```

## 7. Gestión de Errores

```typescript
interface ErrorResponse {
  status: number;
  detail: string;
  timestamp: string;
}
```
- Mensajes de error amigables
- Retry automático para errores de conexión
- Logging de errores

## 8. Dependencias Recomendadas

```json
{
  "dependencies": {
    "@mui/material": "^5.x",
    "@mui/icons-material": "^5.x",
    "react-query": "^3.x",
    "axios": "^1.x",
    "react-hook-form": "^7.x",
    "yup": "^1.x"
  }
}
```

## 9. Seguridad

- Validación de rutas en el cliente
- Sanitización de inputs
- Manejo seguro de rutas de archivo
- No almacenar información sensible

## 10. Características Adicionales

- Autocompletado de rutas comunes del SUA
- Historial de configuraciones previas
- Verificación periódica de conexión
- Modo offline para carga de datos

## 11. Notas de Implementación

### Prioridades de Desarrollo
1. Configuración inicial y conexión con SUA
2. Validaciones y manejo de errores
3. Interfaz de usuario y experiencia de usuario
4. Características adicionales

### Consideraciones Técnicas
- Asegurar compatibilidad con diferentes versiones de Windows
- Optimizar para uso local
- Mantener el código modular y reutilizable
- Documentar componentes y funciones principales 