"""
Script para probar diferentes formas de consulta SQL con pyodbc y Microsoft Access
para encontrar la forma correcta de construir consultas.
"""

import pyodbc
import traceback

def probar_consultas_access():
    try:
        # Configurar la conexión
        ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
        password = "S5@N52V49"
        
        # Normalizar la ruta para evitar problemas con secuencias de escape
        import os
        ruta_normalizada = os.path.normpath(ruta_bd)
        
        # Cadena de conexión
        connection_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={ruta_normalizada};'
            f'PWD={password};'
        )
        
        # Intentar conectar
        conn = pyodbc.connect(connection_str)
        print("✅ Conexión exitosa")
        
        # 1. Prueba 1: SELECT simple
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 1: SELECT simple ---")
            query = "SELECT TOP 1 * FROM Asegura"
            cursor.execute(query)
            row = cursor.fetchone()
            print(f"Resultado: {row}")
        except Exception as e:
            print(f"Error en prueba 1: {str(e)}")
        
        # 2. Prueba 2: SELECT con WHERE
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 2: SELECT con WHERE ---")
            query = "SELECT TOP 1 * FROM Asegura WHERE 1=1"
            cursor.execute(query)
            row = cursor.fetchone()
            print(f"Resultado: {row}")
        except Exception as e:
            print(f"Error en prueba 2: {str(e)}")
        
        # 3. Prueba 3: SELECT con parámetro normal
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 3: SELECT con parámetro normal ---")
            value = 1
            query = "SELECT TOP 1 * FROM Asegura WHERE 1=?"
            cursor.execute(query, (value,))
            row = cursor.fetchone()
            print(f"Resultado: {row}")
        except Exception as e:
            print(f"Error en prueba 3: {str(e)}")
        
        # 4. Prueba 4: SELECT con parámetros múltiples
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 4: SELECT con parámetros múltiples ---")
            reg_patr = "E5352621109"
            num_afil = "04088625456"
            query = "SELECT TOP 1 * FROM Asegura WHERE REG_PATR = ? AND NUM_AFIL = ?"
            cursor.execute(query, (reg_patr, num_afil))
            row = cursor.fetchone()
            print(f"Resultado: {row}")
        except Exception as e:
            print(f"Error en prueba 4: {str(e)}")
        
        # 5. Prueba 5: SELECT con valores directos
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 5: SELECT con valores directos ---")
            reg_patr = "E5352621109"
            num_afil = "04088625456"
            query = f"SELECT TOP 1 * FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            cursor.execute(query)
            row = cursor.fetchone()
            print(f"Resultado: {row}")
        except Exception as e:
            print(f"Error en prueba 5: {str(e)}")
        
        # 6. Prueba 6: UPDATE con parámetros
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 6: UPDATE con parámetros ---")
            tipo_dsc = 3
            reg_patr = "E5352621109"
            num_afil = "04088625456"
            # Primero verificamos si existe el registro
            check_query = f"SELECT COUNT(*) FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            cursor.execute(check_query)
            count = cursor.fetchone()[0]
            print(f"Registros encontrados: {count}")
            
            if count > 0:
                update_query = "UPDATE Asegura SET TIP_DSC = ? WHERE REG_PATR = ? AND NUM_AFIL = ?"
                cursor.execute(update_query, (tipo_dsc, reg_patr, num_afil))
                conn.commit()
                print(f"Filas actualizadas: {cursor.rowcount}")
            else:
                print("No se encontró el registro para actualizar")
        except Exception as e:
            print(f"Error en prueba 6: {str(e)}")
        
        # 7. Prueba 7: UPDATE con valores directos
        cursor = conn.cursor()
        try:
            print("\n--- Prueba 7: UPDATE con valores directos ---")
            tipo_dsc = 3
            reg_patr = "E5352621109"
            num_afil = "04088625456"
            update_query = f"UPDATE Asegura SET TIP_DSC = {tipo_dsc} WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
            cursor.execute(update_query)
            conn.commit()
            print(f"Filas actualizadas: {cursor.rowcount}")
        except Exception as e:
            print(f"Error en prueba 7: {str(e)}")
        
        # Cerrar conexión
        conn.close()
        print("\n✅ Pruebas completadas")
    
    except Exception as e:
        print(f"Error general: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    probar_consultas_access() 