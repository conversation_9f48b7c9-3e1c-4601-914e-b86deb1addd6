"""
Script para verificar el estado del crédito de un asegurado después de 
operaciones de suspensión y reinicio.
"""

from conectores.conector_sua import ConectorSUA
import pandas as pd

def main():
    # Conectar a la base de datos
    conector = ConectorSUA(password="S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    # Consultar datos de asegurado específico
    reg_patr = "E5352621109"
    num_afil = "04088625456"  # Este es el NSS utilizado en las pruebas
    
    print(f"\nConsultando datos del asegurado {num_afil} en el registro patronal {reg_patr}...")
    
    # Ejecutar consulta SQL para obtener datos de Asegura
    query_asegura = f"""
    SELECT REG_PATR, NUM_AFIL, TIP_DSC, VAL_DSC, FEC_DSC, PAG_INFO, SAL_IMSS
    FROM Asegura 
    WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
    """
    
    resultados_asegura = conector.ejecutar_consulta(query_asegura)
    
    if not resultados_asegura:
        print(f"No se encontró el asegurado {num_afil}")
    else:
        print("\n=== Datos del crédito en la tabla Asegura ===")
        for campo, valor in resultados_asegura[0].items():
            print(f"{campo}: {valor}")
        
        # Verificar específicamente los campos de interés
        asegurado = resultados_asegura[0]
        if asegurado.get('FEC_DSC'):
            print(f"\n✅ El asegurado tiene suspensión de crédito desde: {asegurado['FEC_DSC']}")
        else:
            print("\n⚠️ El asegurado no tiene suspensión de crédito activa (FEC_DSC es NULL)")
        
        if asegurado.get('TIP_DSC'):
            print(f"✅ El asegurado tiene tipo de descuento: {asegurado['TIP_DSC']}")
        else:
            print("❌ El asegurado no tiene tipo de descuento configurado")
    
    # Consultar movimientos relacionados con crédito
    query_movimientos = f"""
    SELECT TIP_MOVS, FEC_INIC, CVE_MOVS, Num_Cre, Tip_Des, Val_Des, Tab_Dism
    FROM Movtos 
    WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
    AND TIP_MOVS IN ('15', '16', '17')
    ORDER BY FEC_INIC DESC
    """
    
    movimientos = conector.ejecutar_consulta(query_movimientos)
    
    if not movimientos:
        print("\nNo se encontraron movimientos de crédito")
    else:
        print("\n=== Movimientos de crédito (últimos primero) ===")
        for i, mov in enumerate(movimientos, 1):
            tipo_texto = {
                '15': 'Inicio de crédito',
                '16': 'Suspensión de crédito',
                '17': 'Reinicio de crédito'
            }.get(mov['TIP_MOVS'], 'Desconocido')
            
            fecha = pd.to_datetime(mov['FEC_INIC']).strftime('%d/%m/%Y') if mov['FEC_INIC'] else 'N/A'
            
            print(f"\nMovimiento {i}: {tipo_texto} - Fecha: {fecha}")
            print(f"  Número de crédito: {mov['Num_Cre']}")
            print(f"  Tipo de descuento: {mov['Tip_Des']}")
            print(f"  Valor de descuento: {mov['Val_Des']}")
            print(f"  Tabla de disminución: {mov['Tab_Dism']}")
    
    # Desconectar
    conector.desconectar()

if __name__ == "__main__":
    main() 