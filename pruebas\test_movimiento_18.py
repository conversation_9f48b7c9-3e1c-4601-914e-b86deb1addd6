"""
Script de prueba para el movimiento tipo 18 (Modificación de Tipo de Descuento)
Este script crea un archivo Excel de prueba y luego intenta procesar
el movimiento para validar la implementación.
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from conectores.conector_sua import ConectorSUA

def crear_excel_prueba():
    """Crea un archivo Excel con datos de prueba para movimiento tipo 18"""
    
    # Datos de prueba
    datos = [
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': '01/03/2025',
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': '3',  # 3 = Factor de Descuento
            'VALOR_DESCUENTO': 6.59,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Prueba de cambio a Factor de Descuento'
        },
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': '15/03/2025',
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': '1',  # 1 = Porcentaje
            'VALOR_DESCUENTO': 20.0,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Prueba de cambio a Porcentaje'
        },
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': '30/03/2025',
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': 'Factor de Descuento',  # Texto en lugar de código
            'VALOR_DESCUENTO': 8.25,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Prueba de cambio con texto descriptivo'
        }
    ]
    
    # Crear DataFrame
    df = pd.DataFrame(datos)
    
    # Ruta del archivo
    ruta_archivo = os.path.join('pruebas', 'test_movimiento_18.xlsx')
    
    # Guardar a Excel
    df.to_excel(ruta_archivo, index=False)
    
    print(f"Archivo de prueba creado: {ruta_archivo}")
    return ruta_archivo

def verificar_asegurado_bd(reg_patr, num_afil, conector):
    """Verifica si existe el asegurado en la BD"""
    
    # Consulta para verificar existencia del asegurado
    query = f"""
    SELECT a.REG_PATR, a.NUM_AFIL, a.NOM_ASE, a.TIP_DSC, a.VAL_DSC, a.Num_Cre
    FROM Asegura a
    WHERE a.REG_PATR = '{reg_patr}' AND a.NUM_AFIL = '{num_afil}'
    """
    
    # Ejecutar consulta
    df = conector.ejecutar_consulta(query)
    
    if df is not None and not df.empty:
        print(f"Asegurado encontrado en la BD: {df.iloc[0]['NOM_ASE']}")
        print(f"Tipo de descuento actual: {df.iloc[0]['TIP_DSC']}")
        print(f"Valor de descuento actual: {df.iloc[0]['VAL_DSC']}")
        print(f"Número de crédito actual: {df.iloc[0]['Num_Cre']}")
        return True
    else:
        print(f"No se encontró el asegurado con REG_PATR={reg_patr} y NUM_AFIL={num_afil}")
        return False

def procesar_movimiento_18(reg_patr, num_afil, fecha, tipo_descuento, valor_descuento, num_credito, conector):
    """Procesa manualmente un movimiento tipo 18"""
    
    print(f"\nProcesando movimiento tipo 18 para {num_afil}...")
    
    # Preparar datos del movimiento
    datos_mov = {
        'REG_PATR': reg_patr,
        'NUM_AFIL': num_afil,
        'TIP_MOVS': '18',
        'FEC_INIC': fecha.strftime('%Y-%m-%d'),
        'CVE_MOVS': 'C',
        'NUM_DIAS': 0,
        'SAL_MOVT': 350.00,
        'SAL_MOVT2': 0.0,
        'SAL_MOVT3': 0.0,
        'SAL_ANT1': 0.0,
        'SAL_ANT2': 0.0,
        'SAL_ANT3': 0.0,
        'EDO_MOV': 0
    }
    
    # Procesar el tipo de descuento
    tipo_descuento_texto = None
    if isinstance(tipo_descuento, (int, float)) or (isinstance(tipo_descuento, str) and tipo_descuento.strip().isdigit()):
        tipo_descuento_codigo = int(float(tipo_descuento))
        tipo_descuento_texto = {
            1: "Porcentaje",
            2: "Cuota Fija",
            3: "Factor de Descuento"
        }.get(tipo_descuento_codigo)
    else:
        # Es texto, normalizarlo
        tipo_lower = tipo_descuento.lower().strip()
        if 'porcentaje' in tipo_lower:
            tipo_descuento_texto = "Porcentaje"
        elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
            tipo_descuento_texto = "Cuota Fija"
        elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
            tipo_descuento_texto = "Factor de Descuento"
    
    if not tipo_descuento_texto:
        print(f"Error: Tipo de descuento no válido: {tipo_descuento}")
        return False
    
    datos_mov['Tip_Des'] = tipo_descuento_texto
    
    # Agregar valor de descuento si está presente
    if valor_descuento:
        datos_mov['Val_Des'] = float(valor_descuento)
    
    # Agregar número de crédito si está presente
    if num_credito:
        datos_mov['Num_Cre'] = str(num_credito)
    
    print(f"Datos de movimiento preparados: {datos_mov}")
    
    try:
        # Insertar el movimiento
        resultado_insercion = conector.insertar_movimiento(datos_mov)
        if not resultado_insercion:
            print("Error: No se pudo insertar el movimiento")
            return False
        
        # Actualizar el tipo de descuento en la tabla Asegura
        tipo_codigo = {"Porcentaje": 1, "Cuota Fija": 2, "Factor de Descuento": 3}.get(tipo_descuento_texto, 0)
        sql_update = """
        UPDATE Asegura 
        SET TIP_DSC = ? 
        WHERE REG_PATR = ? AND NUM_AFIL = ?
        """
        conector.ejecutar_consulta(sql_update, (tipo_codigo, datos_mov['REG_PATR'], datos_mov['NUM_AFIL']))
        
        print(f"Movimiento tipo 18 procesado correctamente")
        return True
    except Exception as e:
        print(f"Error al procesar movimiento: {str(e)}")
        return False

def verificar_cambios(reg_patr, num_afil, conector):
    """Verifica los cambios en el tipo de descuento del asegurado"""
    
    # Consulta para verificar los datos del asegurado
    query = f"""
    SELECT a.REG_PATR, a.NUM_AFIL, a.NOM_ASE, a.TIP_DSC, a.VAL_DSC, a.Num_Cre
    FROM Asegura a
    WHERE a.REG_PATR = '{reg_patr}' AND a.NUM_AFIL = '{num_afil}'
    """
    
    # Ejecutar consulta
    df = conector.ejecutar_consulta(query)
    
    if df is not None and not df.empty:
        print(f"\nDatos actualizados del asegurado:")
        print(f"Nombre: {df.iloc[0]['NOM_ASE']}")
        print(f"Tipo de descuento: {df.iloc[0]['TIP_DSC']}")
        print(f"Valor de descuento: {df.iloc[0]['VAL_DSC']}")
        print(f"Número de crédito: {df.iloc[0]['Num_Cre']}")
        
        # Consulta para verificar los movimientos
        query_movs = f"""
        SELECT m.TIP_MOVS, m.FEC_INIC, m.CVE_MOVS, m.Tip_Des, m.Val_Des, m.Num_Cre
        FROM Movtos m
        WHERE m.REG_PATR = '{reg_patr}' AND m.NUM_AFIL = '{num_afil}' AND m.TIP_MOVS = '18'
        ORDER BY m.FEC_INIC DESC
        """
        
        df_movs = conector.ejecutar_consulta(query_movs)
        
        if df_movs is not None and not df_movs.empty:
            print(f"\nMovimientos tipo 18 encontrados: {len(df_movs)}")
            for idx, row in df_movs.iterrows():
                print(f"- Fecha: {row['FEC_INIC']}, Tipo Descuento: {row['Tip_Des']}, Valor: {row['Val_Des']}")
            return True
        else:
            print("No se encontraron movimientos tipo 18 para este asegurado")
            return False
    else:
        print(f"No se encontró el asegurado")
        return False

def ejecutar_prueba():
    """Ejecuta la prueba completa"""
    
    # Crear archivo de prueba
    ruta_archivo = crear_excel_prueba()
    
    # Conectar a la BD
    conector = ConectorSUA("C:\\Cobranza\\SUA\\SUA.MDB", "S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    print("\n=== VERIFICACIÓN INICIAL DEL ASEGURADO ===")
    reg_patr = 'E5352621109'
    num_afil = '04088625456'
    
    # Verificar si existe el asegurado
    if not verificar_asegurado_bd(reg_patr, num_afil, conector):
        print("No se puede continuar con la prueba, el asegurado no existe")
        conector.desconectar()
        return
    
    # Leer el archivo de prueba
    df = pd.read_excel(ruta_archivo)
    
    # Procesar cada registro de prueba
    for idx, row in df.iterrows():
        print(f"\n=== PROCESANDO REGISTRO {idx + 1}/{len(df)} ===")
        fecha = pd.to_datetime(row['FECHA_MOVIMIENTO'], format='%d/%m/%Y')
        
        # Procesar el movimiento
        procesar_movimiento_18(
            row['REG_PATR'],
            row['NUM_AFIL'],
            fecha,
            row['TIPO_DESCUENTO'],
            row['VALOR_DESCUENTO'],
            row['NUMERO_CREDITO'],
            conector
        )
    
    print("\n=== VERIFICACIÓN FINAL DEL ASEGURADO ===")
    verificar_cambios(reg_patr, num_afil, conector)
    
    # Desconectar
    conector.desconectar()
    print("\nPrueba finalizada")

if __name__ == "__main__":
    ejecutar_prueba() 