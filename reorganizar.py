import os
import shutil
from pathlib import Path

def crear_directorios():
    """Crear la estructura de directorios necesaria"""
    directorios = [
        'core/conectores',
        'core/modelos',
        'core/interfaces',
        'utils/analisis',
        'utils/pruebas',
        'utils/herramientas',
        'docs',
        'tests',
        'plantillas',
        'ejemplos'
    ]
    
    for directorio in directorios:
        os.makedirs(directorio, exist_ok=True)

def mover_archivos():
    """Mover los archivos a sus nuevas ubicaciones"""
    # Mover conectores
    if os.path.exists('conectores'):
        for archivo in os.listdir('conectores'):
            if not archivo.startswith('__'):  # Ignorar __pycache__ y __init__.py
                try:
                    shutil.move(f'conectores/{archivo}', 'core/conectores/')
                except:
                    print(f"No se pudo mover {archivo}")
    
    # Mover modelos
    if os.path.exists('modelos'):
        for archivo in os.listdir('modelos'):
            if not archivo.startswith('__'):
                try:
                    shutil.move(f'modelos/{archivo}', 'core/modelos/')
                except:
                    print(f"No se pudo mover {archivo}")
    
    # Mover interfaces principales
    interfaces = [
        'carga_masiva_movimientos.py',
        'carga_masiva_asegurados.py',
        'carga_masiva_patrones.py',
        'carga_catalogos.py',
        'carga_masiva_obras.py',
        'actualizar_fraccion_patrones.py'
    ]
    for interfaz in interfaces:
        if os.path.exists(interfaz):
            try:
                shutil.move(interfaz, 'core/interfaces/')
            except:
                print(f"No se pudo mover {interfaz}")
    
    # Mover scripts de análisis
    analisis = [
        'explorar_bd_sua.py',
        'explorar_tablas_principales.py',
        'explorar_tabla_movtos.py',
        'explorar_tabla_patron.py',
        'examinar_bajas.py',
        'examinar_tabla.py',
        'examinar_tablas.py',
        'examinar_tabla_uma.py',
        'explorar_tablas.py'
    ]
    for script in analisis:
        if os.path.exists(script):
            try:
                shutil.move(script, 'utils/analisis/')
            except:
                print(f"No se pudo mover {script}")
    
    # Mover scripts de prueba
    pruebas = [
        'test_conexion.py',
        'test_baja.py',
        'test_baja_fix.py',
        'test_reinicio.py',
        'test_movimientos.py',
        'test_tipo_18.py',
        'test_tipo_18_debug.py',
        'test_access_odbc.py',
        'test_tablas.py',
        'probar_reingreso.py',
        'probar_carga_masiva.py',
        'probar_carga_patrones.py',
        'probar_carga_creditos.py',
        'probar_actualizacion_fraccion.py',
        'probar_carga_desde_archivo.py',
        'probar_baja_carga_masiva.py',
        'prueba_creditos.py',
        'prueba_creditos_2.py',
        'prueba_mov19.py',
        'prueba_validaciones.py',
        'prueba_simple.py',
        'prueba_directa.py',
        'prueba_crear_xlsx.py'
    ]
    for prueba in pruebas:
        if os.path.exists(prueba):
            try:
                shutil.move(prueba, 'utils/pruebas/')
            except:
                print(f"No se pudo mover {prueba}")
    
    # Mover utilidades
    herramientas = [
        'verificar_credito.py',
        'ver_datos_asegurado.py',
        'verificar_campos_asegura.py',
        'listar_columnas.py',
        'corregir_asegurado.py',
        'limpiar_pruebas.py',
        'consultar_movtos.py',
        'consultar_patron.py',
        'consulta_asegura.py',
        'crear_archivo_test.py',
        'crear_archivo_test_creditos.py',
        'crear_excel_reinicio.py',
        'crear_excel_asegurados_prueba.py',
        'ejemplo_excel.py',
        'ver_columnas_asegura.py',
        'fix_conector_sua.py',
        'actualizar_fraccion.py'
    ]
    for herramienta in herramientas:
        if os.path.exists(herramienta):
            try:
                shutil.move(herramienta, 'utils/herramientas/')
            except:
                print(f"No se pudo mover {herramienta}")
    
    # Mover documentación
    docs = [
        'DOCUMENTACION_PROYECTO.md',
        'README.md',
        'README_movimientos.md',
        'README_carga_masiva.md',
        'README-desarrollo.md',
        'README_actualizacion_fraccion.md',
        'arquitectura_tecnica.md',
        'modelo_datos.md',
        'requisitos_tecnicos.md',
        'roadmap.md',
        'funcionalidades.md',
        'resumen_cambios.md',
        'analisis_mercado.md',
        'modelo_negocio.md',
        'proyecto_sua_vision.md',
        'RESUMEN_CONVERSACION.md'
    ]
    for doc in docs:
        if os.path.exists(doc):
            try:
                shutil.move(doc, 'docs/')
            except:
                print(f"No se pudo mover {doc}")
    
    # Mover plantillas y archivos Excel
    plantillas = [
        'Plantilla_SALARIO_20250425.xlsx',
        'Plantilla_INPC_20250425.xlsx',
        'Plantilla_UMI_20250425.xlsx',
        'Plantilla_UMA_20250425.xlsx',
        'plantillas_catalogos.py'
    ]
    for plantilla in plantillas:
        if os.path.exists(plantilla):
            try:
                shutil.move(plantilla, 'plantillas/')
            except:
                print(f"No se pudo mover {plantilla}")
    
    # Mover archivos de ejemplo
    ejemplos = [
        'carga_masiva_movimientos_ejemplo.xlsx',
        'carga_masiva_trabajadores_ejemplo.xlsx',
        'carga_masiva_patrones_ejemplo.xlsx',
        'test_reinicio_credito.xlsx',
        'test_bajas_reingresos.xlsx',
        'carga_asegurados_prueba.xlsx',
        'carga_patrones_prueba.xlsx',
        'prueba_fechas.xlsx',
        'Prima_RT_datos.csv',
        'Patron_datos.csv'
    ]
    for ejemplo in ejemplos:
        if os.path.exists(ejemplo):
            try:
                shutil.move(ejemplo, 'ejemplos/')
            except:
                print(f"No se pudo mover {ejemplo}")

def main():
    print("Creando estructura de directorios...")
    crear_directorios()
    
    print("Moviendo archivos a sus nuevas ubicaciones...")
    mover_archivos()
    
    print("¡Reorganización completada!")

if __name__ == "__main__":
    main() 