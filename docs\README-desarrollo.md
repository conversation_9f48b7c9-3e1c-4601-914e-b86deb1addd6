# Instrucciones de Desarrollo para SUA-Tool

## Configuración del Entorno

### Requisitos previos
- Python 3.10 o superior
- Base de datos SUA.MDB instalada en C:\Cobranza\SUA\SUA.MDB
- Drivers ODBC de Microsoft Access

### Instalación de dependencias

1. Crear un entorno virtual:
```
python -m venv venv
```

2. Activar el entorno virtual:
- En Windows:
```
venv\Scripts\activate
```
- En macOS/Linux:
```
source venv/bin/activate
```

3. Instalar dependencias:
```
pip install -r requirements.txt
```

### Configuración del driver ODBC

Para que la conexión a la base de datos funcione correctamente, necesitas tener instalado el driver ODBC de Microsoft Access:

1. En Windows, asegúrate de tener instalado Microsoft Access Database Engine:
   - Para 32-bit: [Microsoft Access Database Engine 2010 (32-bit)](https://www.microsoft.com/en-us/download/details.aspx?id=13255)
   - Para 64-bit: [Microsoft Access Database Engine 2016 (64-bit)](https://www.microsoft.com/en-us/download/details.aspx?id=54920)

2. Verifica que el driver esté disponible en el Panel de Control > Herramientas Administrativas > Orígenes de datos ODBC

## Ejecución del script de exploración

Para explorar la estructura de la base de datos SUA:

```
python explorar_bd_sua.py
```

Este script:
1. Conecta a la base de datos SUA.MDB
2. Lista todas las tablas disponibles
3. Explora la estructura de las tablas Patron y Prima_RT
4. Muestra una muestra de datos de estas tablas
5. Guarda los resultados en archivos CSV

## Problemas comunes

### Error de conexión a la base de datos
- Verifica que la ruta C:\Cobranza\SUA\SUA.MDB sea correcta
- Comprueba que los drivers de ODBC están instalados correctamente
- Asegúrate de tener permisos de lectura en el archivo

### Error "Driver not found"
- Asegúrate de estar usando la arquitectura correcta (32-bit vs 64-bit) tanto para Python como para el driver ODBC

### Error al leer los datos
- Algunos caracteres especiales pueden causar problemas de codificación. Prueba con diferentes codificaciones en la función `guardar_resultados_csv` 

## Estructura de Tablas Importantes

### Tabla Movtos
Esta tabla almacena los movimientos de los asegurados. Algunas claves importantes:

- **TIP_MOVS**: Tipo de movimiento
  - 01: Alta
  - 07: Modificación de salario
  - 15: Inicio crédito Infonavit
  - 16: Modificación crédito Infonavit
  - 17: Suspensión crédito Infonavit

- **CVE_MOVS**: Clave del movimiento
  - A: Alta
  - C: Modificación
  - D: Inicio crédito
  - G: Suspensión

- **Tip_Des**: Tipo de descuento para créditos Infonavit
  - "Cuota Fija": Cantidad fija
  - "Porcentaje": Porcentaje del salario
  - "Factor de Descuento": Factor VSM

### Tabla Asegura
Esta tabla almacena la información principal de los asegurados.

- **TIP_DSC**: Tipo de descuento para créditos Infonavit
  - 1: Porcentaje
  - 2: Cuota Fija
  - 3: Factor de Descuento

## Consideraciones para Créditos Infonavit

Al trabajar con créditos Infonavit, es importante tener en cuenta:

1. **Diferencia en formatos**: La tabla Asegura espera el tipo de descuento como número, mientras que Movtos lo espera como texto.

2. **Manejo de tipos**: Al insertar en Asegura, debes convertir el tipo de descuento:
   ```python
   # Ejemplo de conversión para Asegura
   if tipo_descuento_texto == "Cuota Fija":
       tipo_descuento_num = 2
   elif tipo_descuento_texto == "Porcentaje":
       tipo_descuento_num = 1
   elif tipo_descuento_texto == "Factor de Descuento":
       tipo_descuento_num = 3
   ```

3. **Secuencia de inserción**: Para un asegurado con crédito Infonavit, la secuencia correcta es:
   - Insertar en Asegura (datos principales)
   - Insertar en Afiliacion (datos complementarios)
   - Insertar en Trabs (relación trabajador-patrón)
   - Insertar en Movtos tipo 01 (alta)
   - Insertar en Movtos tipo 15 (inicio de crédito) 

## Implementación de créditos Infonavit

### Proceso de carga
Para cargar un asegurado con crédito Infonavit, el archivo `carga_masiva_asegurados.py` implementa la siguiente lógica:

1. **Detección del crédito**: Se verifica si el asegurado tiene crédito Infonavit comprobando si existe un valor no vacío en el campo `Numero Credito Infonavit` en los datos de entrada.

2. **Tipos de descuento**: El sistema maneja automáticamente la conversión entre texto descriptivo y códigos numéricos para el tipo de descuento:
   - Código 1: Porcentaje
   - Código 2: Cuota Fija (valor por defecto)
   - Código 3: Factor de descuento (VSM - Veces Salario Mínimo)

3. **Conversión automática**:
   ```python
   # Si es un número, usar directamente
   if isinstance(tipo_descuento_original, (int, float)):
       tipo_descuento_codigo = int(tipo_descuento_original)
   # Si es string que contiene un número, convertir
   elif isinstance(tipo_descuento_original, str) and tipo_descuento_original.strip().isdigit():
       tipo_descuento_codigo = int(tipo_descuento_original)
   # Si es un texto descriptivo, convertir a su código numérico
   elif isinstance(tipo_descuento_original, str):
       tipo_lower = tipo_descuento_original.lower().strip()
       if 'porcentaje' in tipo_lower:
           tipo_descuento_codigo = 1
       elif 'cuota' in tipo_lower and 'fija' in tipo_lower:
           tipo_descuento_codigo = 2
       elif any(term in tipo_lower for term in ['factor', 'vsm', 'salario mínimo', 'salario minimo']):
           tipo_descuento_codigo = 3
   ```

4. **Inserción en Movtos**: Después de insertar el movimiento de alta (tipo 01), se inserta un segundo movimiento de tipo 15 (Inicio crédito Infonavit) con los siguientes campos específicos:
   - `Num_Cre`: Número de crédito Infonavit
   - `Val_Des`: Valor del descuento
   - `Tip_Des`: Tipo de descuento (código numérico)
   - `Tab_Dism`: Tabla de disminución (por defecto 0)

5. **Valores por defecto**: Si no se especifica un valor para el descuento, se utiliza 0.0 como valor predeterminado.

### Consideraciones importantes

- Siempre se debe utilizar el código numérico para el tipo de descuento en las consultas SQL, nunca el texto descriptivo.
- Para agregar un nuevo asegurado con crédito Infonavit, asegúrese de que tanto los datos del asegurado como los datos del crédito estén presentes y válidos.
- Si ocurre un error al insertar el registro del crédito Infonavit, el proceso completo se considerará fallido. 