from conectores.conector_sua import ConectorSUA
import traceback

def debug_consulta_sql():
    try:
        # Conectar a la base de datos
        conector = ConectorSUA(ruta_bd=r'C:\Cobranza\SUA\SUA.MDB', password="S5@N52V49")
        if not conector.conectar():
            print("❌ Error al conectar a la base de datos")
            return
        print("✅ Conexión establecida correctamente")
        
        # Datos de prueba
        reg_patr = 'E5352621109'
        num_afil = '04088625456'
        
        # Ejecutar la consulta directamente
        cursor = conector.conn.cursor()
        print("✅ Cursor creado correctamente")
        
        # Probar la consulta sin parámetros (usando # para las cadenas en Access)
        query_sin_parametros = f"""
        SELECT TIP_DSC, VAL_DSC, FEC_DSC, Num_Cre, FEC_BAJ
        FROM Asegura
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'
        """
        print("Ejecutando consulta sin parámetros...")
        print(f"Query: {query_sin_parametros}")
        
        # Probar con comillas dobles para Microsoft Access
        query_comillas_dobles = """
        SELECT TIP_DSC, VAL_DSC, FEC_DSC, Num_Cre, FEC_BAJ
        FROM Asegura
        WHERE REG_PATR = "{0}" AND NUM_AFIL = "{1}"
        """.format(reg_patr, num_afil)
        print(f"Query con comillas dobles: {query_comillas_dobles}")
        
        try:
            cursor.execute(query_comillas_dobles)
            resultado = cursor.fetchone()
            print(f"✅ Consulta con comillas dobles ejecutada correctamente: {resultado}")
        except Exception as e:
            print(f"❌ Error con comillas dobles: {str(e)}")
            
        # Probar con otra sintaxis para Access
        query_access = """
        SELECT TIP_DSC, VAL_DSC, FEC_DSC, Num_Cre, FEC_BAJ
        FROM Asegura
        WHERE REG_PATR = ? AND NUM_AFIL = ?
        """
        print("Ejecutando consulta con parámetros...")
        cursor.execute(query_access, (reg_patr, num_afil))
        resultado = cursor.fetchone()
        print(f"✅ Consulta parametrizada ejecutada correctamente: {resultado}")
        
        conector.desconectar()
    
    except Exception as e:
        print(f"❌ Error general: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_consulta_sql() 