"""
Script para explorar la estructura de la tabla Patron y analizar cómo se almacenan
las fracciones en la base de datos SUA.
"""

import os
import sys
import pandas as pd
from pathlib import Path

# Agregar directorio raíz al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar el conector SUA
from conectores.conector_sua import ConectorSUA

def explorar_estructura_tabla(conector, tabla):
    """Explora y muestra la estructura de una tabla"""
    print(f"\n=== ESTRUCTURA DE LA TABLA {tabla.upper()} ===")
    try:
        # Obtener la estructura de la tabla
        cursor = conector.conn.cursor()
        cursor.execute(f"SELECT TOP 0 * FROM {tabla}")
        
        # Obtener información de columnas
        columnas = []
        for i, column_info in enumerate(cursor.description):
            column_name = column_info[0]
            column_type = cursor.description[i][1].__name__ if cursor.description[i][1] else "Unknown"
            
            # Obtener tamaño y otras propiedades si están disponibles
            size = cursor.description[i][3] if len(cursor.description[i]) > 3 else "N/A"
            precision = cursor.description[i][4] if len(cursor.description[i]) > 4 else "N/A"
            scale = cursor.description[i][5] if len(cursor.description[i]) > 5 else "N/A"
            nullable = cursor.description[i][6] if len(cursor.description[i]) > 6 else "N/A"
            
            columnas.append({
                'Nombre': column_name,
                'Tipo': column_type,
                'Tamaño': size,
                'Precisión': precision,
                'Escala': scale,
                'Nullable': nullable
            })
        
        # Mostrar información de columnas
        print("\nInformación de columnas:")
        for col in columnas:
            print(f"- {col['Nombre']}: Tipo={col['Tipo']}, Tamaño={col['Tamaño']}, Precisión={col['Precisión']}, Escala={col['Escala']}, Nullable={col['Nullable']}")
        
        # Buscar específicamente la columna Fraccion
        fraccion_col = next((col for col in columnas if col['Nombre'].lower() == 'fraccion'), None)
        if fraccion_col:
            print(f"\nDetalle específico de la columna Fraccion:")
            print(f"Tipo: {fraccion_col['Tipo']}")
            print(f"Tamaño: {fraccion_col['Tamaño']}")
        else:
            print("\nNo se encontró la columna Fraccion en esta tabla.")
    
    except Exception as e:
        print(f"Error al obtener estructura de la tabla: {e}")

def obtener_datos_fraccion(conector, tabla, limite=10):
    """Obtiene datos de la columna Fraccion"""
    print(f"\n=== EJEMPLOS DE DATOS EN {tabla.upper()} (CAMPO FRACCION) ===")
    try:
        cursor = conector.conn.cursor()
        query = f"SELECT TOP {limite} REG_PAT, Fraccion, Clase FROM {tabla} WHERE Fraccion IS NOT NULL ORDER BY LEN(Fraccion), Fraccion"
        cursor.execute(query)
        
        rows = cursor.fetchall()
        if not rows:
            print("No se encontraron registros con datos en el campo Fraccion")
            return
        
        # Mostrar datos de ejemplo
        print("\nEjemplos de valores existentes (ordenados por longitud y valor):")
        for i, row in enumerate(rows):
            print(f"{i+1}. Registro: {row.REG_PAT}, Fraccion: '{row.Fraccion}', Clase: '{row.Clase}', Tipo de Fraccion: {type(row.Fraccion).__name__}, Longitud: {len(str(row.Fraccion))}")
        
        # Buscar específicamente registros con fracciones que tengan ceros iniciales
        query_ceros = f"SELECT TOP {limite} REG_PAT, Fraccion, Clase FROM {tabla} WHERE Fraccion LIKE '0%' ORDER BY Fraccion"
        cursor.execute(query_ceros)
        rows_ceros = cursor.fetchall()
        
        if rows_ceros:
            print("\nRegistros con fracciones que tienen ceros iniciales:")
            for i, row in enumerate(rows_ceros):
                print(f"{i+1}. Registro: {row.REG_PAT}, Fraccion: '{row.Fraccion}', Clase: '{row.Clase}', Tipo: {type(row.Fraccion).__name__}")
        else:
            print("\nNo se encontraron registros con fracciones que tengan ceros iniciales.")
            
        # Revisar fracciones de un dígito
        query_digito = f"SELECT TOP {limite} REG_PAT, Fraccion, Clase FROM {tabla} WHERE LEN(Fraccion) = 1 ORDER BY Fraccion"
        cursor.execute(query_digito)
        rows_digito = cursor.fetchall()
        
        if rows_digito:
            print("\nRegistros con fracciones de un solo dígito:")
            for i, row in enumerate(rows_digito):
                print(f"{i+1}. Registro: {row.REG_PAT}, Fraccion: '{row.Fraccion}', Clase: '{row.Clase}', Tipo: {type(row.Fraccion).__name__}")
        else:
            print("\nNo se encontraron registros con fracciones de un solo dígito.")
            
        # Contar fracciones por longitud
        query_count = f"SELECT LEN(Fraccion) AS Longitud, COUNT(*) AS Cantidad FROM {tabla} WHERE Fraccion IS NOT NULL GROUP BY LEN(Fraccion) ORDER BY LEN(Fraccion)"
        cursor.execute(query_count)
        rows_count = cursor.fetchall()
        
        if rows_count:
            print("\nDistribución de fracciones por longitud:")
            for row in rows_count:
                print(f"- Longitud {row.Longitud}: {row.Cantidad} registros")
        
    except Exception as e:
        print(f"Error al obtener datos de fracciones: {e}")

def explorar_tabla_fraccion(conector):
    """Explora la tabla TablaFraccion si existe"""
    print("\n=== TABLA DE FRACCIONES ===")
    try:
        # Verificar si existe la tabla TablaFraccion
        cursor = conector.conn.cursor()
        try:
            cursor.execute("SELECT TOP 1 * FROM TablaFraccion")
            print("La tabla TablaFraccion existe en la base de datos.")
            
            # Obtener todos los registros de la tabla
            cursor.execute("SELECT * FROM TablaFraccion ORDER BY Fraccion")
            rows = cursor.fetchall()
            
            if rows:
                print(f"\nContenido de la tabla TablaFraccion ({len(rows)} registros):")
                for i, row in enumerate(rows[:20]):  # Mostrar solo los primeros 20 registros
                    # Intentar acceder a las columnas más comunes en una tabla de fracciones
                    try:
                        cols = [col[0] for col in cursor.description]
                        values = []
                        for col in cols:
                            value = getattr(row, col)
                            values.append(f"{col}: '{value}'")
                        print(f"{i+1}. {', '.join(values)}")
                    except:
                        # Si no podemos acceder por nombre, mostramos los valores directamente
                        print(f"{i+1}. {row}")
                
                if len(rows) > 20:
                    print(f"... y {len(rows) - 20} registros más.")
            else:
                print("La tabla TablaFraccion existe pero no contiene registros.")
                
        except Exception as e:
            if "Invalid object name 'TablaFraccion'" in str(e) or "no such table" in str(e):
                print("La tabla TablaFraccion no existe en la base de datos.")
            else:
                print(f"Error al explorar TablaFraccion: {e}")
                
        # Buscar otras tablas que puedan contener información de fracciones
        tablas_candidatas = []
        for tabla in conector.listar_tablas():
            if "frac" in tabla.lower():
                tablas_candidatas.append(tabla)
                
        if tablas_candidatas:
            print("\nOtras tablas que podrían contener información de fracciones:")
            for tabla in tablas_candidatas:
                print(f"- {tabla}")
    
    except Exception as e:
        print(f"Error al explorar tabla de fracciones: {e}")

def main():
    """Función principal"""
    # Ruta a la base de datos SUA
    ruta_bd = r'C:\Cobranza\SUA\SUA.MDB'
    if not os.path.exists(ruta_bd):
        ruta_bd = input("Ingrese la ruta a la base de datos SUA: ")
    
    # Conectar a la base de datos
    print(f"Conectando a la base de datos: {ruta_bd}")
    conector = ConectorSUA(ruta_bd=ruta_bd, password="S5@N52V49")
    
    if not conector.conectar():
        print("ERROR: No se pudo conectar a la base de datos")
        return
    
    try:
        # Explorar estructura de la tabla Patron
        explorar_estructura_tabla(conector, "Patron")
        
        # Obtener ejemplos de datos de fracciones
        obtener_datos_fraccion(conector, "Patron", 20)
        
        # Explorar la tabla de fracciones si existe
        explorar_tabla_fraccion(conector)
        
    finally:
        # Cerrar conexión
        if conector.conn:
            conector.desconectar()
            print("\nConexión a la base de datos cerrada.")

if __name__ == "__main__":
    main() 