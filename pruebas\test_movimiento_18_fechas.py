"""
Script de prueba para verificar la validación de fechas en el movimiento tipo 18
(Modificación de Tipo de Descuento) respecto a la fecha del movimiento tipo 15 (inicio de crédito).
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from conectores.conector_sua import ConectorSUA

def crear_excel_prueba_fechas():
    """Crea un archivo Excel con datos de prueba para validación de fechas"""
    
    # Datos de prueba
    # Incluye casos donde la fecha es anterior a la fecha del movimiento tipo 15
    fecha_actual = datetime.now()
    
    datos = [
        # Caso 1: Fecha válida (posterior a la fecha de inicio del crédito)
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': (fecha_actual + timedelta(days=30)).strftime('%d/%m/%Y'),
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': '3',  # 3 = Factor de Descuento
            'VALOR_DESCUENTO': 6.59,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Caso válido - Fecha posterior al inicio del crédito'
        },
        # Caso 2: Otra fecha válida
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': (fecha_actual + timedelta(days=60)).strftime('%d/%m/%Y'),
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': '1',  # 1 = Porcentaje
            'VALOR_DESCUENTO': 20.0,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Caso válido - Fecha posterior al inicio del crédito'
        },
        # Caso 3: Fecha inválida (anterior a la fecha de inicio del crédito)
        {
            'REG_PATR': 'E5352621109',
            'NUM_AFIL': '04088625456',
            'NOMBRE': 'JUAN PEREZ RODRIGUEZ',
            'FECHA_MOVIMIENTO': '01/01/2000',  # Fecha muy antigua (debería ser rechazada)
            'TIPO_MOVIMIENTO': '18',
            'TIPO_DESCUENTO': 'Porcentaje',
            'VALOR_DESCUENTO': 25.0,
            'NUMERO_CREDITO': '1565544777',
            'OBSERVACIONES': 'Caso inválido - Fecha anterior al inicio del crédito'
        }
    ]
    
    # Crear DataFrame
    df = pd.DataFrame(datos)
    
    # Ruta del archivo
    ruta_archivo = os.path.join('pruebas', 'test_movimiento_18_fechas.xlsx')
    
    # Guardar a Excel
    df.to_excel(ruta_archivo, index=False)
    
    print(f"Archivo de prueba creado: {ruta_archivo}")
    return ruta_archivo

def obtener_fecha_inicio_credito(reg_patr, num_afil, conector):
    """Obtiene la fecha del movimiento tipo 15 más reciente (inicio de crédito)"""
    
    query = f"""
    SELECT TOP 1 m.FEC_INIC 
    FROM Movtos m
    WHERE m.REG_PATR = '{reg_patr}' 
    AND m.NUM_AFIL = '{num_afil}' 
    AND m.TIP_MOVS = '15'
    ORDER BY m.FEC_INIC DESC
    """
    
    result = conector.ejecutar_consulta(query)
    
    # Verificar el tipo de resultado
    if isinstance(result, pd.DataFrame) and not result.empty and 'FEC_INIC' in result.columns and result.iloc[0]['FEC_INIC'] is not None:
        fecha = pd.to_datetime(result.iloc[0]['FEC_INIC'])
        print(f"Fecha de inicio del crédito (movimiento tipo 15): {fecha.strftime('%d/%m/%Y')}")
        return fecha
    elif isinstance(result, list) and len(result) > 0 and hasattr(result[0], 'FEC_INIC') and result[0].FEC_INIC is not None:
        # Si el resultado es una lista de objetos
        fecha = pd.to_datetime(result[0].FEC_INIC)
        print(f"Fecha de inicio del crédito (movimiento tipo 15): {fecha.strftime('%d/%m/%Y')}")
        return fecha
    else:
        # Como alternativa, intentar obtener la fecha de inicio del descuento de la tabla Asegura
        query_alt = f"""
        SELECT a.FEC_DSC
        FROM Asegura a
        WHERE a.REG_PATR = '{reg_patr}' AND a.NUM_AFIL = '{num_afil}'
        """
        
        result_alt = conector.ejecutar_consulta(query_alt)
        
        if isinstance(result_alt, pd.DataFrame) and not result_alt.empty and 'FEC_DSC' in result_alt.columns and result_alt.iloc[0]['FEC_DSC'] is not None:
            fecha = pd.to_datetime(result_alt.iloc[0]['FEC_DSC'])
            print(f"No se encontró movimiento tipo 15. Usando fecha de inicio del descuento (Asegura.FEC_DSC): {fecha.strftime('%d/%m/%Y')}")
            return fecha
        elif isinstance(result_alt, list) and len(result_alt) > 0 and hasattr(result_alt[0], 'FEC_DSC') and result_alt[0].FEC_DSC is not None:
            # Si el resultado es una lista de objetos
            fecha = pd.to_datetime(result_alt[0].FEC_DSC)
            print(f"No se encontró movimiento tipo 15. Usando fecha de inicio del descuento (Asegura.FEC_DSC): {fecha.strftime('%d/%m/%Y')}")
            return fecha
        else:
            print("No se encontró fecha de inicio del crédito ni fecha de inicio del descuento")
            return None

def procesar_movimiento_con_validacion(reg_patr, num_afil, fecha, tipo_descuento, valor_descuento, num_credito, conector):
    """Procesa un movimiento tipo 18 con validación de fecha respecto al inicio del crédito"""
    
    print(f"\nProcesando movimiento tipo 18 para {num_afil} con fecha {fecha.strftime('%d/%m/%Y')}...")
    
    # Obtener fecha de inicio del crédito
    fecha_inicio_credito = obtener_fecha_inicio_credito(reg_patr, num_afil, conector)
    
    if fecha_inicio_credito is not None:
        if fecha < fecha_inicio_credito:
            print(f"Error: La fecha de modificación ({fecha.strftime('%d/%m/%Y')}) es anterior a la fecha de inicio del crédito ({fecha_inicio_credito.strftime('%d/%m/%Y')})")
            return False
    
    # Preparar datos del movimiento
    datos_mov = {
        'REG_PATR': reg_patr,
        'NUM_AFIL': num_afil,
        'TIP_MOVS': '18',
        'FEC_INIC': fecha.strftime('%Y-%m-%d'),
        'CVE_MOVS': 'C',
        'Tip_Des': tipo_descuento,
        'Val_Des': float(valor_descuento) if valor_descuento else None,
        'Num_Cre': str(num_credito) if num_credito else None
    }
    
    print(f"Datos de movimiento preparados: {datos_mov}")
    
    # Procesar el movimiento
    resultado = conector.procesar_movimiento(datos_mov)
    
    if resultado:
        print(f"Movimiento tipo 18 procesado correctamente")
    else:
        print(f"Error al procesar el movimiento tipo 18")
        
    return resultado

def ejecutar_prueba_fechas():
    """Ejecuta la prueba de validación de fechas"""
    
    # Crear archivo de prueba
    ruta_archivo = crear_excel_prueba_fechas()
    
    # Conectar a la BD
    conector = ConectorSUA("C:\\Cobranza\\SUA\\SUA.MDB", "S5@N52V49")
    if not conector.conectar():
        print("Error al conectar a la base de datos")
        return
    
    print("\n=== VERIFICACIÓN INICIAL ===")
    reg_patr = 'E5352621109'
    num_afil = '04088625456'
    
    # Obtener fecha de inicio del crédito
    fecha_inicio = obtener_fecha_inicio_credito(reg_patr, num_afil, conector)
    if fecha_inicio is None:
        print("ADVERTENCIA: No se encontró fecha de inicio del crédito ni fecha de descuento. La validación de fechas podría no funcionar correctamente.")
    
    # Leer el archivo de prueba
    df = pd.read_excel(ruta_archivo)
    
    # Variables para contar resultados
    total = len(df)
    exitosos = 0
    fallidos = 0
    
    # Procesar cada registro de prueba
    for idx, row in df.iterrows():
        print(f"\n=== PROCESANDO REGISTRO {idx + 1}/{total} ===")
        fecha = pd.to_datetime(row['FECHA_MOVIMIENTO'], format='%d/%m/%Y')
        
        print(f"Caso de prueba: {row['OBSERVACIONES']}")
        print(f"Fecha de movimiento: {fecha.strftime('%d/%m/%Y')}")
        
        # Procesar el movimiento
        resultado = procesar_movimiento_con_validacion(
            row['REG_PATR'],
            row['NUM_AFIL'],
            fecha,
            row['TIPO_DESCUENTO'],
            row['VALOR_DESCUENTO'],
            row['NUMERO_CREDITO'],
            conector
        )
        
        if resultado:
            exitosos += 1
        else:
            fallidos += 1
    
    # Mostrar resultados
    print("\n=== RESULTADOS DE LA PRUEBA ===")
    print(f"Total de casos: {total}")
    print(f"Exitosos: {exitosos}")
    print(f"Fallidos: {fallidos}")
    
    # Desconectar
    conector.desconectar()
    print("\nPrueba finalizada")

if __name__ == "__main__":
    ejecutar_prueba_fechas() 