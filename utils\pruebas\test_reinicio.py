"""
Script para probar el reinicio de crédito para un asegurado con suspensión activa.
"""

import pandas as pd
from datetime import datetime, timedelta
from conectores.conector_sua import ConectorSUA
import traceback

def main():
    # Fecha futura para el reinicio
    fecha_reinicio = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    # Datos del asegurado
    reg_patr = 'E5352621109'
    num_afil = '04088625456'
    
    print(f"Probando reinicio de crédito con fecha: {fecha_reinicio}")
    
    # Crear una instancia del conector
    conector = ConectorSUA(password="S5@N52V49")
    
    # Conectar a la base de datos
    if not conector.conectar():
        print("Error al conectar a la base de datos SUA")
        return
    
    # Verificar estado actual del asegurado
    try:
        cursor = conector.conn.cursor()
        # Usar f-string para la consulta en lugar de parámetros
        query = f"SELECT FEC_DSC, TIP_DSC, VAL_DSC, Num_Cre FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
        cursor.execute(query)
        row = cursor.fetchone()
        
        if not row or row[0] is None:
            print("❌ El asegurado no tiene una suspensión de crédito activa. No se puede reiniciar.")
            return
        
        fec_dsc = row[0]
        print(f"✅ Estado actual: Crédito suspendido desde {fec_dsc}")
        
        # Probar reinicio de crédito
        datos_reinicio = {
            'REG_PATR': reg_patr,
            'NUM_AFIL': num_afil,
            'FEC_INIC': fecha_reinicio,
            'TIP_MOVS': '17'  # Reinicio de crédito
        }
        
        print("\nProcesando reinicio de crédito...")
        resultado = conector.procesar_movimiento(datos_reinicio)
        
        if resultado:
            print("✅ Reinicio procesado correctamente")
        else:
            print("❌ Error al procesar reinicio")
        
        # Verificar movimientos en la tabla Movtos después del reinicio
        print("\nVerificando movimientos después del reinicio...")
        query = f"""
        SELECT TOP 3 TIP_MOVS, FEC_INIC, Tip_Des, Val_Des, Num_Cre
        FROM Movtos 
        WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}' 
        AND TIP_MOVS IN ('15', '16', '17')
        ORDER BY FEC_INIC DESC
        """
        cursor.execute(query)
        rows = cursor.fetchall()
        
        for i, row in enumerate(rows):
            tipo_texto = {
                '15': 'Inicio de crédito',
                '16': 'Suspensión de crédito',
                '17': 'Reinicio de crédito'
            }.get(row[0], 'Desconocido')
            
            print(f"\nMovimiento {i+1}: {tipo_texto}")
            print(f"  Fecha: {row[1]}")
            print(f"  Tipo de descuento: {row[2]}")
            print(f"  Valor de descuento: {row[3]}")
            print(f"  Número de crédito: {row[4]}")
        
        # Verificar estado en Asegura después del reinicio
        print("\nVerificando estado en Asegura después del reinicio...")
        query = f"SELECT FEC_DSC FROM Asegura WHERE REG_PATR = '{reg_patr}' AND NUM_AFIL = '{num_afil}'"
        cursor.execute(query)
        row = cursor.fetchone()
        
        if row:
            fec_dsc = row[0]
            if fec_dsc is not None:
                print(f"✅ FEC_DSC en Asegura: {fec_dsc} (se mantiene la fecha de suspensión para historial)")
            else:
                print(f"❌ FEC_DSC es NULL después del reinicio (comportamiento no esperado)")
        else:
            print("❌ No se encontró el registro en Asegura")
    
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        traceback.print_exc()
    
    finally:
        # Desconectar
        conector.desconectar()

if __name__ == "__main__":
    main() 