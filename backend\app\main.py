from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import BACKEND_CORS_ORIGINS, API_V1_STR, PROJECT_NAME
from app.api.v1.api import api_router

app = FastAPI(
    title=PROJECT_NAME,
    openapi_url=f"{API_V1_STR}/openapi.json"
)

# Configurar CORS
if BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Incluir los routers de la API
app.include_router(api_router, prefix=API_V1_STR)

@app.get("/")
def root():
    return {"message": "Bienvenido a ToolSUA API"} 